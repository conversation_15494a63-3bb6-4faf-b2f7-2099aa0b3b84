// 编辑模态框样式

.edit-modal-content {
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 16px;
  --padding-end: 16px;
}

.edit-form {
  .form-section {
    margin-bottom: 20px;
    
    .form-item {
      --border-radius: 12px;
      --background: var(--ion-color-light);
      --border-color: var(--ion-color-light-shade);
      --padding-start: 16px;
      --padding-end: 16px;
      --padding-top: 12px;
      --padding-bottom: 12px;
      margin-bottom: 8px;
      border: 1px solid var(--ion-color-light-shade);
      transition: all 0.3s ease;
      
      &:hover {
        --background: rgba(var(--ion-color-primary-rgb), 0.05);
        --border-color: var(--ion-color-primary);
      }
      
      &.error {
        --background: rgba(var(--ion-color-danger-rgb), 0.05);
        --border-color: var(--ion-color-danger);
        border-color: var(--ion-color-danger);
      }
      
      ion-label {
        font-weight: 600;
        margin-bottom: 8px;
        
        .field-icon {
          margin-right: 6px;
          font-size: 16px;
          vertical-align: middle;
        }
        
        .required {
          color: var(--ion-color-danger);
          font-weight: bold;
        }
      }
      
      ion-input, ion-select {
        --padding-start: 0;
        --padding-end: 0;
        font-size: 16px;
        
        &::placeholder {
          color: var(--ion-color-medium);
          opacity: 0.7;
        }
      }
      
      ion-select {
        --placeholder-color: var(--ion-color-medium);
        --placeholder-opacity: 0.7;
      }
    }
    
    .error-message {
      color: var(--ion-color-danger);
      font-size: 12px;
      margin-top: 4px;
      margin-left: 16px;
      display: flex;
      align-items: center;
      
      &::before {
        content: '⚠';
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }
  
  .form-tips {
    background: rgba(var(--ion-color-primary-rgb), 0.1);
    border: 1px solid rgba(var(--ion-color-primary-rgb), 0.2);
    border-radius: 8px;
    padding: 12px 16px;
    margin-top: 20px;
    display: flex;
    align-items: flex-start;
    
    ion-icon {
      margin-right: 8px;
      margin-top: 2px;
      font-size: 16px;
      flex-shrink: 0;
    }
    
    span {
      font-size: 13px;
      line-height: 1.4;
      color: var(--ion-color-medium);
    }
  }
}

.edit-modal-footer {
  .footer-buttons {
    display: flex;
    gap: 8px;
    padding: 12px 16px;
    
    .reset-button,
    .cancel-button {
      flex: 0 0 auto;
      --border-radius: 8px;
      height: 44px;
      
      ion-icon {
        font-size: 16px;
      }
    }
    
    .save-button {
      flex: 1;
      --border-radius: 8px;
      height: 44px;
      font-weight: 600;
      
      ion-icon,
      ion-spinner {
        font-size: 16px;
      }
      
      &:disabled {
        opacity: 0.6;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .edit-modal-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }
  
  .edit-form {
    .form-section {
      margin-bottom: 16px;
      
      .form-item {
        --padding-start: 12px;
        --padding-end: 12px;
        --padding-top: 10px;
        --padding-bottom: 10px;
        
        ion-label {
          .field-icon {
            font-size: 14px;
          }
        }
        
        ion-input, ion-select {
          font-size: 14px;
        }
      }
      
      .error-message {
        font-size: 11px;
        margin-left: 12px;
      }
    }
    
    .form-tips {
      padding: 10px 12px;
      
      ion-icon {
        font-size: 14px;
      }
      
      span {
        font-size: 12px;
      }
    }
  }
  
  .edit-modal-footer {
    .footer-buttons {
      padding: 10px 12px;
      gap: 6px;
      
      .reset-button,
      .cancel-button,
      .save-button {
        height: 40px;
        font-size: 14px;
        
        ion-icon,
        ion-spinner {
          font-size: 14px;
        }
      }
    }
  }
}
