import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod } from '@ngx-resource/core';
import { RequestResult } from '../../@core/base/request-result';
import { Observable } from 'rxjs';

/**
 * 未确认关键点数据模型
 */
export interface UnconfirmedKeyPoint {
  pointCode: string;           // 关键点编码
  bufferRange: number;         // 缓冲范围
  bufferTrans: number;         // 缓冲范围
  pointName: string;           // 关键点名称
  geom: string;               // 几何信息 (GeoJSON格式)
  stakeName: string;          // 桩号名称
  depName: string;            // 部门名称
  isItRaining: string;        // 是否雨天 ("是"|"否")
  depCode: string;            // 部门编码
  stakeCode: string;          // 桩号编码
  inspectionMethod: string;   // 巡检方式 ("巡视"|"巡查")
  longitude?: number;         // 经度 (从geom解析或直接提供)
  latitude?: number;          // 纬度 (从geom解析或直接提供)
  selected?: boolean;         // 前端选中状态
}

/**
 * 关键点查询参数
 */
export interface KeyPointQueryParams {
  depCode?: string;           // 部门编码
  pointName?: string;         // 关键点名称
  inspectionMethod?: string;  // 巡检方式 ("巡视"|"巡查")
  isItRaining?: string;       // 是否雨天 ("是"|"否")
}

/**
 * 确认操作参数
 */
export interface ConfirmParams {
  isOk: 'yes' | 'no';         // 确认结果
  pointCode: string;          // 关键点编码
}

/**
 * 关键点确认服务
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root'
})
export class KeypointConfirmService extends Resource {

  constructor() {
    super();
  }

  /**
   * 查询未确认关键点列表(不分页)
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/point/unconfirmed/list',
    method: ResourceRequestMethod.Get
  })
  getUnconfirmedList!: IResourceMethodObservable<KeyPointQueryParams, RequestResult<UnconfirmedKeyPoint[]>>;

  /**
   * 查询未确认关键点个数
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/point/countUnconfirmedNo',
    method: ResourceRequestMethod.Get
  })
  getUnconfirmedCount!: IResourceMethodObservable<KeyPointQueryParams, RequestResult<string>>;

  /**
   * 关键点审批
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/point/ok',
    method: ResourceRequestMethod.Put
  })
  confirmKeyPoint!: IResourceMethodObservable<ConfirmParams, RequestResult<any>>;
}