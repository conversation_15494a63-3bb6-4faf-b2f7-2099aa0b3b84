# 桩类型搜索功能优化说明

## 优化内容

1. **布局重新设计**：桩名称放在第一行，桩类型和操作按钮放在第二行
2. **重置功能修复**：点击重置后，桩类型选择器的值正确清空
3. **移动端优化**：删除hover效果，优化触摸体验
4. **按钮样式改进**：搜索和重置改为文字按钮

## 修复内容

### 1. 高度优化

**修改文件：** `stake-menu.component.scss`

- 减少搜索容器内边距：`12px 16px` → `8px 16px`
- 缩小搜索行间距：`margin-bottom: 10px` → `8px`
- 调整所有组件高度：从 36-38px 减少到 32px
- 优化字体大小：从 14px 调整为 13px
- 调整高度计算：搜索区域高度从 120px 优化为 90px

### 2. 重置功能修复

**问题根源：** InputSearchComponent 的 `writeValue` 方法只处理 `null` 值，没有处理空字符串

**修改文件：** `input-search.component.ts`

```typescript
// 修复前
writeValue(obj: any): void {
  if (obj === null) {
    this.name = obj;
  }
}

// 修复后
writeValue(obj: any): void {
  if (obj === null || obj === undefined || obj === '') {
    this.name = null;
  }
}
```

**修改文件：** `option-source.component.ts`

```typescript
// 优化 getRadioName 方法
getRadioName(value: any) {
  if (!value || value === null || value === '') {
    return null;
  }
  if (this.list.length > 0) {
    return this.list.find((item: any) => item.value === value)?.name;
  }
  return null;
}

// 优化 initData 方法
initData(data: any) {
  if (this.labelName && this.labelValue) {
    this.transformation(data, this.labelValue, this.labelName);
  } else {
    this.list = data;
  }
  // 只有当radioValue有有效值时才回显
  if (this.radioValue && this.radioValue !== null && this.radioValue !== '') {
    this.onChangeRadio(this.radioValue);
  }
}
```

**修改文件：** `stake-menu.component.ts`

```typescript
// 优化重置方法
onReset(): void {
  this.params = '';
  this.stakeType = '';
  this.isSearching = false;

  // 使用 null 值重置选择器
  if (this.stakeTypeSelector) {
    this.stakeTypeSelector.writeValue(null);
    this.stakeTypeSelector.value = null;
  }

  // 强制触发变更检测
  setTimeout(() => {
    this.cd.detectChanges();
  }, 100);

  // 重新加载数据
  if (this.isPage) {
    this.searchData = new StakeParams();
    this.loadMenuTree(this.pipelineId);
  } else {
    this.items = this.stakeTree;
  }
}

// 优化桩类型变化处理
onStakeTypeChange(value: string): void {
  this.stakeType = value;
  // 只有当值不为空且不为null时才自动触发搜索
  if (value && value !== null && value !== '') {
    this.onSearch();
  }
}
```

### 3. 布局重新设计

**修改文件：** `stake-menu.component.html`

```html
<!-- 新布局 -->
<!-- 第一行：桩名称搜索 -->
<div class="search-row">
  <div class="search-item search-input-item">
    <span class="search-label">桩名称：</span>
    <ion-input
      class="compact-search-input"
      placeholder="请输入桩名称"
      [(ngModel)]="params">
    </ion-input>
  </div>
</div>

<!-- 第二行：桩类型选择和操作按钮 -->
<div class="search-row">
  <div class="search-item stake-type-item">
    <span class="search-label">桩类型：</span>
    <ost-quick-option-select
      #stakeTypeSelector
      [isShowIcon]="false"
      [(ngModel)]="stakeType"
      (ngModelChange)="onStakeTypeChange($event)"
      [labelValue]="'dictValue'"
      [labelName]="'dictValue'"
      interfaceUrl="/work-basic/api/v2/basic/dict/msg/list?dictCode=fcMarkerType"
      placeholder="请选择桩类型"
      [disabled]="loading">
    </ost-quick-option-select>
  </div>
  <div class="action-buttons">
    <span class="search-btn" (click)="onSearch()">搜索</span>
    <span class="reset-btn" (click)="onReset()">重置</span>
  </div>
</div>
```

### 4. 移动端优化样式

**修改文件：** `stake-menu.component.scss`

```scss
// 操作按钮容器 - 无hover效果，适合移动端
.action-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;

  .search-btn, .reset-btn {
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    min-width: 50px;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    white-space: nowrap;
    border: 1px solid;

    // 只保留点击效果，删除hover
    &:active {
      opacity: 0.8;
    }
  }

  .search-btn {
    color: #007bff;
    background-color: #e7f3ff;
    border-color: #007bff;
  }

  .reset-btn {
    color: #666666;
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }
}
```

## 修复效果

### ✅ 高度优化
- 搜索区域更加紧凑，节省屏幕空间
- 所有组件高度统一为 32px，视觉更协调
- 移动端体验更佳

### ✅ 重置功能
- 点击重置后，桩类型选择器正确清空显示
- 避免重置时触发不必要的搜索请求
- 数据状态完全重置

### ✅ 布局优化
- 桩名称搜索放在第一行，更符合用户习惯
- 桩类型选择和操作按钮放在第二行，布局更紧凑
- 搜索和重置改为文字按钮，操作更直观

### ✅ 移动端适配
- 删除所有hover效果，避免移动端误触
- 只保留active点击效果，提供触摸反馈
- 按钮尺寸适合手指触摸操作

## 测试建议

1. **重置功能测试**：
   - 选择桩类型后点击重置，确认选择器显示为空
   - 输入关键词后点击重置，确认输入框清空
   - 重置后确认数据重新加载

2. **搜索功能测试**：
   - 选择桩类型后自动触发搜索
   - 桩类型 + 关键词组合搜索
   - 清空桩类型选择后不触发搜索

3. **UI体验测试**：
   - 确认搜索区域高度合适
   - 确认"选择"文字按钮交互正常
   - 确认移动端触摸体验良好
