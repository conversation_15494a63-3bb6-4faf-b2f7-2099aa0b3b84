import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod } from '@ngx-resource/core';
import { RequestResult } from '../@core/base/request-result';
import { LoginMsg } from './class/auth';

/**
 * 登录服务
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root',
})
export class AuthService extends Resource {

  constructor() { super(); }

  /**
   * 登录
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/app/user/login',
  })
  loginRequest!: IResourceMethodObservable<{ account: string, userPasd: string }, RequestResult<LoginMsg>>;

  /**
   * token验证
   */
  @ResourceAction({
    method: ResourceRequestMethod.Get,
    path: '/work-user/api/v2/userCenter/checkToken',
  })
  tokenValidate!: IResourceMethodObservable<null, RequestResult<any>>;

}
