import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { LoadingController, ModalController } from '@ionic/angular';
import { PageGridResult } from '../@core/base/request-result';
import { NewsInfo, NewsParams } from './class/news';
import { NewsService } from './news.service';
import { Subject } from 'rxjs';
import { DetailsMode } from '../@core/base/environment';
import { NewsDetailComponent } from './news-detail/news-detail.component';
import { takeUntil } from 'rxjs/operators';
import { PageEventService } from '../home/<USER>';
import { ToastService } from '../@core/providers/toast.service';


@Component({
  selector: 'app-news',
  templateUrl: 'news.page.html',
  styleUrls: ['news.page.scss']
})
export class NewsPage implements OnInit, OnDestroy {
  // 分页数据
  newsPageResult: PageGridResult<NewsInfo[]>;
  // 分页列表数据
  newsGridData: NewsInfo[] = [];
  // 搜索条件
  newsParams: NewsParams = new NewsParams();
  // 刷新动画
  loading: any;
  // 下拉刷新事件
  refreshEvent: any;
  // 上拉加载更多
  moreEvent: any;
  // 动画类型
  spinnerType = 'bubbles';
  // 上拉到底
  isShowNoMoreStr = '加载更多...';
  // 是否加载中
  isLoading = false;
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  // 未读消息数量
  unreadCount = 0;
  constructor(
    private loadingCtr: LoadingController, private newsService: NewsService,
    private toastSer: ToastService, private modalCtrl: ModalController,
    private dataEvent: PageEventService,
  ) { }

  ngOnInit(): void {
    this.loadGrid(this.newsParams, this.loadGridSuccess);
  }

  /**
   * 列表加载
   */
  async loadGrid(params: NewsParams, functionName: (arg0: any) => void): Promise<void> {
    this.isLoading = true;
    this.loading = await this.loadingCtr.create({
      message: '数据加载中...',
    });
    await this.loading.present();

    this.newsService.gridByNotice(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe(functionName, this.loadGridError);
  }

  /**
   * 统计未读消息数量
   * @param newsData 消息数据数组
   */
  private countUnreadMessages(newsData: NewsInfo[]): void {
    const unreadMessages = newsData.filter(item => item.readStatus !== '已读');
    this.unreadCount = unreadMessages.length;
    // 通知父组件或其他需要知道未读消息数量的组件
    this.dataEvent.sendData('news', this.unreadCount);
  }

  // 列表加载成功
  loadGridSuccess = (request: PageGridResult<NewsInfo[]>) => {
    const { code, data, msg } = request;
    if (code === 0) {
      const pagerData = data?.records || [];
      this.newsPageResult = request;
      this.newsGridData = pagerData.length > 0 ? pagerData : [];
      // 统计未读消息数量
      this.countUnreadMessages(this.newsGridData);
    } else {
      this.toastSer.presentToast(msg,'danger');
      this.newsGridData = [];
      this.newsPageResult = new PageGridResult();
      this.unreadCount = 0;
    }

    this.closeRefresh();
  }
  // 列表加载失败
  loadGridError = (err: any) => {
    this.closeRefresh();
    this.toastSer.presentToast(err.msg || '加载失败', 'danger');
  }

  /**
   * 关闭刷新
   */
  closeRefresh(): void {
    this.isLoading = false;
    this.loading?.dismiss();
    this.refreshEvent?.target.complete();
    // 完成上拉加载事件
    this.moreEvent?.target.complete();
  }

  /**
   * 下拉刷新
   */
  doRefresh(event): void {
    this.refreshEvent = event;
    this.newsParams = new NewsParams();
    this.loadGrid(this.newsParams, this.loadGridSuccess);
  }

  /**
   * 上拉加载更多
   */
  loadMoreData(event): void {
    const { pageNum, pageSize, total } = this.newsPageResult.data;

    // 判断是否还有更多数据
    if (total > (pageNum * pageSize)) {
      this.moreEvent = event;
      this.newsParams.pageNum = pageNum + 1;
      this.loadGrid(this.newsParams, this.moreGridSuccess);
    } else {
      // 没有更多数据时显示提示
      this.spinnerType = null;
      this.isShowNoMoreStr = '到底啦~';
      setTimeout(() => {
        event.target.complete();
      }, 1000);
    }
  }

  /**
   * 加载更多数据成功处理
   * @param result 加载结果
   */
  moreGridSuccess = (result: PageGridResult<NewsInfo[]>) => {
    this.newsPageResult = result;
    const newRecords = result.data.records || [];
    // 合并新加载的数据到现有列表中
    this.newsGridData = [...this.newsGridData, ...newRecords];
    // 统计所有数据中的未读消息数量
    this.countUnreadMessages(this.newsGridData);
    this.closeRefresh();
  }

  /**
   * 详情跳转
   */
  async onNewsDetail(item): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: NewsDetailComponent,
      componentProps: { newsInfo: item, modelMode: DetailsMode.SEE },
      cssClass: 'app-news-detail',
      swipeToClose: true,
    });
    await modal.present();
    modal.onDidDismiss().then(() => {
      this.loadGrid(this.newsParams, this.loadGridSuccess);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
