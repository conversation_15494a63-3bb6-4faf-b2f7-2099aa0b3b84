import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ShareModule } from '../share/share.module';
import { MonitorDetailComponent } from './detail/detail.component';
import { MonitorPageRoutingModule } from './monitor-routing.module';
import { MonitorPage } from './monitor.page';

const COMPONENT: any[] = [
  MonitorDetailComponent, // 实时监控人员详情
];
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ShareModule,
    MonitorPageRoutingModule
  ],
  entryComponents: [...COMPONENT],
  declarations: [MonitorPage, ...COMPONENT]
})
export class MonitorPageModule {}
