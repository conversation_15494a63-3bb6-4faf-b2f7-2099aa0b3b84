import { Injectable } from '@angular/core';
import { ModalController } from '@ionic/angular';

export type ModalType = 'video' | 'evReport' | 'keyPoint' | 'points' | 'punchIn';

export interface ModalState {
  video: boolean;
  evReport: boolean;
  keyPoint: boolean;
  points: boolean;
  punchIn: boolean;
}

@Injectable({
  providedIn: 'root'
})
// 模态框管理服务
export class ModalManagerService {
  private modalStates: ModalState = {
    video: false,
    evReport: false,
    keyPoint: false,
    points: false,
    punchIn: false
  };

  constructor(private modalCtrl: ModalController) { }

  /**
   * 获取模态框状态
   */
  getModalStates(): ModalState {
    return { ...this.modalStates };
  }

  /**
   * 检查指定模态框是否正在显示
   * @param modalType 模态框类型
   */
  isModalOpen(modalType: ModalType): boolean {
    return this.modalStates[modalType];
  }

  /**
   * 设置模态框状态
   * @param modalType 模态框类型
   * @param isOpen 是否打开
   */
  setModalState(modalType: ModalType, isOpen: boolean): void {
    this.modalStates[modalType] = isOpen;
  }

  /**
   * 创建模态框并防止重复打开
   * @param modalType 模态框类型
   * @param modalConfig 模态框配置
   * @param callback 模态框关闭后的回调
   */
  async createModalWithGuard(
    modalType: ModalType,
    modalConfig: any,
    callback?: (data: any, role: string) => void
  ): Promise<void> {
    // 防止重复打开
    if (this.modalStates[modalType]) {
      return;
    }

    this.modalStates[modalType] = true;

    try {
      const modal = await this.modalCtrl.create(modalConfig);
      await modal.present();

      const { data, role } = await modal.onWillDismiss();

      if (callback) {
        callback(data, role);
      }
    } finally {
      this.modalStates[modalType] = false;
    }
  }

  /**
   * 关闭所有模态框
   */
  async closeAllModals(): Promise<void> {
    const elements = await this.modalCtrl.getTop();
    if (elements) {
      await this.modalCtrl.dismiss();
    }
  }

  /**
   * 关闭指定类型的模态框
   * @param modalType 模态框类型
   */
  async closeModal(modalType: ModalType): Promise<void> {
    if (this.modalStates[modalType]) {
      const topModal = await this.modalCtrl.getTop();
      if (topModal) {
        await this.modalCtrl.dismiss();
      }
      this.modalStates[modalType] = false;
    }
  }

  /**
   * 重置所有模态框状态
   */
  resetModalStates(): void {
    this.modalStates = {
      video: false,
      evReport: false,
      keyPoint: false,
      points: false,
      punchIn: false
    };
  }
} 