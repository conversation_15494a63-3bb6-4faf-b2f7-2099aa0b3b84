import { Injectable } from '@angular/core';
import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { NavController } from '@ionic/angular';

@Injectable()
export class ResponseHandlerInterceptor implements HttpInterceptor {
  constructor(
    private router: Router,
    private nav: NavController,
    // private toastSer: ToastService
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          this.handleBusinessLogic(event);
        }
      }),
      catchError(error => {
        // 在这里可以处理网络错误或未被捕获的HTTP状态码错误
        console.error('全局HTTP错误处理器捕获到错误:', error);
        // 例如，可以显示一个通用的网络错误提示
        // this.toastSer.error('网络连接错误，请稍后重试');
        return throwError(() => error);
      })
    );
  }

  private handleBusinessLogic(event: HttpResponse<any>): void {
    const body = event.body;
    if (!body || typeof body.code === 'undefined') {
      return; // 如果响应体不存在或没有code字段，则不处理
    }

    const { code, msg } = body;
    
    // 根据code处理重定向逻辑
    if (code === 7000 || code === 9990) {
      this.nav.navigateRoot('/auth');
      return;
    }
    
    if (code === 8006 && msg === '密码已过期') {
      this.router.navigate(['/editPassword'], { queryParams: { isModal: false } });
      return;
    }
    
    // 处理其他业务错误（非0的code）
    if (code !== 0) {
       console.warn(`业务请求失败: Code=${code}, Msg=${msg}, URL=${event.url}`);
       // 在这里可以调用全局的Toast服务来显示错误信息
       // this.toastSer.error(msg || '操作失败');
    }
  }
}