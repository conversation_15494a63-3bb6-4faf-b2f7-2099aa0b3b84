ion-content {
  height: calc(80vh - 48px - 38px);
  --background: #f4f5f8;
}

ion-list {
  padding-top: 0px;
  margin-bottom: 0px;
  background: transparent;
}

// 使列表项更紧凑
ion-item {
  // 减少项目内部的垂直填充
  --inner-padding-top: 8px;
  --inner-padding-bottom: 8px;

  // 减少项目的最小高度
  --min-height: 60px;

  ion-label {
    margin-top: 0;
    margin-bottom: 0;
  }

  h2 {
    font-size: 0.875rem;
    margin-bottom: 4px;
  }

  p {
    font-size: 0.75rem;
    line-height: 1.2;
  }
}

// 调整缩略图大小以适应紧凑视图
ion-thumbnail {
  width: 50px;
  height: 50px;
  margin-inline-end: 12px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
  }
}

// 图片数量标签样式
.image-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--ion-color-primary, #3880ff);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

// 自定义页脚样式
.footer-content {
  width: 100%;
  height: 38px;
  background-color: var(--ion-color-primary, #3880ff);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;

  // 为点击/触摸提供反馈
  &:active {
    background-color: var(--ion-color-primary-shade, #3171e0);
  }
}