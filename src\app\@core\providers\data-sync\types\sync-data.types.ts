/**
 * 业务数据类型枚举
 * 用于区分不同业务场景下的本地缓存与上传逻辑
 */
export enum SyncDataType {
  /**
   * 用户手动打卡（页面：clock-in.component.ts）
   */
  MANUAL_CLOCK_IN = 'manualClockIn',

  /**
   * 系统自动关键点打卡（服务：key-point.service.ts）
   */
  AUTO_KEY_POINT_CLOCK_IN = 'autoKeyPointClockIn',

  /**
   * 未巡检原因上报（页面：not-inspected.component.ts）
   */
  NOT_INSPECTED_REASON = 'notInspectedReason',

  /**
   * 巡检事件上报（如表单、事件等，缓存键 inspection_data）
   */
  INSPECTION_REPORT = 'inspectionReport'
}

/**
 * 各业务类型上传格式配置
 * payloadType: 'array' 表示接口要求数组上传，'object' 表示接口要求对象上传
 * 如需扩展新业务类型，按需添加配置项
 */
export const SyncDataTypeConfig = {
  [SyncDataType.MANUAL_CLOCK_IN]: { payloadType: 'array' },
  [SyncDataType.AUTO_KEY_POINT_CLOCK_IN]: { payloadType: 'array' },
  [SyncDataType.NOT_INSPECTED_REASON]: { payloadType: 'object' },
  [SyncDataType.INSPECTION_REPORT]: { payloadType: 'array' }
};

/**
 * 本地缓存数据结构
 * @property id         唯一标识（建议用时间戳+业务主键拼接，确保全局唯一）
 * @property type       业务类型（SyncDataType）
 * @property data       业务原始数据（如打卡参数、巡检表单等）
 * @property timestamp  缓存时间（毫秒时间戳）
 * @property retryCount 当前重试次数（用于失败重试控制）
 * @property uploadUrl  上传接口地址（每类业务可配置不同接口）
 * @property method     请求方式
 */
export interface SyncCacheItem {
  id: string;
  type: SyncDataType;
  data: any;
  timestamp: number;
  retryCount: number;
  uploadUrl: string;
  method?: string;
}

/**
 * 上传任务结构
 */
export interface UploadTask {
  type: SyncDataType;
  url: string;
  items: SyncCacheItem[];
}

/**
 * 添加缓存的返回结果
 */
export type AddToCacheResult = 
  | { status: 'uploaded', code: number, msg: string } 
  | { status: 'cached' };

/**
 * 失败数据结构
 */
export interface FailedDataGroup {
  type: SyncDataType;
  items: SyncCacheItem[];
}
