$ost-layout-item-config: (
  top-left: (),
  top-center: (
    justify-content: center,
  ),
  top-right: (
    flex-direction: column,
    align-items: flex-end,
  ),
  right-center: (
    justify-content: flex-end,
    align-items: center,
  ),
  right-bottom: (
    justify-content: flex-end,
    align-items: flex-end,
  ),
  bottom-center: (
    justify-content: center,
    align-items: flex-end,
  ),
  bottom-left: (
    align-items: flex-end,
  ),
  left-center: (
    align-items: center,
  ),
  center: (
    align-items: center,
    justify-content: center,
  ),
);

.ost-layout-item {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  touch-action: none;
  display: flex;

  @each $type in top-left, top-center, top-right, right-center, right-bottom,
    bottom-center, bottom-left, left-center,center
  {
    &.ost-layout-item-#{$type} {
      @each $key, $value in map-get($ost-layout-item-config, $type) {
        #{$key}: $value;
      }
    }
  }
}
