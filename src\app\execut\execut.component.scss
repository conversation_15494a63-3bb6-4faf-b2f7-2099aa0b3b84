ost-map {
  width: 100%;
  height: 100%;
}

.close-btn {
  margin-top: 50px;
}

.help {
  font-size: 36px;
  margin: 16px;
  color: #4d80cf;
  pointer-events: all;
}

.sunny-toggle {
  display: flex;
  align-items: center;
  margin-top: 16px;
  pointer-events: all;
}

.video-monitor-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 16px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  pointer-events: all;
  min-width: 32px;
  text-align: center;
  line-height: 1.2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  div {
    margin: 0;
    padding: 0;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    background-color: rgba(200, 200, 200, 0.7);
  }
}

.punch-in-fab {
  position: absolute;
  right: 12px;
  bottom: 220px;
  width: 56px;
  height: 56px;
  background: #21c97a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(33,201,122,0.2);
  color: #fff;
  font-size: 32px;
  z-index: 1000;
  cursor: pointer;
  transition: box-shadow 0.2s;
  pointer-events: all;
}