import { Component, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { NewsInfo } from '../class/news';
import { NewsService } from '../news.service';

@Component({
  selector: 'app-news-detail',
  templateUrl: './news-detail.component.html',
  styleUrls: ['./news-detail.component.scss'],
})
export class NewsDetailComponent implements OnInit, OnDestroy {
  @Input() newsInfo: NewsInfo = new NewsInfo();
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    private modalCtrl: ModalController, private newsService: NewsService,
  ) { }

  ngOnInit(): void {
    this.getDetail();
  }

  /**
   * 获取详情
   */
  getDetail(): void {
    this.newsService.update({ receiveId: this.newsInfo.receiveId })
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => { });
  }

  /**
   * 回退
   */
  goBack(): void { this.modalCtrl.dismiss(); }
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.goBack();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

}
