import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON>vent, HttpHand<PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class TimestampInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    const timestamp = Date.now();
    let urlWithReplayTime = req.url;

    if (urlWithReplayTime.includes('?')) {
      urlWithReplayTime += `&messageReplayTime=${timestamp}`;
    } else {
      urlWithReplayTime += `?messageReplayTime=${timestamp}`;
    }
    
    const newReq = req.clone({
      url: urlWithReplayTime
    });

    return next.handle(newReq);
  }
}