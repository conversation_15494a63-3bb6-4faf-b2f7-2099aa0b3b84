.outerdiv {
  position: fixed;
  display: flex;
  flex-direction: column;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 2;
  width: 100%;
  height: 100%;
  padding: 8px;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;

  .closepic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .closepic::before,
  .closepic::after {
    position: absolute;
    content: '';
    background-color: #fff;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 16px;
  }

  .closepic::before {
    transform: translate(-50%, -50%) rotate(45deg);
  }

  .closepic::after {
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  .closepic {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  .closepic:active {
    background-color: rgba(255, 255, 255, 0.5);
  }

  .clear-btn-float {
    position: absolute;
    bottom: 16px;
    right: 16px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(4px);
    z-index: 10;

    ion-icon {
      font-size: 20px;
      color: #fff;
    }
  }

  .clear-btn-float:active {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(0.95);
  }

}

.outerdiv.card-mode {
  padding: 4px;
}

.outerdiv .closepic.card-mode {
  width: 24px;
  height: 24px;
}

.outerdiv .closepic.card-mode::before,
.outerdiv .closepic.card-mode::after {
  height: 12px;
}

.outerdiv .clear-btn-float.card-mode {
  bottom: 0px;
  right: 2px;
  width: 36px;
  height: 36px;

  ion-icon {
    font-size: 16px;
  }
}

.header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 50px;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.header-row .title {
  flex: 1;
  font-size: 18px;
  color: #fff;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.header-row .closepic {
  flex-shrink: 0;
  margin-left: 8px;
}

.img-container {
  position: relative;
  width: 100%;
  flex: 1;
  min-height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* 确保图片缩放时不会超出容器边界 */
  contain: layout style paint;
}

.bigimg {
  max-width: 95%;
  max-height: 95%;
  object-fit: contain;
  /* 默认不使用过渡效果，避免干扰手势操作 */
  transition: none;
  /* 优化渲染性能 */
  will-change: transform;
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 在拖拽时确保没有过渡效果 */
  &.dragging {
    transition: none;
  }
  /* 在缩放时确保没有过渡效果 */
  &.pinching {
    transition: none;
  }
  /* 重置时使用平滑过渡 */
  &.resetting {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

.outerdiv.card-mode .header-row {
  min-height: 30px;
  margin-bottom: 0px;
  padding: 6px;

  .title {
    font-size: 14px;
  }
}

.outerdiv.card-mode .img-container {
  flex: 1;
  min-height: 200px;
}

.outerdiv.card-mode .bigimg {
  max-width: 98%;
  max-height: 98%;
  margin-bottom: 10px;
}