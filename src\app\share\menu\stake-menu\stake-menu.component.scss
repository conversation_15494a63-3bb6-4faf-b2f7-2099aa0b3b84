// 容器样式
.stake-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 紧凑搜索容器样式
.compact-search-container {
  position: sticky;
  top: 0;
  background-color: #ffffff;
  border-bottom: 1px solid #e9ecef;
  z-index: 100;
  flex-shrink: 0;
  padding: 8px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  // 搜索行样式
  .search-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    // 搜索项样式
    .search-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-right: 10px;

      .search-label {
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        margin-right: 8px;
        min-width: 60px;
        flex-shrink: 0;
      }

      // 桩类型选择项样式
      &.stake-type-item {
        flex: 1;
        margin-right: 8px;

        .search-label {
          min-width: 60px;
          color: #495057;
          font-weight: 400;
          font-size: 13px;
        }

        ost-quick-option-select {
          --margin-inline-start: 0px !important;
          flex: 1;
          min-width: 0;
          width: 100%;

          ::ng-deep .input-search {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            height: 32px;

            ion-input {
              font-size: 13px;
              text-align: left;
              --color: #495057;
              --placeholder-color: #999999;
              --padding-start: 6px;
              --padding-end: 6px;
            }
          }
        }
      }

      // 关键词输入框特殊样式
      &.search-input-item {
        .compact-search-input {
          flex: 1;
          --padding-start: 10px;
          --padding-end: 10px;
          --color: #495057;
          --placeholder-color: #999999;
          font-size: 13px;
          height: 32px;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          background-color: #f8f9fa;

          &:focus-within {
            border-color: #007bff;
            background-color: #ffffff;
          }
        }
      }
    }

    // 操作按钮容器
    .action-buttons {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      .search-btn,
      .reset-btn {
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 4px;
        min-width: 50px;
        min-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none;
        white-space: nowrap;
        border: 1px solid;

        &:active {
          opacity: 0.8;
        }
      }

      .search-btn {
        color: #007bff;
        background-color: #e7f3ff;
        border-color: #007bff;
      }

      .reset-btn {
        color: #666666;
        background-color: #f8f9fa;
        border-color: #e9ecef;
      }
    }
  }
}

// 可滚动内容区域样式
.content-scrollable {
  flex: 1;
  position: relative;

  // 重置 ion-content 的默认样式
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    ion-spinner {
      width: 32px;
      height: 32px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }
}

// 无数据状态样式
.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}
