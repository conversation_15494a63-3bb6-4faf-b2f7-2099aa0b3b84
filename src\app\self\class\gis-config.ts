export class GeolocationConfig {
    desiredAccuracy: number; // 期望准确度
    stationaryRadius: number; // 静止半径 默认50
    distanceFilter: number; // 最小间距 默认500
    stopOnTerminate: boolean; // 是否随程序强制停止 默认【是：true】
    startOnBoot: boolean; // 在设备启动时启动后台服务。 默认【否：false】
    interval: number; // 位置更新之间的最小时间间隔（毫秒） 默认60000
    fastestInterval: number; // 应用程序处理位置更新的最快速度（以毫秒为单位）。默认120000
    startForeground: boolean; // 前台显示通知 默认【否：false】
    notificationTitle: string; // 通知标题
    notificationText: string; // 通知文本
    notificationIconColor: string; // 图标颜色 #4CAF50
    pauseLocationUpdates: string; // 暂停应用程序时暂停位置更新 默认【否：false】
    maxLocations: number; // 限制存储到db中的最大位置数。 默认10000
}

