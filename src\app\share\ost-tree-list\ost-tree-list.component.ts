import { AfterViewInit, Component, DoCheck, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { OstTreeLisstInternalService, OstTreeListBag, OstTreeListItem, OstTreeListService } from './ost-tree-list.service';
import { filter, map, takeWhile } from 'rxjs/operators';


export enum ToggleStates {
  Expanded = 'expanded',
  Collapsed = 'collapsed',
}

@Component({
  selector: '[TreeListItem]',
  templateUrl: './ost-tree-list-item.component.html',
  animations: [trigger('toggle', [
    state(ToggleStates.Collapsed, style({ height: '0', margin: '0' })),
    state(ToggleStates.Expanded, style({ height: '*' })),
    transition(`${ToggleStates.Collapsed} <=> ${ToggleStates.Expanded}`,
      animate(300))
  ])],
})
export class OstTreeComponent implements DoCheck, AfterViewInit, OnDestroy {
  @Input() listItem: OstTreeListItem = new OstTreeListItem();
  // 是否显示 目录按钮
  @Input() showDirBtn = false;
  // 菜单文本
  @Input() dirBtnText: string;
  // 是否显示 菜单按钮
  @Input() showSubBtn = false;
  @Input() showSubIcon = false;
  // 菜单文本
  @Input() subBtnText: string;
  @Output() toggleSubItem = new EventEmitter<any>();
  @Output() selectItem = new EventEmitter<any>();
  @Output() itemClick = new EventEmitter<any>();
  @Output() subItemClick = new EventEmitter<any>();
  @Output() itemBtnClick = new EventEmitter<any>();

  protected alive = true;
  toggleState: ToggleStates;

  constructor(protected ostTreeListService: OstTreeListService) { }

  ngDoCheck(): void {
    this.toggleState = this.listItem.expanded ? ToggleStates.Expanded : ToggleStates.Collapsed;
  }

  ngAfterViewInit(): void {
    this.dataInit();
  }
  dataInit(): void {
    this.ostTreeListService.onSubmenuToggle()
      .pipe(
        takeWhile(() => this.alive),
        filter(({ item }) => item === this.listItem),
        map(({ item }: OstTreeListBag) => item.expanded),
      )
      .subscribe(isExpanded => this.toggleState = isExpanded ? ToggleStates.Expanded : ToggleStates.Collapsed);
  }

  ngOnDestroy(): void {
    this.alive = false;
  }

  getSuffix(fileName: string): any {
    const image = ['bmp', 'jpg', 'png', 'tif', 'gif'];
    const suffix = fileName.substring(fileName.lastIndexOf('.') + 1);
    return image.includes(suffix);
  }

  onToggleSubMenu(item: OstTreeListItem): void {
    this.toggleSubItem.emit(item);
  }

  onSelectItem(item: OstTreeListItem): void {
    this.selectItem.emit(item);
  }

  onItemClick(item: OstTreeListItem): void {
    this.itemClick.emit(item);
  }

  onSubItemClick(item: OstTreeListItem): void {
    this.subItemClick.emit(item);
  }

  onItemBtnClick(item: OstTreeListItem): void {
    this.itemBtnClick.emit(item);
  }
  getExpandStateIcon(): string {
    if (this.listItem.expanded) {
      return 'caret-down-outline';
    }
    return 'caret-forward-outline';
  }

}

@Component({
  selector: 'ost-tree-list',
  templateUrl: './ost-tree-list.component.html',
  styleUrls: ['./ost-tree-list.component.scss']
})
export class OstTreeListComponent implements OnInit, OnChanges {
  // 是否显示 目录按钮
  @Input() showDirBtn = false;
  // 菜单文本
  @Input() dirBtnText: string;
  @Input() showSubBtn = false;
  @Input() subBtnText: string;
  @Input() showSubIcon = false;
  @Input() items: OstTreeListItem[];
  @Input() tag: string;
  @Input() hidenEdit: string;
  @Output() toggleSubMenu = new EventEmitter<any>();
  @Output() selectItem = new EventEmitter<any>();
  @Output() itemClick = new EventEmitter<any>();
  @Output() subItemClick = new EventEmitter<any>();
  @Output() itemBtnClick = new EventEmitter<any>();

  constructor(public menuInternalService: OstTreeLisstInternalService) { }

  ngOnInit(): void {
  }

  protected collapseAll(): void {
    this.menuInternalService.collapseAll(this.items, this.tag);
  }

  setSelectItem(item: OstTreeListItem): void {
    this.menuInternalService.selectItem(item, this.items, false, this.tag);
  }

  ngOnChanges(changes: SimpleChanges): void {
    const items = changes.items;
    if (items && items.currentValue) {
      this.menuInternalService.prepareItems(this.items);
    }
  }

  onToggleSubMenu(item: OstTreeListItem): void {
    item.expanded = !item.expanded;
    this.menuInternalService.submenuToggle(item, this.tag);
    this.menuInternalService.selectItem(item, this.items, false, this.tag);
    this.toggleSubMenu.emit(item);
  }

  onItemClick(item: OstTreeListItem): void {
    this.items.forEach(i => i.selected = false);
    item.selected = true;
    this.menuInternalService.itemClick(item, this.tag);
    this.menuInternalService.selectItem(item, this.items, false, this.tag);
    this.itemClick.emit(item);
  }
  onSubItemClick(item: OstTreeListItem): void {
    this.subItemClick.emit(item);
  }
  onItemBtnClick(item: OstTreeListItem): void {
    this.itemBtnClick.emit(item);
  }
}
