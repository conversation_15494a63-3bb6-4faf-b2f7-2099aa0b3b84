import flvjs from 'flv.js';

export class VideoPlayerService {
  private flvPlayer: any = null;
  private currentUrl: string | null = null;
  private videoElement: HTMLVideoElement | null = null;
  // 添加一个私有属性来存储错误回调函数
  private _onErrorCallback: ((message: string) => void) | null = null;
  private isDestroying: boolean = false;
  private retryCount: number = 0;
  private maxRetries: number = 3;
  private retryTimeout: any = null;

  // 初始化FLV播放器
  initFlvPlayer(videoElement: HTMLVideoElement, videoUrl: string): void {
    if (!videoElement || !videoUrl) {
      console.warn('无效的视频元素或地址');
      // 如果参数无效，也通过回调报告错误
      if (this._onErrorCallback) {
        this._onErrorCallback('视频元素或地址无效。');
      }
      return;
    }

    // 如果已初始化并且是同一个流地址，避免重复初始化
    if (this.isPlayerInitialized() && this.currentUrl === videoUrl) {
      console.log('FLV播放器已初始化，无需重复创建');
      return;
    }

    // 检查浏览器是否支持FLV播放
    if (flvjs.isSupported()) {
      // 如果已存在播放器实例，先销毁它以避免资源泄露
      this.destroyPlayer();

      this.videoElement = videoElement;
      this.isDestroying = false;
      this.retryCount = 0; // 重置重试计数

      this.createFlvPlayer(videoUrl);
    } else {
      console.error('您的浏览器不支持 FLV 播放');
      if (this._onErrorCallback) {
        this._onErrorCallback('您的浏览器不支持 FLV 播放。');
      }
    }
  }

  // 创建FLV播放器实例
  private createFlvPlayer(videoUrl: string): void {
    try {
      const flvConfig = {
        type: 'flv',
        url: videoUrl,
        isLive: true,
        hasAudio: true,
        hasVideo: true,
        enableStashBuffer: false,
        stashInitialSize: 128,
        enableWorker: true,
        lazyLoad: true,
        seekType: 'range',
        // 增加更严格的错误处理配置
        cors: true,
        withCredentials: false,
        // 增加超时配置
        timeout: 10000,
        // 增加重试配置
        retryCount: 0, // 我们手动处理重试
        retryDelay: 1000
      } as flvjs.MediaDataSource;

      // 创建FLV播放器实例
      this.flvPlayer = flvjs.createPlayer(flvConfig);
      
      // 将播放器绑定到视频元素
      this.flvPlayer.attachMediaElement(this.videoElement!);

      // **改进的错误事件监听器**
      this.flvPlayer.on(flvjs.Events.ERROR, (errorType: any, errorDetail: any, errorInfo: any) => {
        // 如果正在销毁中，忽略错误
        if (this.isDestroying) {
          return;
        }

        let errorMessage = '视频播放器发生未知错误。';
        console.error('Flv.js 播放器错误:', errorType, errorDetail, errorInfo);

        // **关键改动：使用类型断言**
        if (errorType === (flvjs.ErrorTypes as any).NETWORK) {
          if (errorDetail === (flvjs.ErrorDetails as any).NETWORK_STATUS_CODE_INVALID) {
            if (errorInfo && errorInfo.code) {
              errorMessage = `视频请求失败：HTTP 状态码 ${errorInfo.code}。`;
              if (errorInfo.code === 400) {
                errorMessage = '视频地址请求失败：400 Bad Request。请检查视频地址是否正确或有效。';
              }
            } else {
              errorMessage = '视频网络请求返回无效状态码。';
            }
          } else if (errorDetail === (flvjs.ErrorDetails as any).NETWORK_EXCEPTION) {
            errorMessage = '视频网络连接异常或超时。';
          } else if (errorDetail === (flvjs.ErrorDetails as any).NETWORK_TIMEOUT) {
            errorMessage = '视频加载超时。';
          }
        } else if (errorType === (flvjs.ErrorTypes as any).MEDIA) {
          if (errorDetail === (flvjs.ErrorDetails as any).MEDIA_FORMAT_UNSUPPORTED) {
            errorMessage = '视频格式不支持。';
          } else if (errorDetail === (flvjs.ErrorDetails as any).MEDIA_CODEC_UNSUPPORTED) {
            errorMessage = '视频编解码器不支持。';
          } else {
            errorMessage = '视频媒体播放错误。';
          }
        }

        // 处理MediaSource错误
        if (errorDetail === 'MediaMSEError' || errorInfo?.msg?.includes('SourceBuffer')) {
          errorMessage = '视频流数据异常，正在尝试重新连接...';
          this.handleMediaSourceError(videoUrl);
          return; // 不立即报告错误，而是尝试重试
        }

        // 通过回调函数将错误信息传递出去
        if (this._onErrorCallback) {
          this._onErrorCallback(errorMessage);
        }

        // 错误发生后，重置播放器状态
        this.resetPlayerState();
      });

      // 添加加载完成事件监听
      this.flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {
        console.log('FLV播放器加载完成');
        this.retryCount = 0; // 加载成功时重置重试计数
      });

      // 加载视频流
      this.flvPlayer.load();
      
      // 播放视频
      this.flvPlayer.play().catch(err => {
        // 如果正在销毁中，忽略错误
        if (this.isDestroying) {
          return;
        }

        // play() 方法的 catch 捕获的是 Promise 拒绝，通常是用户手势问题或浏览器限制
        console.error('FLV 播放失败 (play() Promise 拒绝):', err);
        // 如果是用户未交互导致，可能不需要显示为"错误"
        // 但如果是其他原因，可以考虑报告
        if (this._onErrorCallback) {
          this._onErrorCallback('视频自动播放被阻止或播放失败。');
        }
      });

      this.currentUrl = videoUrl;
    } catch (error) {
      console.error('创建FLV播放器失败:', error);
      if (this._onErrorCallback) {
        this._onErrorCallback('创建视频播放器失败。');
      }
    }
  }

  // 处理MediaSource错误
  private handleMediaSourceError(videoUrl: string): void {
    if (this.retryCount >= this.maxRetries) {
      console.error('达到最大重试次数，停止重试');
      if (this._onErrorCallback) {
        this._onErrorCallback('视频流连接失败，已达到最大重试次数。');
      }
      this.resetPlayerState();
      return;
    }

    this.retryCount++;
    console.log(`MediaSource错误，第${this.retryCount}次重试...`);

    // 清除之前的重试定时器
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }

    // 延迟重试，避免立即重试
    this.retryTimeout = setTimeout(() => {
      if (!this.isDestroying && this.videoElement) {
        console.log('开始重试创建FLV播放器...');
        this.createFlvPlayer(videoUrl);
      }
    }, 2000 * this.retryCount); // 递增延迟时间
  }

  // 销毁FLV播放器
  destroyPlayer(): void {
    this.isDestroying = true;
    
    // 清除重试定时器
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
    
    if (this.flvPlayer) {
      try {
        this.flvPlayer.destroy();
      } catch (e) {
        console.warn('销毁 FLV 播放器时发生错误：', e);
      }
      this.flvPlayer = null;
    }
    
    this.currentUrl = null;
    this.videoElement = null;
    this.isDestroying = false;
    this.retryCount = 0;
  }

  // 检查播放器是否已初始化
  isPlayerInitialized(): boolean {
    return !!this.flvPlayer && !this.isDestroying;
  }

  // 重置播放器状态
  private resetPlayerState(): void {
    this.currentUrl = null;
    this.flvPlayer = null;
    this.retryCount = 0;
  }

  // **新增方法：设置错误回调函数**
  public setOnErrorCallback(callback: (message: string) => void): void {
    this._onErrorCallback = callback;
  }

  // 获取当前URL
  public getCurrentUrl(): string | null {
    return this.currentUrl;
  }

  // 新增方法：手动重试
  public retry(): void {
    if (this.currentUrl && this.videoElement && !this.isDestroying) {
      console.log('手动重试视频播放器...');
      this.retryCount = 0; // 重置重试计数
      this.createFlvPlayer(this.currentUrl);
    }
  }
}