import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ShareModule } from '../share/share.module';
import { WorkPageRoutingModule } from './work-routing.module';
import { WorkPage } from './work.page';
import { TaskDetailComponent } from './detail/detail.component';
import { PlaybackPage } from './playback/playback.page';
import { TrackControlBarPage } from './playback/track-control-bar/track-control-bar.page';

const PAGE: any[] = [
  TaskDetailComponent,
  PlaybackPage,
  TrackControlBarPage,
];
@NgModule({
  imports: [
    IonicModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ShareModule,
    WorkPageRoutingModule,
  ],
  entryComponents: [...PAGE],
  declarations: [WorkPage, ...PAGE]
})
export class WorkPageModule { }
