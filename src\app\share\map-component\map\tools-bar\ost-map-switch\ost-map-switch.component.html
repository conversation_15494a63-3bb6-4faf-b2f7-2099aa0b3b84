<div class="switch-layout">
  <!-- 头部 -->
  <div class="switch-header">
    <div class="header-item start">图层选择器</div>
    <div class="header-item end">
      <ion-icon name="close-outline" (click)="onClose()"></ion-icon>
    </div>
  </div>
  <!-- 内容 -->
  <div class="switch-content">
    <!-- 表格 -->
    <ul class="content-grid">
      <li
        *ngFor="let item of baseLayerList.getArray()"
        (click)="onMapServe(item)"
        class="grid-item {{ item.get('selected') ? 'active' : '' }}"
      >
        <img [src]="item.get('icon')" />
        <span>{{ item.get("name") }}</span>
      </li>
    </ul>
    <!-- 业务图层切换 -->
    <ul class="content-grid">
      <li class="layer-checked" *ngFor="let item of businessLayerList.getArray()">
        <ion-item lines="none">
          <ion-checkbox
          slot="start"
          [ngModel]="item.get('selected')"
          (ngModelChange)="onChangeBusiness($event, item)"></ion-checkbox>
          <ion-label>{{item.get("name")}}</ion-label>
        </ion-item>
        <div class="range">
          <div><span>透明度：</span></div>
          <ion-range min="0" max="100"  pin="true" [ngModel]="item.get('opacity')*100" (ngModelChange)="opacityChange($event, item)" >
            <ion-label slot="start">0</ion-label>
            <ion-label slot="end">100</ion-label>
          </ion-range>
        </div>
      </li>
    </ul>
  </div>
</div>
