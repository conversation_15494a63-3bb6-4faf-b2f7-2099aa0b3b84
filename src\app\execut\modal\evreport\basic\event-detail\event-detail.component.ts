import { DatePipe } from '@angular/common';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { ControlContainer, FormBuilder, FormGroupDirective, Validators } from '@angular/forms';
import { DetailsMode } from 'src/app/@core/base/environment';

@Component({
  selector: 'app-event-detail',
  templateUrl: './event-detail.component.html',
  styleUrls: ['./event-detail.component.scss'],
  viewProviders: [
    {
      provide: ControlContainer,
      useExisting: FormGroupDirective
    }
  ]
})
export class EventDetailComponent implements OnInit, OnChanges {
  // 事件类型
  @Input() eventType: string;
  @Input() modelMode: DetailsMode;
  @Input() initialValues: any;
  @Input() pipelineId: string;
  DetailsMode: typeof DetailsMode = DetailsMode;

  // 表单字段配置
  private formControlsConfig = {
    '管道占压': [
      { name: 'stakeCode', initialValue: '', validators: [Validators.required] },
      { name: 'stakeOffset', initialValue: 0 },
      { name: 'pressorSubstance', initialValue: '', validators: [Validators.required] },
      { name: 'pressorType', initialValue: '', validators: [Validators.required] },
      { name: 'pressorUnit', initialValue: '' },
      { name: 'pressorSubstancePlace', initialValue: '' },
      { name: 'pipeCentreDistance', initialValue: 0, }
    ],
    '设备失效': [
      { name: 'stationCode', initialValue: '', validators: [Validators.required] },
      { name: 'deviceName', initialValue: '', validators: [Validators.required] },
      { name: 'deviceTypeCode', initialValue: '', validators: [Validators.required] },
      { name: 'expireDate', initialValue: '', validators: [Validators.required], type: 'date' },
      { name: 'emergncyLevel', initialValue: '', validators: [Validators.required] },
      { name: 'currentStatus', initialValue: '', validators: [Validators.required] },
      { name: 'expireDesc', initialValue: '' },
      { name: 'expireReason', initialValue: '' }
    ],
    '第三方施工': [
      { name: 'beginStakeCode', initialValue: '', validators: [Validators.required] },
      { name: 'endStakeCode', initialValue: '', validators: [Validators.required] },
      { name: 'beginOffset', initialValue: 0 },
      { name: 'endOffset', initialValue: 0 },
      { name: 'construcType', initialValue: '', validators: [Validators.required] },
      { name: 'threatLevel', initialValue: '', validators: [Validators.required] },
      { name: 'constructionUnit', initialValue: '' },
      { name: 'dutyPerson', initialValue: '' },
      { name: 'dutypersonPhone', initialValue: '' },
      { name: 'projectName', initialValue: '' },
    ],
    '隐患上报': [
      { name: 'stakeCode', initialValue: '', validators: [Validators.required] },
      { name: 'stakeOffset', initialValue: 0 },
      { name: 'hiddenDangerType', initialValue: '', validators: [Validators.required] },
      { name: 'severityLevel', initialValue: '', validators: [Validators.required] },
      { name: 'scopeOfInfluence', initialValue: '' },
      { name: 'hiddenDangerDesc', initialValue: '' },
      { name: 'processStatus', initialValue: '', validators: [Validators.required] },
      { name: 'processPeopleCode', initialValue: '' },
      { name: 'handlingMeasures', initialValue: '' }
    ]
  };

  // 获取所有可能的控件名
  private get allControlNames(): string[] {
    // 收集所有控件名称
    const allNames: string[] = [];

    // 遍历每种事件类型的控件配置
    Object.values(this.formControlsConfig).forEach(controls => {
      controls.forEach(control => {
        if (allNames.indexOf(control.name) === -1) {
          allNames.push(control.name);
        }
      });
    });

    return allNames;
  }

  constructor(
    private fb: FormBuilder, private datePipe: DatePipe,
    private parentForm: FormGroupDirective
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.eventType || changes.initialValues) {
      this.initForm();
    }

    // 当管线ID改变时，清空桩号相关字段
    if (changes.pipelineId && !changes.pipelineId.firstChange) {
      const parentForm = this.parentForm.form;
      const stakeFields = ['stakeCode', 'beginStakeCode', 'endStakeCode'];

      stakeFields.forEach(field => {
        if (parentForm.contains(field)) {
          parentForm.get(field).setValue('');
        }
      });
    }
  }

  ngOnInit() {
    this.initForm();
  }

  private initForm() {
    const parentForm = this.parentForm.form;
    const values = this.initialValues || {};

    // 移除所有可能的控件
    this.allControlNames.forEach(controlName => {
      if (parentForm.contains(controlName)) {
        parentForm.removeControl(controlName);
      }
    });

    // 根据事件类型添加对应的控件
    if (this.formControlsConfig[this.eventType]) {
      this.formControlsConfig[this.eventType].forEach(control => {
        let initialValue = values[control.name] !== undefined ? values[control.name] : control.initialValue;

        // 针对 type: 'date' 字段，若无初始值则赋当前日期
        if (control.type === 'date' && (!initialValue || initialValue === '')) {
          initialValue = this.dateTransform(new Date(), 'yyyy-MM-dd');
        }

        parentForm.addControl(control.name, this.fb.control(initialValue, control.validators || []));
      });
    }
  }

  /**
   * 时间转换器
   */
  dateTransform(date: any, format: string = 'yyyy-MM-dd'): any {
    return this.datePipe.transform(date ? new Date(date) : new Date(), format);
  }

  ngOnDestroy() {
    const parentForm = this.parentForm.form;

    // 移除所有添加的控件
    this.allControlNames.forEach(controlName => {
      if (parentForm.contains(controlName)) {
        parentForm.removeControl(controlName);
      }
    });
  }
}

