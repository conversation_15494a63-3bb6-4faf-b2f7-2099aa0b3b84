import { Injectable } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { filter, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class EventBusService {
  // 多播对象
  private subject$ = new Subject<EmitEvent>();
  /**
   * 从下游 根据条件 遍历 回调 对象
   * @param event 拦截事件
   * @param action 响应操作意图
   */
  on(event: any, action: any): Subscription {
    return this.subject$
      .pipe(
        filter((e: EmitEvent) => e.name === event),
        map((e: EmitEvent) => e.value),
      )
      .subscribe(action);
  }

  /**
   * 发送一个事件到上游
   *
   */
  emit(event: EmitEvent): void {
    this.subject$.next(event);
  }
}
/**
 * 事件载体
 */
export class EmitEvent {
  constructor(public name: any, public value?: any) { }
}
