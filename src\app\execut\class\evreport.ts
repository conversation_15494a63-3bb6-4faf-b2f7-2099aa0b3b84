import { PagingModule } from "src/app/@core/base/request-result";

/**
 * 事件上报
 */
export class EvReportInfo {
    /**
     * 完成修复时间
     */
    completeRepairDate: string;
    /**
     * 设备状态
     */
    currentStatus: string;
    /**
     * 部门名称
     */
    depName: string;
    depCode: string;
    /**
     * 设备编号
     */
    deviceName: string;
    /**
     * 事件描述
     */
    eventDesc: string;
    /**
     * 事件状态
     */
    eventStatus: string;
    /**
     * 事件类型
     */
    eventType: string;
    /**
     * 失效日期
     */
    expireDate: string;
    /**
     * 失效描述
     */
    expireDesc: string;
    /**
     * 失效原因
     */
    expireReason: string;
    /**
     * 处理措施
     */
    handlingMeasures: string;
    /**
     * 管线名称
     */
    pipelineCode: string;
    pipelineName: string;
    /**
     * 事件编码
     */
    eventCode: string = '';
    /**
     * 事项解决情况
     */
    eventSolve: string;
    /**
     * 事件名称
     */
    eventName: string;
    /**
     * 事项发生地点
     */
    eventPlace: string;
    /**
     * 事件上报时间
     */
    createTime: string;
    /**
     * 处理时间
     */
    processTime: string;
    /**
     * 处理人员编码
     */
    processUserCode: string;
    /**
     * 处理措施
     */
    processMeasure: string;
    /**
     * 现场照片
     */
    picCode: any = [];
    /**
     * 上报人员姓名
     */
    reportUserName: string;
    /**
     * 上报人员编码
     */
    reportUserCode: string;

    /**
     * ================= 管道占压 =================
     */
    /**
     * 桩号编码
     */
    stakeCode: string;
    /**
     * 桩号名称
     */
    stakeName: string;
    /**
     * 桩偏移量
     */
    stakeOffset: string;
    /**
     * 占压物
     */
    pressorSubstance: string;
    /**
     * 占压方式
     */
    pressorType: string;
    /**
     * 管道中心距离
     */
    pipeCentreDistance: string;
    /**
     * 占压物归属单位
     */
    pressorUnit: string;
    /**
     * 占压物所属县、乡、村
     */
    pressorSubstancePlace: string;

    /**
     * ================= 设备失效 =================
     */
    /**
     * 站场编码
     */
    stationCode: string;
    /**
     * 站场名称
     */
    stationName: string;
    /**
     * 设备类型编码
     */
    deviceTypeCode: string;
    /**
     * 设备类型名称
     */
    deviceTypeName: string;
    /**
     * 紧急级别
     */
    emergncyLevel: string;

    /**
     * ================= 第三方施工 =================
     */
    /**
     * 起始桩编码
     */
    beginStakeCode: string;
    /**
     * 起始桩名称
     */
    beginStakeName: string;
    /**
     * 终止桩编码
     */
    endStakeCode: string;
    /**
     * 终止桩名称
     */
    endStakeName: string;
    /**
     * 起始偏移量
     */
    beginOffset: string;
    /**
     * 终止偏移量
     */
    endOffset: string;
    /**
     * 施工类型
     */
    construcType: string;
    /**
     * 隐患等级
     */
    threatLevel: string;
    /**
     * 施工项目名称
     */
    projectName: string;
    /**
     * 施工单位
     */
    constructionUnit: string;
    /**
     * 施工联系人
     */
    dutyPerson: string;
    /**
     * 联系方式
     */
    dutypersonPhone: string;

    /**
     * ================= 隐患上报 =================
     */
    /**
     * 隐患类型
     */
    hiddenDangerType: string;
    /**
     * 隐患严重程度
     */
    severityLevel: string;
    /**
     * 影响范围
     */
    scopeOfInfluence: string;
    /**
     * 隐患描述
     */
    hiddenDangerDesc: string;
    /**
     * 处理状态
     */
    processStatus: string;
    /**
     * 处理人编码
     */
    processPeopleCode: string;
    /**
     * 处理人名称
     */
    processPeopleName: string;


    /**
     * 节点信息列表
     */
    scheduleList: any;
    /**
     * 按钮状态 是否可修改  默认可修改  false：不可修改
     */
    btnStatus: boolean = true;
    /**
     * 事件坐标
     */
    eventGeom: any;
    geom: any
}

export class EventParams extends PagingModule<any> {
    eventType = '';
    eventName = '';
    createTime = '';
    startTime = '';
    endTime = '';
}


export class LogInfo {
    /**
     * id
     */
    scheduleCode: string;
    /**
     * 事件编码
     */
    eventCode: string;
    /**
     * 事件类型
     */
    eventType: string;
    /**
     * 人员名称
     */
    userName: string;
    /**
     * 文件编码
     */
    fileCodes: any = [];
    /**
     * 描述
     */
    describe: string;
    /**
     * 状态 未完成 、已完成
     */
    status: string = '未完成';
}