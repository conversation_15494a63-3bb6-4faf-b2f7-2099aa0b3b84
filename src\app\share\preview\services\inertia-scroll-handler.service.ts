import { Injectable } from '@angular/core';
import { VelocitySample, InertiaState, GestureConfig, BoundaryResult } from './image-gesture.model';

/**
 * 惯性滑动处理器
 * 负责处理拖拽结束后的惯性滑动效果
 */
@Injectable({
  providedIn: 'root'
})
export class InertiaScrollHandlerService {
  // 惯性滑动相关
  private inertia: InertiaState = {
    animationId: null,
    velocityTracker: [],
    maxTrackerSize: 5 // 保留最近5个速度样本
  };

  // 当前速度
  private velocityX = 0;
  private velocityY = 0;
  private isInertiaScrolling = false;

  /**
   * 添加速度样本到追踪器
   */
  addVelocitySample(deltaX: number, deltaY: number): void {
    const now = Date.now();
    this.inertia.velocityTracker.push({
      x: deltaX,
      y: deltaY,
      time: now
    });

    // 保持追踪器大小
    if (this.inertia.velocityTracker.length > this.inertia.maxTrackerSize) {
      this.inertia.velocityTracker.shift();
    }
  }

  /**
   * 计算当前速度（优化版）
   */
  private calculateVelocity(): { x: number; y: number } {
    const tracker = this.inertia.velocityTracker;
    if (tracker.length < 2) {
      return { x: 0, y: 0 };
    }

    // 只使用最近的2个样本，避免累积误差
    const recent = tracker.slice(-2);
    const timeDiff = recent[1].time - recent[0].time;

    if (timeDiff <= 0 || timeDiff > 100) { // 忽略时间间隔过大的样本
      return { x: 0, y: 0 };
    }

    // 计算瞬时速度，并限制最大速度
    const velocityX = Math.max(-5, Math.min(5, recent[1].x / timeDiff));
    const velocityY = Math.max(-5, Math.min(5, recent[1].y / timeDiff));

    return { x: velocityX, y: velocityY };
  }

  /**
   * 开始惯性滑动
   */
  startInertiaScroll(
    config: GestureConfig,
    onUpdate: (x: number, y: number) => void,
    getCurrentPosition: () => { x: number; y: number },
    getBounds: () => BoundaryResult | null,
    currentScale: number = 1
  ): void {
    if (!config.inertiaEnabled) {
      return;
    }

    const velocity = this.calculateVelocity();
    // 根据缩放级别动态调整最小速度阈值，放大时需要更大的速度才启动惯性
    const scaleAdjustedMinVelocity = (config.inertiaMinVelocity || 1.0) * Math.max(1, currentScale * 0.5);

    // 检查速度是否足够启动惯性滑动
    if (Math.abs(velocity.x) < scaleAdjustedMinVelocity && Math.abs(velocity.y) < scaleAdjustedMinVelocity) {
      return;
    }

    // 根据缩放级别调整速度倍数，放大时显著减少惯性
    const velocityMultiplier = Math.max(5, 20 / Math.max(1, currentScale));

    this.velocityX = velocity.x * velocityMultiplier;
    this.velocityY = velocity.y * velocityMultiplier;
    this.isInertiaScrolling = true;

    this.performInertiaScroll(config, onUpdate, getCurrentPosition, getBounds);
  }

  /**
   * 执行惯性滑动动画
   */
  private performInertiaScroll(
    config: GestureConfig,
    onUpdate: (x: number, y: number) => void,
    getCurrentPosition: () => { x: number; y: number },
    getBounds: () => BoundaryResult | null
  ): void {
    if (!this.isInertiaScrolling) {
      return;
    }

    const friction = config.inertiaFriction || 0.95;
    const minVelocity = config.inertiaMinVelocity || 0.5;

    // 应用摩擦力
    this.velocityX *= friction;
    this.velocityY *= friction;

    // 检查速度是否太小，停止惯性滑动
    if (Math.abs(this.velocityX) < minVelocity && Math.abs(this.velocityY) < minVelocity) {
      this.stopInertiaScroll();
      return;
    }

    // 计算新位置
    const currentPos = getCurrentPosition();
    const newX = currentPos.x + this.velocityX;
    const newY = currentPos.y + this.velocityY;

    // 应用边界约束
    const bounds = getBounds();
    if (bounds) {
      // 如果碰到边界，停止对应方向的惯性滑动
      if (newX <= bounds.minTranslateX || newX >= bounds.maxTranslateX) {
        this.velocityX = 0;
      }
      if (newY <= bounds.minTranslateY || newY >= bounds.maxTranslateY) {
        this.velocityY = 0;
      }

      // 约束位置
      const constrainedX = Math.max(bounds.minTranslateX, Math.min(bounds.maxTranslateX, newX));
      const constrainedY = Math.max(bounds.minTranslateY, Math.min(bounds.maxTranslateY, newY));
      onUpdate(constrainedX, constrainedY);
    } else {
      onUpdate(newX, newY);
    }

    // 继续动画
    this.inertia.animationId = requestAnimationFrame(() => 
      this.performInertiaScroll(config, onUpdate, getCurrentPosition, getBounds)
    );
  }

  /**
   * 停止惯性滑动
   */
  stopInertiaScroll(): void {
    this.isInertiaScrolling = false;
    this.velocityX = 0;
    this.velocityY = 0;

    if (this.inertia.animationId) {
      cancelAnimationFrame(this.inertia.animationId);
      this.inertia.animationId = null;
    }
  }

  /**
   * 清空速度追踪器
   */
  clearVelocityTracker(): void {
    this.inertia.velocityTracker = [];
  }

  /**
   * 检查是否正在惯性滑动
   */
  isScrolling(): boolean {
    return this.isInertiaScrolling;
  }

  /**
   * 获取当前速度
   */
  getCurrentVelocity(): { x: number; y: number } {
    return { x: this.velocityX, y: this.velocityY };
  }

  /**
   * 获取速度追踪器状态
   */
  getTrackerStatus(): {
    sampleCount: number;
    latestSample: VelocitySample | null;
  } {
    return {
      sampleCount: this.inertia.velocityTracker.length,
      latestSample: this.inertia.velocityTracker.length > 0 
        ? this.inertia.velocityTracker[this.inertia.velocityTracker.length - 1] 
        : null
    };
  }

  /**
   * 重置惯性滑动处理器
   */
  reset(): void {
    this.stopInertiaScroll();
    this.clearVelocityTracker();
  }
}
