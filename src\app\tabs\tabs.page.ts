import { Component, OnDestroy, OnInit } from '@angular/core';
import { NewsService } from '../news/news.service';
import { NewsParams } from '../news/class/news';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { PageEventService } from '../home/<USER>';
import { NetworkService } from '../@core/providers/network.service';

@Component({
  selector: 'app-tabs',
  templateUrl: 'tabs.page.html',
  styleUrls: ['tabs.page.scss']
})
export class TabsPage implements OnInit, OnDestroy {
  tabList = [
    { id: 'home', name: '首页', icon: 'home-outline' },
    { id: 'work', name: '工作', icon: 'newspaper-outline' },
    { id: 'news', name: '消息', icon: 'chatbubble-ellipses-outline' },
    { id: 'self', name: '个人', icon: 'person-outline' },
  ];
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  // 未读消息数量
  unreadCount = 0;

  constructor(
    private newsSer: NewsService,
    private dataEvent: PageEventService,
    private networkService: NetworkService,
  ) {
    if (this.networkService.isOffline()) {
      // 如果没有网络连接
      this.tabList = [
        { id: 'workbench', name: '工作台', icon: 'grid-outline' },
        { id: 'self', name: '个人', icon: 'person-outline' },
      ];
    }
  }
  ngOnInit(): void {
    this.getNewsLength();
    // 监听消息数量变化
    this.dataEvent.receiveDataForPageType('news')
      .pipe(takeUntil(this.destroy$))
      .subscribe((dataInfo: {pageType: string, data: number}) => {
        this.unreadCount = dataInfo.data;
      });
  }

  /**
   * 获取未读消息数量
   */
  getNewsLength() {
    const params = new NewsParams();
    this.newsSer.gridByNotice(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe(result => {
        if (result.code === 0) {
          const newsInfo = result.data.records;
          let unreadCount = newsInfo.reduce((accumulator, currentMessage) => {
            return accumulator + (currentMessage.readStatus === "未读" ? 1 : 0);
          }, 0);
          this.unreadCount = unreadCount;
        }
      });
  }

  // 回收
  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

}
