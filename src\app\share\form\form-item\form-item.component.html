<div [ngClass]="{ 'dividing-line': divLine }" #ostFormItem>
  <ion-item-divider [ngClass]="{'textarea-vertical-layout': isShowTextarea}">
    <ng-content select="ion-label"></ng-content>
    <ng-content select="ion-input"></ng-content>
    <ng-content select="ion-select"></ng-content>
    <ng-content select="ion-datetime"></ng-content>
    <ng-content select="ion-textarea"></ng-content>
    <ng-content select="ion-toggle"></ng-content>
    <ng-content select="ost-input-search"></ng-content>
    <ng-content select="ost-quick-option-select"></ng-content>
    <ng-content select="ion-icon"></ng-content>
    <ng-container *ngIf="required">
      <ion-text
        slot="end" style="margin: 0;"
        [ngClass]="{ 'ion-hide': modelMode === DetailsMode.SEE }"
        >*
      </ion-text>
    </ng-container>
  </ion-item-divider>
  <ng-content select="ost-error"></ng-content>
</div>
