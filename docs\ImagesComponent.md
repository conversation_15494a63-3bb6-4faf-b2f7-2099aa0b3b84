# ImagesComponent 组件说明文档

## 组件简介
`ImagesComponent` 是一个用于移动端/混合应用场景下的图片采集、压缩、上传、预览与管理的通用组件。支持相机/相册选择、图片压缩与水印、上传状态管理、最大数量限制、与父组件的数据同步等功能。适用于如巡检、报修、现场采集等业务场景。

## 主要功能
- 支持相机/相册选择图片（集成水印处理）
- 图片压缩，提升上传效率
- 支持 base64 与 fileCode 两种上传模式
- 图片上传状态管理（上传中、成功、失败）
- 图片预览与删除
- 最大上传数量限制
- 与父组件的数据/状态同步

## 输入属性（@Input）
| 属性名         | 类型                        | 说明                         |
| -------------- | --------------------------- | ---------------------------- |
| label          | string                      | 图片区域标题，默认“现场照片” |
| modelMode      | DetailsMode                 | 详情模式控制                  |
| fileCodes      | string[]                    | 已上传图片 fileCode 列表      |
| uploadMode     | 'base64' \| 'fileCode'     | 上传模式，默认 'fileCode'    |
| base64Images   | string[]                    | base64图片数组（base64模式）  |
| maxCount       | number                      | 最大图片数量，默认不限制      |
| cameraOnly     | boolean                     | 是否仅允许相机，默认 false    |

## 输出属性（@Output）
| 属性名              | 类型           | 说明                                 |
| ------------------- | --------------| ------------------------------------ |
| fileCodesChange     | EventEmitter  | fileCodes 变更事件                   |
| uploadStatusChange  | EventEmitter  | 上传状态变化事件（是否有上传中图片） |
| base64ImagesChange  | EventEmitter  | base64图片变更事件                   |

## 依赖服务
- `ImagePickerService`：负责图片选择（相机/相册）、水印处理等
- `ToastService`：消息提示
- `ShareModuleService`：图片上传、文件获取等
- `compressImage`、`getBase64WithPrefix`：图片处理工具函数

## 主要方法说明
- `presentActionSheet()`：弹出图片选择入口，调用 ImagePickerService 进行图片采集
- `combination(imageData: string)`：图片压缩、状态管理、上传等处理
- `onDelete(index: number, emit: boolean)`：删除图片
- `previewAvatar(file: ImageFile)`：图片预览

## 典型用法示例
```html
<ost-images
  [label]="'现场照片'"
  [fileCodes]="fileCodes"
  [uploadMode]="'fileCode'"
  [maxCount]="5"
  (fileCodesChange)="onFileCodesChange($event)"
  (uploadStatusChange)="onUploadStatusChange($event)"
></ost-images>
```

## 维护与扩展建议
- 推荐将图片上传、图片处理等通用逻辑持续抽离为服务，保持组件“瘦身”
- 如需支持更多图片来源（如文件、微信小程序等），可扩展 ImagePickerService
- 组件样式、UI 可根据业务需求自定义
- 保持输入输出属性的单向数据流，便于父组件统一管理

---
如需进一步定制或扩展，请参考项目最佳实践或联系维护者。 