/**
 * 图片手势状态接口
 */
export interface ImageGestureState {
  scale: number;
  lastScale: number;
  translateX: number;
  translateY: number;
  lastTranslateX: number;
  lastTranslateY: number;
  isDragging: boolean;
  // 惯性滑动相关状态
  velocityX: number;
  velocityY: number;
  isInertiaScrolling: boolean;
}

/**
 * 手势配置接口
 */
export interface GestureConfig {
  minScale?: number;
  maxScale?: number;
  dampingFactor?: number;
  resetAnimationDuration?: number;
  minDragThreshold?: number; // 最小拖拽阈值，确保即使图片很小也能拖拽
  minVisibleRatio?: number; // 图片最小可见区域比例，默认0.25（25%）
  // 惯性滑动配置
  inertiaEnabled?: boolean; // 是否启用惯性滑动，默认true
  inertiaFriction?: number; // 惯性摩擦系数，默认0.95
  inertiaMinVelocity?: number; // 最小惯性速度阈值，默认0.5
  inertiaMaxDuration?: number; // 最大惯性滑动时间，默认1000ms
}

/**
 * 速度追踪样本
 */
export interface VelocitySample {
  x: number;
  y: number;
  time: number;
}

/**
 * 边界计算结果
 */
export interface BoundaryResult {
  minTranslateX: number;
  maxTranslateX: number;
  minTranslateY: number;
  maxTranslateY: number;
  debug?: {
    containerSize: { width: number; height: number };
    scaledSize: { width: number; height: number };
    minVisibleSize: { width: number; height: number };
    minVisibleRatio: number;
  };
}

/**
 * 图片尺寸信息
 */
export interface ImageDimensions {
  containerRect: DOMRect;
  imgRect: DOMRect;
  originalWidth: number;
  originalHeight: number;
  scaledWidth: number;
  scaledHeight: number;
}

/**
 * 性能缓存接口
 */
export interface PerformanceCache {
  containerRect: DOMRect | null;
  imageRect: DOMRect | null;
  lastTime: number;
  duration: number;
}

/**
 * 节流控制接口
 */
export interface ThrottleControl {
  lastTime: number;
  interval: number;
}

/**
 * 惯性滑动状态
 */
export interface InertiaState {
  animationId: number | null;
  velocityTracker: VelocitySample[];
  maxTrackerSize: number;
}