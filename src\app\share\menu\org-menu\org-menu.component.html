<ion-header [translucent]="true">
  <div class="search-bar" *ngIf="showSearch">
    <div class="search-bar-parent">
      <ion-icon class="login-form-input-icon" name="search-outline"></ion-icon>
      <ion-input
        placeholder="请输入查询字段"
        [(ngModel)]="params"
      >
      </ion-input>
    </div>
    <ion-note slot="end" class="title-end-operate" style="padding-right: 60px;" (click)="onReset()">
      重置
    </ion-note>
    <ion-note slot="end" class="title-end-operate" style="padding-right:10px;" (click)="onSearch()">
      搜索
    </ion-note>
  </div>
</ion-header>
  <ion-text color="warning" class="tip" *ngIf="leafOnly">
    <ion-icon name="warning"></ion-icon>仅跟节点可选
  </ion-text>
<div
  *ngIf="loading"
  style="height: 50px; text-align: center; line-height: 50px;"
>数据加载中...</div>
<div *ngIf="items.length<=0 && !loading;" class="no-data">
  <img src="assets/menu/box2.png" style="padding-top: 50px;" />
  <!-- 暂无数据 -->
  <span class="no-data-span">暂无数据</span>
</div>
<ost-tree-list 
  *ngIf="items.length > 0"
  [style.height.px]="screenHeight"
  style="overflow-y: auto;"
  #menu
  [items]="items"
  [showDirBtn]="showDirBtn"
  [dirBtnText]="dirBtnText"
  [showSubBtn]="showSubBtn"
  [subBtnText]="subBtnText"
  (toggleSubMenu)="onToggleSubMenu($event)"
  (itemClick)="onItemClick($event)"
  (subItemClick)="onSubItemClick($event)"
>
</ost-tree-list>