import { AfterViewInit, Directive, ElementRef, EventEmitter, Input, NgZone, OnDestroy, Output, Renderer2 } from '@angular/core';
import { fromEvent, interval, merge, Observable, of, Subject } from 'rxjs';
import { filter, map, switchMap, take, takeLast, takeUntil, takeWhile, tap } from 'rxjs/operators';

@Directive({
  selector: '[ngPress]',
  exportAs: 'ngPress'
})
export class OnPressDirective implements AfterViewInit, OnDestroy {
  @Output() ngPress: EventEmitter<number> = new EventEmitter();
  @Output() ngRelease: EventEmitter<number> = new EventEmitter();
  @Output() ngPressing: EventEmitter<number> = new EventEmitter();

  @Input() start = 0;
  @Input() step = 100;

  hasPrese = false;
  private destroy$ = new Subject<void>();

  constructor(public ele: ElementRef, public ngZone: NgZone, public render: Renderer2) { }



  ngAfterViewInit(): void {
    this.ngZone.runOutsideAngular(() => {
      const touchStart$ = onTouchStart(this.ele.nativeElement);
      const touchEnd$ = onTouchEnd(this.ele.nativeElement);
      const touchCancel$ = onTouchCancel(this.ele.nativeElement);

      this.ngRelease.pipe(
        takeUntil(this.destroy$)
      ).subscribe(() => {
        this.render.addClass(this.ele.nativeElement, 'ng-press-disabled');
        setTimeout(() => {
          this.hasPrese = false;
          this.render.removeClass(this.ele.nativeElement, 'ng-press-disabled');
        }, 200);
      });

      const touchCancelOrEnd$ = merge(
        touchEnd$,
        touchCancel$
      ).pipe(
        tap(res => { if (res.cancelable) { res.preventDefault(); } }),
        tap(res => res.stopPropagation()),
        take(1));

      this.ngPress.pipe(
        tap(() => this.hasPrese = true),
        map(() => 0),
        switchMap(() => {
          return interval(100).pipe(
            takeUntil(touchCancelOrEnd$),
            map(res1 => res1++),
            tap(res2 => {
              this.ngZone.run(() => {
                this.ngPressing.emit(res2 / 10);
              });
            })
          );
        }),
        takeUntil(this.destroy$)
      ).subscribe();
      touchStart$.pipe(
        tap(res => { if (res.cancelable) { res.preventDefault(); } }),
        tap(res => res.stopPropagation()),
        // 初始化时间为0
        map(() => 0),
        // 防止重复触发
        filter(() => !this.hasPrese),
        switchMap(pressTime => {
          // 开始计时
          touchCancelOrEnd$.pipe(
            takeWhile(() => this.hasPrese)
          ).subscribe(() => {
            // 释放 超过1秒可以触发释放
            this.ngZone.run(() => {
              this.ngRelease.emit();
            });
          });
          // 开始计时
          return interval(100).pipe(
            // touchEnd结束
            takeUntil(
              touchCancelOrEnd$
            ),
            map(() => {
              return pressTime++;
            }),
            // 触发press
            filter(res => res > this.start),
            take(1),
            tap(() => {
              this.ngZone.run(() => {
                this.hasPrese = true;
                this.ngPress.emit();
              });
            })
          );
        }),
        takeUntil(this.destroy$)
      ).subscribe();
    });
  }

  ngOnDestroy(): void {
    // 清理所有订阅，防止内存泄漏
    this.destroy$.next();
    this.destroy$.complete();
  }
}

export function onTouchStart(ele: DomElement): Observable<TouchEvent> {
  return fromEvent(ele, 'touchstart') as Observable<TouchEvent>;
}
export function onTouchEnd(ele: DomElement): Observable<TouchEvent> {
  return fromEvent(ele, 'touchend') as Observable<TouchEvent>;
}
export function onTouchCancel(ele: DomElement): Observable<TouchEvent> {
  return fromEvent(ele, 'touchcancel') as Observable<TouchEvent>;
}
export function onTouchMove(ele: DomElement): Observable<TouchEvent> {
  return fromEvent(ele, 'touchmove') as Observable<TouchEvent>;
}

export type DomElement = HTMLElement | Element | Document | Window;
