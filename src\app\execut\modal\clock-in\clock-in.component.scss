ion-segment {
  width: 90%;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  margin: 12px 16px 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

  ion-segment-button {
    --color: #666;
    --color-checked: #fff;
    --background-checked: var(--ion-color-primary);
    --indicator-color: transparent;
    --border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    min-height: 36px;
    transition: all 0.3s ease;

    &.segment-button-checked {
      box-shadow: 0 2px 8px rgba(var(--ion-color-primary-rgb), 0.3);
      transform: translateY(-1px);
    }
  }
}

.required-label {
  position: relative;
  font-size: 16px;
  font-weight: bold;
  color: #222;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
.required-star {
  color: #ff4444;
  font-size: 16px;
  margin-left: 4px;
  font-weight: bold;
}

ion-footer {
  ion-toolbar {
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --background: #fff;

    &::before {
      display: none;
    }
  }

  ion-button {
    margin: 0px 12px;
    --border-radius: 6px;
    --box-shadow: 0 2px 6px rgba(var(--ion-color-primary-rgb), 0.3);
    font-weight: 500;
  }
}

.keypoint-info-list {
  margin: 16px 0 4px 0;
  padding: 0 8px;
}
.keypoint-info-row {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 2px;
}

.keypoint-label {
  color: #666;
  margin-right: 8px;
  min-width: 60px;
  
  &.point {
    color: #4285F4;
  }
  
  &.coord {
    color: #34A853;
  }
}

.keypoint-value {
  color: #333;
  font-weight: 500;
  flex: 1;
}
