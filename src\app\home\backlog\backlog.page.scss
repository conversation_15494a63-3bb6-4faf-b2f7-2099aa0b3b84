@import "../../../theme/variables.scss";
.backlog {
  margin: 0;
  padding: 0;
  background-color: var(--ion-color-primary-contrast);
  width: 100%;


  .backlog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-bottom: 1px solid #f6f6f6;

    .backlog-title {
      color: var(--ion-color-primary);
      font-size: 14px;
      font-weight: 500;
      margin: 0;
    }

    .backlog-date {
      font-size: 14px;
      color: #666;
    }
  }


  .backlog-content {

    .loading-state {
      width: 100%;
      height: 160px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px 0px 12px 0px;
      color: #666666;

      .loading-text {
        font-size: 12px;
        margin-left: 8px;
      }
    }


    .task-sections {
      .task-section {
        // margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }


        .section-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 16px;
          border-bottom: 1px solid #f0f0f0;

          .section-title {
            display: flex;
            align-items: center;
            gap: 6px;

            .section-icon {
              font-size: 16px;
            }

            .section-name {
              font-size: 14px;
              font-weight: 500;
              color: #333;
            }

            .section-stats {
              font-size: 12px;
              color: #666;
            }
          }

          .view-all-btn {
            background: transparent;
            border: none;
            color: var(--ion-color-primary);
            font-size: 12px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            
            &:hover {
              background-color: rgba(var(--ion-color-primary-rgb), 0.1);
            }
          }
        }


        .task-cards {
          padding: 8px 16px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          max-height: 150px;
          overflow-y: auto;

          .task-card {
             background: #fff;
             border-radius: 6px;
             border: 1px solid #f0f0f0;
             padding: 8px 12px;
             cursor: pointer;
             transition: all 0.2s ease;

             &:hover {
               border-color: #d0d0d0;
               box-shadow: 0 2px 8px rgba(0,0,0,0.1);
             }

             .task-row {
               display: flex;
               align-items: center;
               gap: 8px;



               .task-name {
                 font-size: 14px;
                 font-weight: 500;
                 color: #333;
                 flex: 1;
                 overflow: hidden;
                 text-overflow: ellipsis;
                 white-space: nowrap;
               }

               .task-progress {
                 font-size: 12px;
                 color: #666;
                 flex-shrink: 0;
                 min-width: 60px;
                 text-align: right;
               }

               .action-btn {
                 background: var(--ion-color-primary);
                 color: white;
                 border: none;
                 border-radius: 4px;
                 padding: 4px 8px;
                 font-size: 12px;
                 cursor: pointer;
                 transition: background-color 0.2s ease;
                 flex-shrink: 0;

                 &:hover {
                   background: var(--ion-color-primary-shade);
                 }
               }
             }
           }


        }


        .section-empty {
          padding: 20px;
          text-align: center;

          .empty-text {
            font-size: 12px;
            color: #999;
          }
        }
      }


       .inspection-section {
         .section-icon {
           color: #f59e0b;
         }
       }


       .patrol-section {
         .section-icon {
           color: #22c55e;
         }
       }
    }
  }


  .empty-state {
    width: 100%;
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 0px 12px 0px;

    .empty-icon {
      height: 45px;
    }

    .empty-text {
      padding-top: 10px;
      font-size: 12px;
      color: #666666;
    }
  }
}