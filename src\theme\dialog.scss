@import "./variables.scss";
// 对话框样式
.dialog-box-xs {
  --height: auto;
  --max-height: 70vh;
  --min-height: 200px;
  --width: 280px;
}
.dialog-box-sm {
  --height: 220px;
  --width: 60%;
}
.dialog-box-lg {
  --height: 50vh;
  --width: 80%;
}

.dialog-box-xl {
  --height: 50vh;
  --width: 80%;
}

.dialog-box-xxl {
  --height: 50vh;
  --width: 80%;
}

.dialog-box-xxxl {
  --height: 80vh;
  --width: 90%;
}

.label-stacked.sc-ion-label-md-h{
  font-size: 22px;
}

// 页面样式
.body{
  // width: 100%;height: 100%;
  background-color: #f6f6f6;
  --background: #f6f6f6;
  margin: 0px;
  padding: 0px;
}

// 暂无数据样式
.no-data {
  width: 100%;
  height: 60%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 0px 12px 0px;
  img {
    height: 98px;
  }
  .no-data-span {
    padding-top: 10px;
    font-size: 14px;
    color: #666666;
  }
}

.item-box {
  font-size: 14px;
  background-color: var(--ion-color-primary-contrast);
  margin-bottom: 12px;
}

// 列表ListItem标题名称（首页事件上报使用）
.entry-name-12px {
  font-size: 12px;
  padding-left: 8px;
  height: 24px;
  line-height: 24px;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // color: var(--ion-color-primary);
  // font-weight: 600;
}
// 列表ListItem标题时间（首页事件上报使用）
.entry-time-12px {
  text-align: right;
  font-size: 12px;
  padding-right: 16px;
  height: 24px;
  line-height: 24px;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 列表ListItem子标题（首页事件上报使用）
.entry-subtitle-12px {
  font-size: 12px;
  padding-left: 8px;
  height: 24px;
  line-height: 24px;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #ffa200;
  font-weight: 600;
}

// 编号
.serial-number {
  font-size: 14px;
  height: 30px;
  width: 30px;
  line-height: 30px;
  background-color: var(--ion-color-primary);
  text-align: center;
  color: var(--ion-color-primary-contrast);
}

// 列表ListItem标题名称
.entry-name {
  font-size: 14px;
  padding-left: 8px;
  height: 30px;
  line-height: 30px;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--ion-color-primary);
  font-weight: 600;
}
// 列表ListItem标题时间
.entry-time {
  text-align: right;
  font-size: 14px;
  padding-right: 16px;
  height: 30px;
  line-height: 30px;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 列表ListItem 自定义Icon
.entry-icon {
  text-align: right;
  font-size: 16px;
  padding-right: 16px;
  height: 30px;
  line-height: 30px;
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--ion-color-primary);
}

// 分隔线
.dividing-line {
  border-bottom: 2px solid #e3e3e3;
}

// 上下padding间距
.ion-padding-custom{
  padding: 8px 2px 8px 16px;
}

// 列表内容过长缩略
.list-col-name {
  overflow:hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


// 部门树
ost-tree-list,personal-tree-list {
  background-color: #fff;
  ul.tree-list-itmes {
    margin: 0;
    padding: 0;
  }

  .tree-list-group,
  .tree-list-itme a {
    font-size: 14px;
    font-weight: 900;
    line-height: 50px;
    padding-left: 20px;
    border-bottom: #fafafa solid 1px;
    background-color: #fff;
  }

  .tree-list-group,
  .tree-list-group on-icon.tree-list-icon {
    color: #000;
  }
  .tree-list-itme {
    color: #000;
    a {
      color: #000;
    }
    a.active {
      border-color: var(--ion-color-primary);
      background-color: #fafafa;
      color: var(--ion-color-primary);
      .tree-list-icon {
        color: var(--ion-color-primary);
      }
    }
  }

  .tree-list-itme > .tree-list-itmes > .tree-list-itme {
    a {
      padding-left: 32px !important;
    }
    a.active {
      border-color: var(--ion-color-primary);
      background-color: #fafafa;
      color: var(--ion-color-primary);
      .tree-list-icon {
        color: var(--ion-color-primary);
      }
    }
  }

  .tree-list-itme
    > .tree-list-itmes
    > .tree-list-itme
    > .tree-list-itmes
    > .tree-list-itme {
    a {
      padding-left: 48px !important;
    }
  }

  
  .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme {
    a {
      padding-left: 64px !important;
    }
  }

  .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme {
    a {
      padding-left: 78px !important;
    }
  }

  .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme
  > .tree-list-itmes
  > .tree-list-itme {
    a {
      padding-left: 96px !important;
    }
  }

  .tree-list-itme > .tree-list-itmes > .tree-list-group {
    &,
    & on-icon.tree-list-icon {
      color: #000;
    }
  }
}
