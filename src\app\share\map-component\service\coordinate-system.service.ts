import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Coordinate } from 'ol/coordinate';
import { CoordinateSystem, BaseMapCoordinateMapping, BusinessLayerConfigs } from '../map/layer.config';
import { LayerOption } from '../map/class/layer-option';

/**
 * 坐标转换结果接口
 */
export interface CoordinateTransformResult {
  coordinate: Coordinate;
  accuracy: number;
  source: CoordinateSystem;
  target: CoordinateSystem;
}

/**
 * 坐标系管理服务
 * 负责坐标系切换、坐标转换、图层配置管理
 */
@Injectable({
  providedIn: 'root'
})
export class CoordinateSystemService {
  // 当前激活的坐标系
  private currentCoordinateSystem$ = new BehaviorSubject<CoordinateSystem>(CoordinateSystem.CGCS2000);
  
  // 当前激活的底图ID
  private currentBaseMapId$ = new BehaviorSubject<string>('td_vec');
  
  // 坐标转换缓存
  private transformCache = new Map<string, CoordinateTransformResult>();
  
  // 缓存最大数量
  private readonly MAX_CACHE_SIZE = 1000;

  constructor() {}

  /**
   * 获取当前坐标系
   */
  getCurrentCoordinateSystem(): Observable<CoordinateSystem> {
    return this.currentCoordinateSystem$.asObservable();
  }

  /**
   * 获取当前坐标系值
   */
  getCurrentCoordinateSystemValue(): CoordinateSystem {
    return this.currentCoordinateSystem$.value;
  }

  /**
   * 获取当前底图ID
   */
  getCurrentBaseMapId(): Observable<string> {
    return this.currentBaseMapId$.asObservable();
  }

  /**
   * 获取当前底图ID值
   */
  getCurrentBaseMapIdValue(): string {
    return this.currentBaseMapId$.value;
  }

  /**
   * 切换底图并更新坐标系
   * @param baseMapId 底图ID
   */
  switchBaseMap(baseMapId: string): void {
    const coordinateSystem = BaseMapCoordinateMapping[baseMapId];
    if (coordinateSystem) {
      this.currentBaseMapId$.next(baseMapId);
      this.currentCoordinateSystem$.next(coordinateSystem);
      console.log(`🗺️ 底图切换: ${baseMapId}, 坐标系: ${coordinateSystem}`);
    }
  }

  /**
   * 根据当前底图获取对应的业务图层配置
   * @param layerIds 可选的图层ID过滤列表
   */
  getCurrentBusinessLayers(layerIds?: string[]): LayerOption[] {
    const currentCRS = this.getCurrentCoordinateSystemValue();
    const businessLayers = BusinessLayerConfigs[currentCRS] || BusinessLayerConfigs[CoordinateSystem.CGCS2000];
    
    if (layerIds && layerIds.length > 0) {
      return businessLayers.filter(layer => this.matchLayerByLogicalId(layer, layerIds));
    }
    
    return businessLayers;
  }

  /**
   * 通过逻辑ID匹配图层（忽略坐标系后缀）
   * @param layer 图层配置
   * @param layerIds 图层ID列表
   */
  private matchLayerByLogicalId(layer: LayerOption, layerIds: string[]): boolean {
    const logicalId = layer.id.replace(/_cgcs2000|_gcj02$/i, '');
    return layerIds.includes(logicalId);
  }

  /**
   * 坐标转换（CGCS2000 ↔ GCJ02）
   * @param coordinate 原始坐标
   * @param from 源坐标系
   * @param to 目标坐标系
   */
  transformCoordinate(coordinate: Coordinate, from: CoordinateSystem, to: CoordinateSystem): CoordinateTransformResult {
    // 如果坐标系相同，直接返回
    if (from === to) {
      return {
        coordinate,
        accuracy: 0,
        source: from,
        target: to
      };
    }

    // 生成缓存键
    const cacheKey = `${coordinate[0]},${coordinate[1]}_${from}_${to}`;
    
    // 检查缓存
    if (this.transformCache.has(cacheKey)) {
      return this.transformCache.get(cacheKey)!;
    }

    // 执行坐标转换
    let transformedCoordinate: Coordinate;
    let accuracy = 2; // 默认精度误差约2米

    if (from === CoordinateSystem.CGCS2000 && to === CoordinateSystem.GCJ02) {
      transformedCoordinate = this.cgcs2000ToGcj02(coordinate);
    } else if (from === CoordinateSystem.GCJ02 && to === CoordinateSystem.CGCS2000) {
      transformedCoordinate = this.gcj02ToCgcs2000(coordinate);
    } else {
      // 不支持的转换，返回原坐标
      transformedCoordinate = coordinate;
      accuracy = 0;
    }

    const result: CoordinateTransformResult = {
      coordinate: transformedCoordinate,
      accuracy,
      source: from,
      target: to
    };

    // 添加到缓存
    this.addToCache(cacheKey, result);

    return result;
  }

  /**
   * 批量坐标转换
   * @param coordinates 坐标数组
   * @param from 源坐标系
   * @param to 目标坐标系
   */
  batchTransformCoordinates(coordinates: Coordinate[], from: CoordinateSystem, to: CoordinateSystem): CoordinateTransformResult[] {
    return coordinates.map(coord => this.transformCoordinate(coord, from, to));
  }

  /**
   * CGCS2000转GCJ02（简化算法）
   * 注意：这是简化的转换算法，实际项目中应使用更精确的七参数转换
   */
  private cgcs2000ToGcj02(coordinate: Coordinate): Coordinate {
    const [lon, lat] = coordinate;
    
    // 简化的偏移量计算（实际应使用更精确的算法）
    const dLat = this.transformLat(lon - 105.0, lat - 35.0);
    const dLon = this.transformLon(lon - 105.0, lat - 35.0);
    
    const radLat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    
    const dLatFinal = (dLat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
    const dLonFinal = (dLon * 180.0) / (6378245.0 / sqrtMagic * Math.cos(radLat) * Math.PI);
    
    return [lon + dLonFinal, lat + dLatFinal];
  }

  /**
   * GCJ02转CGCS2000（简化算法）
   */
  private gcj02ToCgcs2000(coordinate: Coordinate): Coordinate {
    const [lon, lat] = coordinate;
    
    // 使用迭代方法进行逆转换
    const gcj02 = this.cgcs2000ToGcj02([lon, lat]);
    const dLon = gcj02[0] - lon;
    const dLat = gcj02[1] - lat;
    
    return [lon - dLon, lat - dLat];
  }

  /**
   * 纬度转换辅助函数
   */
  private transformLat(lon: number, lat: number): number {
    let ret = -100.0 + 2.0 * lon + 3.0 * lat + 0.2 * lat * lat + 0.1 * lon * lat + 0.2 * Math.sqrt(Math.abs(lon));
    ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 经度转换辅助函数
   */
  private transformLon(lon: number, lat: number): number {
    let ret = 300.0 + lon + 2.0 * lat + 0.1 * lon * lon + 0.1 * lon * lat + 0.1 * Math.sqrt(Math.abs(lon));
    ret += (20.0 * Math.sin(6.0 * lon * Math.PI) + 20.0 * Math.sin(2.0 * lon * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lon * Math.PI) + 40.0 * Math.sin(lon / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lon / 12.0 * Math.PI) + 300.0 * Math.sin(lon / 30.0 * Math.PI)) * 2.0 / 3.0;
    return ret;
  }

  /**
   * 添加到缓存
   */
  private addToCache(key: string, result: CoordinateTransformResult): void {
    // 如果缓存已满，删除最旧的条目
    if (this.transformCache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.transformCache.keys().next().value;
      this.transformCache.delete(firstKey);
    }
    
    this.transformCache.set(key, result);
  }

  /**
   * 清空转换缓存
   */
  clearTransformCache(): void {
    this.transformCache.clear();
  }

  /**
   * 验证坐标有效性
   * @param coordinate 坐标
   */
  isValidCoordinate(coordinate: Coordinate): boolean {
    if (!coordinate || coordinate.length < 2) {
      return false;
    }
    
    const [lon, lat] = coordinate;
    
    // 检查经纬度范围（中国境内）
    return lon >= 73 && lon <= 135 && lat >= 18 && lat <= 54;
  }

  /**
   * 获取坐标系显示名称
   */
  getCoordinateSystemDisplayName(crs: CoordinateSystem): string {
    switch (crs) {
      case CoordinateSystem.CGCS2000:
        return '国家2000';
      case CoordinateSystem.GCJ02:
        return '火星坐标';
      default:
        return '未知坐标系';
    }
  }
}
