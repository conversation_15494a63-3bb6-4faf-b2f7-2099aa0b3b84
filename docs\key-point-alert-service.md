# KeyPointAlertService 服务说明文档

## 服务简介

`KeyPointAlertService` 是一个用于关键点距离提醒的服务，适用于基于 Angular + Ionic 的移动端应用。该服务可根据用户当前位置与预设关键点的距离，自动触发震动提醒和TTS语音播报，并支持自定义回调扩展，常用于地理围栏、巡检打卡、路线导航等场景。

## 主要功能
- 关键点距离检测与提醒
- 震动提醒（基于 `@ionic-native/vibration`）
- TTS语音播报（基于 `@ionic-native/text-to-speech`）
- 智能数字转换（支持工程测量格式播报）
- 支持自定义提醒回调
- 支持关键点提醒状态重置

## API 说明

### 1. `init(keyPointList: any[]): void`
初始化关键点列表，并重置提醒状态。
- **参数**：`keyPointList` —— 关键点对象数组，格式为：
  ```typescript
  {
    pointName: string,        // 关键点名称
    point: string | number[], // 坐标，支持字符串或数组格式
    bufferRange?: number      // 提醒范围（米），默认2米
  }[]
  ```

### 2. `updateLocation(currentCoord: number[]): void`
位置更新时调用，判断当前位置是否接近某个关键点并触发提醒。
- **参数**：`currentCoord` —— 当前坐标 `[lng, lat]`

### 3. `reset(): void`
重置所有关键点的提醒状态。

### 4. `setOnAlert(callback: (index: number, point: number[], pointName: string) => void): void`
设置提醒回调函数，在触发提醒时执行自定义逻辑。
- **参数**：`callback` —— 回调函数，参数为关键点索引、坐标和名称

### 5. `triggerAlert(index: number, pointObj: any): void`
手动触发关键点提醒（用于测试或特殊场景）。
- **参数**：
  - `index` —— 关键点索引
  - `pointObj` —— 关键点对象

### 6. `static getDistanceMeters([lng1, lat1]: number[], [lng2, lat2]: number[]): number`
静态方法，计算两点间的球面距离（单位：米）。
- **参数**：两个坐标点 `[lng, lat]`
- **返回值**：距离（米）

### 7. `isInRange(currentCoord: number[]): { inRange: boolean, keyPoint?: any }`
判断当前位置是否在任一关键点的指定范围内。
- **参数**：`currentCoord` —— 当前坐标 `[lng, lat]`
- **返回值**：包含是否在范围内和命中的关键点信息

## TTS播报特性

### 智能数字转换
服务会自动将关键点名称中的数字和符号转换为TTS友好的格式：

| 原始格式 | 播报格式 | 说明 |
|---------|---------|------|
| "001+12945.3" | "零零一加一二九四五点三" | 数字逐个播报，符号转中文 |
| "KP-123.45" | "KP减一二三点四五" | 支持英文+数字混合 |
| "A点(001)" | "A点左括号零零一右括号" | 支持括号等特殊符号 |
| "测试点001.5" | "测试点零零一点五" | 支持中文+数字混合 |

### 字符转换规则
- **数字**：0-9 → 零到九
- **小数点**：. → 点
- **加号**：+ → 加
- **减号**：- → 减
- **括号**：( ) → 左括号 右括号
- **其他**：中文、英文、空格、下划线保持原样

## 使用示例

```typescript
import { KeyPointAlertService } from 'src/app/@core/services/key-point-alert.service';

// 1. 注入服务（以 Angular 组件为例）
constructor(private keyPointAlert: KeyPointAlertService) {}

// 2. 初始化关键点
this.keyPointAlert.init([
  {
    pointName: '001+12945.3',
    point: [120.123456, 30.123456],
    bufferRange: 5
  },
  {
    pointName: 'KP-123.45',
    point: [120.654321, 30.654321],
    bufferRange: 3
  }
]);

// 3. 设置提醒回调（可选）
this.keyPointAlert.setOnAlert((index, point, pointName) => {
  console.log(`到达第${index + 1}个关键点: ${pointName}`, point);
  // 可扩展音频、弹窗等
});

// 4. 在位置变化时调用
this.keyPointAlert.updateLocation([120.123400, 30.123400]);

// 5. 测试TTS播报（可选）
testVibration() {
  const pointObj = {
    pointName: '001+12945.3',
    pointArr: [0, 0]
  };
  this.keyPointAlert.triggerAlert(0, pointObj);
}

// 6. 重置提醒状态（如需重新开始）
this.keyPointAlert.reset();
```

## 扩展点与注意事项

- **TTS播报**：服务已集成TTS功能，支持中文语音播报，播报格式为"您已到达{关键点名称}关键点"
- **震动提醒**：默认震动模式为 `[500, 100, 500, 100, 500]`（震动500ms，停顿100ms，重复3次）
- **关键点格式**：支持字符串和数组两种坐标格式，服务会自动解析
- **提醒距离**：每个关键点可单独设置 `bufferRange`，默认为2米
- **适用场景**：适用于巡检、签到、路线导航、工程测量等需到点提醒的业务
- **依赖说明**：需在项目中引入 `@ionic-native/vibration` 和 `@ionic-native/text-to-speech` 插件

## 技术特性

- ✅ **智能数字处理**：自动转换数字为TTS友好格式
- ✅ **多格式支持**：支持各种关键点命名格式
- ✅ **性能优化**：高效的字符映射算法
- ✅ **可扩展性**：易于添加新的字符转换规则
- ✅ **错误处理**：完善的TTS异常处理机制

---

如有更多定制需求，可参考源码进行扩展。 