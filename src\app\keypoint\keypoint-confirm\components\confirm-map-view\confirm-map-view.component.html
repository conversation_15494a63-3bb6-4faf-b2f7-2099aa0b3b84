<div class="map-view-container" #mapContainer>
  <!-- 地图组件 -->
  <ost-map 
    #mapComponent
    class="confirm-map"
    [layerIds]="layerIds"
    [addMapClickEvent]="true"
    (mapLoaded)="onMapReady()">
  </ost-map>
  
  <!-- 地图控制按钮 -->
  <div class="map-controls">
    <ion-button 
      fill="clear" 
      size="small" 
      class="control-btn"
      (click)="fitToAllPoints()"
      title="显示全部关键点">
      <ion-icon name="scan" slot="icon-only"></ion-icon>
    </ion-button>
    

  </div>
  

  
  <!-- 统计信息 -->
  <div class="map-stats" *ngIf="points.length > 0">
    <div class="stats-item">
      <ion-icon name="location" color="primary"></ion-icon>
      <span>{{points.length}}</span>
    </div>

  </div>
  
  <!-- 加载状态 -->
  <div class="map-loading" *ngIf="!mapReady">
    <ion-spinner></ion-spinner>
    <p>地图加载中...</p>
  </div>
  
  <!-- 空状态 -->
  <div class="map-empty" *ngIf="mapReady && points.length === 0">
    <ion-icon name="map" color="medium"></ion-icon>
    <p>暂无关键点数据</p>
  </div>
</div>