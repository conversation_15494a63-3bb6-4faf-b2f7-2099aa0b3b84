import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod } from '@ngx-resource/core';
import { VersionInfo } from './class/version-info';
import { RequestResult } from 'src/app/@core/base/request-result';

/**
 * 版本检测服务
 */
@ResourceParams({
  pathPrefix: '',
})

@Injectable({
  providedIn: 'root'
})
export class VersionCheckService extends Resource {

  constructor() { super(); }

  /**
   * 版本检测
   */
  @ResourceAction({
    method: ResourceRequestMethod.Get,
    path: '/work-inspect/api/v2/inspect/version/new',
  })
  CheckVersion!: IResourceMethodObservable<null, RequestResult<VersionInfo>>;
}
