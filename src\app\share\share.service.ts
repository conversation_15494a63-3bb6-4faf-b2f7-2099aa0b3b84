import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod, ResourceResponseBodyType } from '@ngx-resource/core';
import { environment } from 'src/environments/environment';


@ResourceParams({})
@Injectable({
  providedIn: 'root'
})
/**
 * 公共模块服务
 */
export class ShareModuleService extends Resource {
  constructor() { super(); }

  @ResourceAction({
    url: '{interfaceUrl}',
    method: ResourceRequestMethod.Get,
  })
  selectOptionRequest!: IResourceMethodObservable<any, any>;

  @ResourceAction({
    path: '{interfaceUrl}',
    method: ResourceRequestMethod.Get,
  })
  getRequest!: IResourceMethodObservable<any, any>;

  @ResourceAction({
    path: '{interfaceUrl}',
    method: ResourceRequestMethod.Post,
  })
  postRequest!: IResourceMethodObservable<any, any>;

  @ResourceAction({
    path: '{interfaceUrl}',
    method: ResourceRequestMethod.Put,
  })
  putRequest!: IResourceMethodObservable<any, any>;

  /**
   * 获取部门列表
   */
  @ResourceAction({
    method: ResourceRequestMethod.Get,
    path: '/work-user/api/v2/userCenter/dep/msg/list',
  })
  orgList!: IResourceMethodObservable<null, any>;

  /**********************************************************附件上传服务*************************************************************** */

  /**
   * 删除服务器文件
   */
  @ResourceAction({
    path: '/file/delete',
    method: ResourceRequestMethod.Delete,
    responseBodyType: ResourceResponseBodyType.Text,
  })
  DeleteFile: IResourceMethodObservable<any, any>;

  /**
   * 获取图片附件数据数据
   */
  @ResourceAction({
    url: `${environment.production ? 'https' : 'http'}://${environment.api.ip}:${environment.api.port}${environment.openProxy ? '/serverApi' : ''}/file/fileDes`,
    method: ResourceRequestMethod.Post,
    responseBodyType: ResourceResponseBodyType.Json,
  })
  GetFile: IResourceMethodObservable<any, any>;

  /**
   * 附件添加
   */
  @ResourceAction({
    url: `${environment.production ? 'https' : 'http'}://${environment.api.ip}:${environment.api.port}${environment.openProxy ? '/serverApi' : ''}/file/base64/upload?busType=defBusType`,
    method: ResourceRequestMethod.Post,
  })
  addFile: IResourceMethodObservable<any, any>;

}

