<!-- 打卡模态框页面 -->
<ion-header>
  <ion-toolbar [color]="currentToolbarColor">
    <ion-title>{{ currentTabTitle }}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onClose()">
        关闭
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<!-- Tab切换区域 -->
<div class="tabs-container">
  <div class="tabs">
    <button class="tab-button" 
      [ngClass]="{ active: activeTab === 'clockIn' }" 
      (click)="switchTab('clockIn')">
      关键点打卡
    </button>
    <button class="tab-button" 
      [ngClass]="{ active: activeTab === 'reason' }" 
      (click)="switchTab('reason')">
      未巡检原因
    </button>
  </div>
</div>

<ion-content style="height: calc(60vh - 45px - 56px);">
  <!-- 关键点打卡组件 -->
  <app-clock-in
    *ngIf="activeTab === 'clockIn'"
    [currentKeyPoint]="currentKeyPoint"
    [taskCode]="taskCode"
    [modelMode]="modelMode"
    [isDetailMode]="isDetailMode"
    [coordinate]="currentCoordinate"
    (submitSuccess)="onSubmitSuccess($event)">
  </app-clock-in>

  <!-- 未巡检原因上报组件 -->
  <app-not-inspected
    *ngIf="activeTab === 'reason'"
    [currentKeyPoint]="currentKeyPoint"
    [taskCode]="taskCode"
    [modelMode]="modelMode"
    [isDetailMode]="isDetailMode"
    (submitSuccess)="onSubmitSuccess($event)">
  </app-not-inspected>
</ion-content> 