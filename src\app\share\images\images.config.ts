import { environment } from 'src/environments/environment';

// 图片文件接口
export interface ImageFile {
  // 'pending': 待上传, 'uploading': 上传中, 'success': 上传成功, 'failed': 上传失败， 'optimizing':优化中
  status: 'pending' | 'uploading' | 'success' | 'failed' | 'optimizing';
  base64?: string;    // base64格式的图片数据
  fileCode?: string;  // 文件唯一标识
  url?: string;       // 文件访问地址
}

// 图片配置
export const IMAGE_CONFIG = {
  // 服务器配置
  server: {
    protocol: environment.production ? 'https' : 'http',
    host: environment.api.ip,
    port: environment.api.port
  }
  
} as const; 