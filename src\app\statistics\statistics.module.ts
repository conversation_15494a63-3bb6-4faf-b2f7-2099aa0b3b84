import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { NgxEchartsModule } from "ngx-echarts";
import { ShareModule } from "../share/share.module";
import { StatisticsPageRoutingModule } from "./statistics-routing.module";
import { StatisticsPage } from "./statistics.page";
import { IncidentListComponent } from "./incident-list/incident-list.component";
import { FormatPercentagePipe } from "../@core/utils/format-percentage.pipe";

const COMPONENT: any[] = [
  StatisticsPage,
  IncidentListComponent,
];
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ShareModule,
    NgxEchartsModule,
    StatisticsPageRoutingModule
  ],
  entryComponents: [...COMPONENT],
  declarations: [...COMPONENT,FormatPercentagePipe]
})
export class StatisticsPageModule { }