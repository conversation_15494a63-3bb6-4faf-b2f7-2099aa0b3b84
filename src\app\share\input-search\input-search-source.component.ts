import { AfterViewInit, Component, Input, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'ost-input-search-source',
  templateUrl: './input-search-source.component.html'
})
export class InputSearchSourceComponent implements AfterViewInit {
  @ViewChild('sourceContainer', { read: ViewContainerRef, static: false }) viewRef: ViewContainerRef;

  @Input() tplRef: TemplateRef<any>;

  constructor(private modalCtrl: ModalController) { }

  ngAfterViewInit(): void {
    this.viewRef.createEmbeddedView(this.tplRef);
  }

  onClose(): void {
    this.modalCtrl.dismiss(false);
  }

  onConfirm(): void {
    this.modalCtrl.dismiss(true);
  }
}
