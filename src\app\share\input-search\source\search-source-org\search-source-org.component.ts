import { Component, Input, OnInit } from '@angular/core';
import { OstTreeListItem } from 'src/app/share/ost-tree-list/ost-tree-list.service';
import { InputSearchSourceService } from '../../input-search-source.service';

@Component({
  selector: 'search-source-org',
  templateUrl: './search-source-org.component.html'
})
export class SearchSourceOrgComponent implements OnInit {

  @Input() depCode: string;
  @Input() interfaceUrl: string;
  @Input() leafOnly: boolean;
  constructor(public searchSer: InputSearchSourceService) { }

  ngOnInit(): void {
  }
  onToggleSubMenu(item: OstTreeListItem): void {
    this.searchSer.change(item.data.depName, item.data.depCode);
  }
  onItemClick(item: OstTreeListItem): void {
    this.searchSer.change('', '');
    if (this.leafOnly) {
      const hasChildrenField = item.hasOwnProperty('children');
      if (hasChildren<PERSON>ield) {
        console.log('只能选择子节点');
      } else {
        this.searchSer.change(item.data.depName, item.data.depCode);
      }
    } else {
      this.searchSer.change(item.data.depName, item.data.depCode);
    }

  }
}
