import { Injectable, ElementRef } from '@angular/core';
import { PerformanceCache, ThrottleControl, ImageDimensions } from './image-gesture.model';

/**
 * 性能优化工具
 * 负责缓存管理和节流控制
 */
@Injectable({
  providedIn: 'root'
})
export class PerformanceOptimizerService {
  // 性能优化：缓存计算结果
  private cache: PerformanceCache = {
    containerRect: null,
    imageRect: null,
    lastTime: 0,
    duration: 100 // 缓存100ms
  };

  // 性能优化：节流控制
  private throttle: ThrottleControl = {
    lastTime: 0,
    interval: 8 // 约120fps，提高缩放流畅度
  };

  /**
   * 性能优化：获取缓存的尺寸信息
   */
  getCachedRect(
    type: 'container' | 'image',
    imageElement?: ElementRef,
    containerElement?: HTMLElement
  ): DOMRect | null {
    const now = Date.now();
    const cacheKey = type === 'container' ? 'containerRect' : 'imageRect';

    if (this.cache[cacheKey] && (now - this.cache.lastTime) < this.cache.duration) {
      return this.cache[cacheKey];
    }

    const element = type === 'container'
      ? containerElement
      : imageElement?.nativeElement;

    if (element) {
      this.cache[cacheKey] = element.getBoundingClientRect();
      this.cache.lastTime = now;
      return this.cache[cacheKey];
    }

    return null;
  }

  /**
   * 性能优化：清除缓存
   */
  clearCache(): void {
    this.cache.containerRect = null;
    this.cache.imageRect = null;
    this.cache.lastTime = 0;
  }

  /**
   * 性能优化：检查是否应该更新（节流）
   */
  shouldUpdate(): boolean {
    const now = Date.now();
    if (now - this.throttle.lastTime >= this.throttle.interval) {
      this.throttle.lastTime = now;
      return true;
    }
    return false;
  }

  /**
   * 设置节流间隔
   */
  setThrottleInterval(interval: number): void {
    this.throttle.interval = interval;
  }

  /**
   * 设置缓存持续时间
   */
  setCacheDuration(duration: number): void {
    this.cache.duration = duration;
  }

  /**
   * 计算图片尺寸信息
   */
  calculateImageDimensions(
    scale: number,
    imageElement?: ElementRef,
    containerElement?: HTMLElement
  ): ImageDimensions | null {
    const containerRect = this.getCachedRect('container', imageElement, containerElement);
    const imgRect = this.getCachedRect('image', imageElement, containerElement);

    if (!containerRect || !imgRect) {
      return null;
    }

    const originalWidth = imgRect.width / scale;
    const originalHeight = imgRect.height / scale;
    const scaledWidth = originalWidth * scale;
    const scaledHeight = originalHeight * scale;

    return {
      containerRect,
      imgRect,
      originalWidth,
      originalHeight,
      scaledWidth,
      scaledHeight
    };
  }

  /**
   * 重置性能优化器
   */
  reset(): void {
    this.clearCache();
    this.throttle.lastTime = 0;
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): {
    hasContainerCache: boolean;
    hasImageCache: boolean;
    cacheAge: number;
  } {
    const now = Date.now();
    return {
      hasContainerCache: this.cache.containerRect !== null,
      hasImageCache: this.cache.imageRect !== null,
      cacheAge: now - this.cache.lastTime
    };
  }

  /**
   * 获取节流状态
   */
  getThrottleStatus(): {
    timeSinceLastUpdate: number;
    shouldUpdate: boolean;
  } {
    const now = Date.now();
    return {
      timeSinceLastUpdate: now - this.throttle.lastTime,
      shouldUpdate: this.shouldUpdate()
    };
  }
}
