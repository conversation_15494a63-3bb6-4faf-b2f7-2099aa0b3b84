import { InjectionToken } from '@angular/core';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { environment } from 'src/environments/environment';

/**
 * 环境变量
 */
export interface Environment {
    mapTK?: string;
    // 版本号
    version: string;
    // 是否是生产环境
    production: boolean;
    // 网络配置
    api: Api;
    // 工程名称
    projectName: string;
    // 是否开启代理
    openProxy: boolean;
    // 加密配置
    encryption?: {
        // 是否启用加密
        enabled: boolean;
        // 是否启用调试日志
        debugEnabled: boolean;
    };
}
/**
 * 网络配置
 */
export interface Api {
    // 服务地址
    ip: string;
    // 服务器端口号
    port?: number;
    // 接口版本号
    version: string;
    // 地图地址
    mapUrl?: string;
}

/**
 * 详情模式
 */
export enum DetailsMode {
    ADD = '新增',
    SEE = '查看',
    EDITE = '编辑'
}

export const CurrentTime = new InjectionToken<string>('CurrentTime');


/***
 * 网络配置加载
 */
export const loadNetWorkConfig = () => {
    ResourceGlobalConfig.url = `${environment.production ? 'https' : 'http'}://${environment.api.ip}:${environment.api.port}${environment.openProxy ? '/serverApi' : ''}`;
};

