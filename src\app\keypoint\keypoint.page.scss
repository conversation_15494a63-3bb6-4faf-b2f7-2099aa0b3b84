// 关键点管理页面样式

.container {
  padding: 16px;
}

// 主要功能按钮
.view-button {
  margin-bottom: 16px;
  --border-radius: 12px;
  --box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
  height: 48px;
  font-weight: 600;
  
  ion-icon {
    font-size: 18px;
  }
}

// 描述文本
.description {
  display: block;
  margin-bottom: 16px;
  
  p {
    font-size: 13px;
    line-height: 1.4;
    margin: 0;
    text-align: center;
  }
}

// 分隔线
.divider {
  border: none;
  height: 1px;
  background: var(--ion-color-light-shade);
  margin: 16px 0;
}

// 列表项样式
ion-list {
  background: transparent;
  
  ion-item {
    --padding-start: 0;
    --padding-end: 0;
    --border-color: var(--ion-color-light-shade);
    margin-bottom: 8px;
    border-radius: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    ion-label {
      h2 {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin-bottom: 4px;
      }
      
      p {
        font-size: 13px;
        color: var(--ion-color-medium);
        margin: 0;
      }
    }
    
    ion-icon[slot="start"] {
      font-size: 20px;
      margin-right: 12px;
    }
    
    ion-icon[slot="end"] {
      color: var(--ion-color-medium);
      font-size: 16px;
    }
  }
}

// 卡片样式
ion-card {
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  
  ion-card-header {
    padding-bottom: 8px;
    
    ion-card-title {
      font-size: 20px;
      font-weight: 700;
      color: var(--ion-color-dark);
    }
    
    ion-card-subtitle {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin-top: 4px;
    }
  }
}

// 浮动操作按钮
ion-fab {
  ion-fab-button {
    --background: var(--ion-color-primary);
    --box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.4);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
  
  .view-button {
    height: 44px;
    font-size: 14px;
    
    ion-icon {
      font-size: 16px;
    }
  }
  
  ion-card {
    ion-card-header {
      ion-card-title {
        font-size: 18px;
      }
      
      ion-card-subtitle {
        font-size: 13px;
      }
    }
  }
  
  ion-list {
    ion-item {
      ion-label {
        h2 {
          font-size: 15px;
        }
        
        p {
          font-size: 12px;
        }
      }
      
      ion-icon[slot="start"] {
        font-size: 18px;
        margin-right: 10px;
      }
    }
  }
}