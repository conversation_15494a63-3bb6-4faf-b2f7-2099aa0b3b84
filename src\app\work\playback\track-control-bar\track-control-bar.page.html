<div class="track-control">
  <div class="track-control-layout">
    <!-- 中部 -->
    <div class="layout-content">
      <!-- 用户选择区域 -->
      <div class="user-selector" (click)="$event.stopPropagation()">
        <div class="user-selected" (click)="toggleUserList()">
          <span>巡检人员: {{ trackUsers[selectedUserIndex]?.userName || '无数据' }}</span>
          <ion-icon name="chevron-down-outline" [class.rotate-icon]="showUserList"></ion-icon>
        </div>
        <div class="user-list" [class.show]="showUserList" [hidden]="!showUserList">
          <div class="user-item" *ngFor="let user of trackUsers; let i = index"
            [class.active]="i === selectedUserIndex"
            (click)="selectUserTrack(i)">
            {{user.userName}}
          </div>
        </div>
      </div>
      
      <div>
        <ion-range min="0" max="100" pin [(ngModel)]="speed" (ngModelChange)="onSpeedChange()">
        <ion-note slot="start">速度:</ion-note>
        <ion-note slot="end"> {{speed}} </ion-note>
      </ion-range>
      </div>
      <div>
          <ion-range min="0"  max="100"  pin [(ngModel)]="progress" (ngModelChange)="onProgressChange($event)" >
            <ion-note slot="start">进度:</ion-note>
            <ion-note slot="end"> {{progress}} </ion-note>
          </ion-range>
      </div>
    </div>
  </div>
  
  <!-- 任务控制 -->
  <ion-item lines="none" class="ion-text-center">
    <!-- 开始按钮 -->
    <div [hidden]="playState" slot="start" class="icon-div" (click)="onPlayStateChange()" >
      <ion-icon class="icon-suspend" name="play" ></ion-icon>
    </div>
    <!-- 暂停按钮 -->
    <div [hidden]="!playState" slot="start" class="icon-div" (click)="onPlayStateChange()" >
      <ion-icon class="icon-cancel-suspend" name="pause" ></ion-icon>
    </div>
    <!-- 时间 -->
    <div class="track-date" >
      {{trackDate}}
    </div>
    <!-- 结束按钮 -->
    <div slot="end" class="icon-div" (click)="onStop()"  >
      <ion-icon  class="icon-stop" name="stop" ></ion-icon>
    </div>
  </ion-item>
</div>