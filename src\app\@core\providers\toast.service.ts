import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  // 存储最近显示的消息
  private recentMessages = new Map<string, number>();

  constructor(private toastCtrl: ToastController) { }

  async presentToast(
    msg: string, color: 'primary' | 'success' | 'warning' | 'danger' = 'primary',
    duration = 2000, position: 'top' | 'middle' | 'bottom' = 'top'
  ) {
    // 获取当前时间戳
    const now = Date.now();
    
    // 检查是否存在相同消息且在2秒内
    const lastShownTime = this.recentMessages.get(msg);
    if (lastShownTime && now - lastShownTime < 2000) {
      return; // 如果是重复消息且在2秒内，直接返回
    }

    // 更新消息显示时间
    this.recentMessages.set(msg, now);

    // 清理旧消息（可选，防止 Map 无限增长）
    this.cleanOldMessages();

    const toast = await this.toastCtrl.create({
      message: msg,
      duration: duration,
      position: position,
      color
    });
    toast.present();
  }

  // 清理超过2秒的旧消息
  private cleanOldMessages() {
    const now = Date.now();
    for (const [msg, time] of this.recentMessages.entries()) {
      if (now - time > 2000) {
        this.recentMessages.delete(msg);
      }
    }
  }
}