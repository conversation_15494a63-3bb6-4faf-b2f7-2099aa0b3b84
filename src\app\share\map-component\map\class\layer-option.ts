import { Extent } from 'ol/extent';
import { Options } from 'ol/Tile';

export class LayerOption {
    readonly id: string;
    readonly childs?: LayerOption[];
    readonly name?: string;
    readonly icon?: string;
    readonly url?: string;
    readonly serverType?: ServerType;
    readonly param?: any;
    readonly tileGrid?: Options;
    readonly projection?: string;
    readonly extent?: Extent;
    readonly format?: FormatStr;
    readonly formatOption?: any;
    readonly coordinateSystem?: string; // 新增坐标系字段
    visible?: boolean;
    selected?: boolean;
}
export type ServerType = 'GROUP' | 'TILE' | 'WMS' | 'WFS' | 'KML' | 'WMTS' | 'XYZ' | 'TILE-TILEGRID';
export type FormatStr = 'GEOJSON' | 'GML';
