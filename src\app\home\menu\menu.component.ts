import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { UserInfoService } from '../../@core/providers/user-Info.service';
import { KeypointConfirmService } from '../../keypoint/keypoint-confirm/keypoint-confirm.service';
import { PageEventService } from '../home.event';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'home-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss']
})
export class MenuComponent implements OnInit, OnDestroy {
  // 当前用户信息
  currentUser: any = {};
  // 未确认关键点数量
  unconfirmedCount = 0;
  // 销毁订阅
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private userInfoService: UserInfoService,
    private keypointConfirmService: KeypointConfirmService,
    private pageEventService: PageEventService
  ) { }

  ngOnInit(): void {
    this.getCurrentUserInfo();
    // 如果有关键点审批权限，则开始监听home事件
    if (this.hasKeyPointApprovalPermission()) {
      this.startHomeEventListener();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUserInfo(): void {
    this.currentUser = {
      userId: this.userInfoService.userId,
      userName: this.userInfoService.userName,
      account: this.userInfoService.account,
      phoneNumber: this.userInfoService.phoneNumber,
      depCode: this.userInfoService.depCode,
      depName: this.userInfoService.depName,
      roleType: this.userInfoService.roleType,
      avatar: this.userInfoService.Avatar
    };
  }

  /**
   * 检查是否有关键点查看权限
   */
  hasKeyPointPermission(): boolean {
    return this.currentUser.roleType && this.currentUser.roleType.includes('关键点管理员');
  }

  /**
   * 检查是否有关键点审批权限
   */
  hasKeyPointApprovalPermission(): boolean {
    return this.currentUser.roleType && this.currentUser.roleType.includes('关键点管理员');
  }

  /**
   * 跳转对应菜单
   */
  onSubItemClick(link: string): void {
    this.router.navigateByUrl(link);
  }

  /**
   * 开始监听home事件
   */
  private startHomeEventListener(): void {
    // 立即获取一次未确认数量
    this.getUnconfirmedCount();
    
    // 监听home事件，当收到事件时更新未确认关键点数量
    this.pageEventService.receiveDataForPageType('home')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (event) => {
          this.getUnconfirmedCount();
        },
        error: (error) => {
          console.error('[MenuComponent] 监听home事件失败:', error);
        }
      });
  }

  /**
   * 获取未确认关键点数量
   */
  private getUnconfirmedCount(): void {
    this.keypointConfirmService.getUnconfirmedCount({})
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          if (result.code === 0) {
            this.unconfirmedCount = parseInt(result.data) || 0;
          }
        },
        error: (error) => {
          console.error('获取未确认关键点数量失败:', error);
        }
      });
  }

}
