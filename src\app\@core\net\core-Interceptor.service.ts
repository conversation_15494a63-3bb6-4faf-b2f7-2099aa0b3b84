import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, mergeMap, tap } from 'rxjs/operators';
import { StorageService } from '../providers/storage.service';
import { ToastService } from '../providers/toast.service';
import { NetworkService } from '../providers/network.service';
import { Router } from '@angular/router';
import { AesEncryptionService } from './aes-encryption.service';
import { EncryptionDebugLoggerService } from './encryption-debug-logger.service';
import { environment } from 'src/environments/environment';
import { ModalManagerService } from 'src/app/execut/services/modal-manager.service';

/**
 * 核心HTTP拦截器服务
 * 优化版本：统一处理加密、缓存、认证和错误处理
 */
@Injectable({
  providedIn: 'root'
})
export class CoreInterceptorService implements HttpInterceptor {
  // 需要缓存的url
  private readonly cacheUrls = new Set([
    '/work-user/api/v2/userCenter/dep/msg/list',  // 部门列表
    '/work-inspect/api/v2/inspect/dict/msg/byDictCode?dictCode=eventType',  // 事件类型
  ]);

  // 不需要加密的接口地址 （白名单）
  private readonly encryptionWhitelist = new Set([

  ]);

  // 认证相关的错误码
  private readonly authErrorCodes = new Set([7000, 9990]);

  // 特殊处理的错误码
  private readonly specialErrorCodes = new Map([
    [8006, { message: '密码已过期', action: () => this.router.navigate(['/editPassword'], { queryParams: { isModal: false } }) }]
  ]);

  constructor(
    private nav: NavController,
    private storage: StorageService,
    private toastSer: ToastService,
    private networkService: NetworkService,
    private router: Router,
    private aesEncryption: AesEncryptionService,
    private debugLogger: EncryptionDebugLoggerService,
    private modalManager: ModalManagerService
  ) { }


  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // 处理请求加密
    const processedRequest = this.processRequest(req);

    // 检查网络状态和缓存
    if (this.isOfflineAndCacheable(processedRequest.url)) {
      return this.handleOfflineRequest(processedRequest.url);
    }

    // 在线请求处理
    return this.handleOnlineRequest(processedRequest, next);
  }

  /**
   * 为所有请求自动注入 messageReplayTime 字段到URL参数（毫秒级时间戳）
   */
  private injectMessageReplayTime(req: HttpRequest<any>): HttpRequest<any> {
    const timestamp = Date.now();
    const urlObj = new URL(req.url, 'http://localhost');
    urlObj.searchParams.set('messageReplayTime', String(timestamp));

    // 合并原有params，但排除messageReplayTime避免重复
    let params = req.params;
    if (params && typeof params.keys === 'function') {
      params = params.keys().reduce((acc, key) => {
        if (key !== 'messageReplayTime') {
          acc = acc.set(key, params.get(key));
        }
        return acc;
      }, new HttpParams());
    } else {
      params = new HttpParams();
    }

    return req.clone({ url: urlObj.toString(), params });
  }

  /**
   * 处理请求（加密等预处理 添加 messageReplayTime 时间戳）
   */
  // private processRequest(req: HttpRequest<any>): HttpRequest<any> {
  //   // 先注入 messageReplayTime 到URL参数
  //   const reqWithTime = this.injectMessageReplayTime(req);

  //   // 检查环境变量中的加密开关
  //   if (!environment.encryption?.enabled || this.isInWhitelist(reqWithTime.url)) {
  //     return reqWithTime;
  //   }
  //   return this.encryptRequest(reqWithTime);
  // }

  /**
   * 处理请求（加密等预处理）
   */
  private processRequest(req: HttpRequest<any>): HttpRequest<any> {
    // 检查环境变量中的加密开关
    if (!environment.encryption?.enabled || this.isInWhitelist(req.url)) {
      return req;
    }
    return this.encryptRequest(req);
  }

  /**
   * 检查是否为离线状态且URL可缓存
   */
  private isOfflineAndCacheable(url: string): boolean {
    return this.networkService.isOffline() && this.isCacheable(url);
  }

  /**
   * 处理离线请求（从缓存读取）
   */
  private handleOfflineRequest(url: string): Observable<HttpEvent<any>> {
    return this.storage.get(url).pipe(
      mergeMap(cachedResponse =>
        cachedResponse
          ? of(new HttpResponse({ status: 200, body: cachedResponse }))
          : throwError({ status: 0, message: 'No network and no cached data' })
      )
    );
  }

  /**
   * 处理在线请求
   */
  private handleOnlineRequest(processedRequest: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(processedRequest).pipe(
      this.decryptResponse(),
      this.processResponse(),
      this.cacheResponse(processedRequest.url),
      catchError(this.handleError)
    );
  }

  /**
   * 统一响应处理（合并认证和包验证）
   */
  private processResponse = () => (source: Observable<any>) => source.pipe(
    mergeMap(async (event: HttpEvent<any>) => {
      if (event instanceof HttpResponse) {
        const body = event.body;

        // 处理认证错误
        if (body?.code && this.authErrorCodes.has(body.code)) {
          if (body.code === 9990) {
            // Toast提示
            await this.toastSer.presentToast(body.msg, 'danger');
            // 关闭所有Modal
            await this.modalManager.closeAllModals();
          }
          this.nav.navigateRoot('/auth');
          return event;
        }

        // 处理业务错误
        if (body && body.hasOwnProperty('code') && body.hasOwnProperty('msg') && body.code !== 0) {
          this.handleBusinessError(event, body);
          throw new HttpErrorResponse({
            error: body,
            status: event.status,
            statusText: 'Business Error',
            url: event.url,
          });
        }
      }
      return event;
    })
  )

  /**
   * 处理业务错误
   */
  private handleBusinessError(event: HttpResponse<any>, body: any): void {
    const isLoginRequest = event.url?.includes('/login');

    // 非登录接口显示错误提示
    if (!isLoginRequest) {
      this.toastSer.presentToast(body.msg, 'danger');
    }

    // 处理特殊错误码
    const specialError = this.specialErrorCodes.get(body.code);
    if (specialError && body.msg === specialError.message) {
      specialError.action();
    }
  }

  /**
   * 缓存响应数据
   */
  private cacheResponse = (url: string) => (source: Observable<any>) => source.pipe(
    tap(event => {
      if (event instanceof HttpResponse && this.isCacheable(url)) {
        this.storage.set(url, event.body).subscribe({
          next: () => console.log('Cache updated'),
          error: err => console.error('Error caching data', err)
        });
      }
    })
  )

  /**
   * 错误处理
   */
  private handleError = (error: any) => {
    if (error instanceof HttpResponse) {
      const errBody = error.body;
      if (errBody && errBody.hasOwnProperty('code') && errBody.hasOwnProperty('message')) {
        // 错误提示
        // this.toastSer.presentToast(errBody.message, 'danger');
        return throwError({
          status: error.status,
          message: errBody.message,
          data: errBody.data
        });
      }
    }
    return throwError(error);
  }

  /**
   * 判断URL是否需要缓存
   */
  private isCacheable(url: string): boolean {
    return this.cacheUrls.has(url);
  }

  /**
   * 检查URL是否在加密白名单中
   */
  private isInWhitelist(url: string): boolean {
    return Array.from(this.encryptionWhitelist).some(whitelistUrl => url.includes(whitelistUrl));
  }

  /**
   * 对请求进行加密处理（优化版，移除明文参数）
   */
  private encryptRequest(req: HttpRequest<any>): HttpRequest<any> {
    try {
      const urlParams = this.extractUrlParams(req);
      const bodyParams = this.extractBodyParams(req.body);

      // 记录加密前的原始数据
      this.debugLogger.logOriginalRequest(req.url, urlParams, bodyParams);

      let encryptedRequest = req;

      // 加密URL参数并清空明文params
      if (urlParams) {
        const encryptedUrl = this.replaceUrlParams(req.url, { cip: this.aesEncryption.encryptData(urlParams) });
        encryptedRequest = req.clone({ url: encryptedUrl, params: new HttpParams() });
      }

      // 加密Body参数
      if (bodyParams) {
        const encryptedBody = this.encryptBodyParams(bodyParams);
        encryptedRequest = encryptedRequest.clone({ body: encryptedBody });
      }

      // 记录加密后的数据
      this.debugLogger.logEncryptedRequest(req.url, encryptedRequest.url, encryptedRequest.body);

      return encryptedRequest;
    } catch (error) {
      console.error('加密请求时发生错误:', error);
      return req;
    }
  }

  /**
   * 提取URL参数（支持req.url和req.params）
   */
  private extractUrlParams(req: HttpRequest<any>): any {
    const urlObj = new URL(req.url, 'http://localhost');
    const params: any = {};
    urlObj.searchParams.forEach((value, key) => params[key] = value);
    if (req.params && typeof req.params.keys === 'function') {
      req.params.keys().forEach(key => {
        params[key] = req.params.get(key);
      });
    }
    return Object.keys(params).length > 0 ? params : null;
  }

  /**
   * 提取Body参数
   */
  private extractBodyParams(body: any): any {
    return body && typeof body === 'object' && Object.keys(body).length > 0 ? body : null;
  }

  /**
   * 加密URL参数
   */
  private encryptUrlParams(url: string, params: any): string {
    const encryptedParams = this.aesEncryption.encryptData(params);
    return this.replaceUrlParams(url, { cip: encryptedParams });
  }

  /**
   * 加密Body参数
   */
  private encryptBodyParams(params: any): any {
    const encryptedParams = this.aesEncryption.encryptData(params);
    return { cip: encryptedParams };
  }

  /**
   * 替换URL中的查询参数（返回完整URL，含协议、主机、端口、路径和参数）
   */
  private replaceUrlParams(url: string, newParams: any): string {
    const urlObj = new URL(url, 'http://localhost');
    urlObj.search = '';
    Object.entries(newParams).forEach(([key, value]) => {
      urlObj.searchParams.set(key, value as string);
    });
    return urlObj.toString();
  }

  /**
   * 响应解密处理（优化版本）
   */
  private decryptResponse = () => (source: Observable<any>) => source.pipe(
    map((event: HttpEvent<any>) => {
      if (event instanceof HttpResponse && this.isEncryptedResponse(event.body)) {
        try {
          const decryptedData = this.performResponseDecryption(event.body);
          // 记录解密后的数据
          this.debugLogger.logDecryptedResponse(event.url || '', decryptedData);
          return event.clone({ body: decryptedData });
        } catch (error) {
          console.error('响应解密失败:', error);
        }
      }
      return event;
    })
  )

  /**
   * 检查响应是否为加密数据
   */
  private isEncryptedResponse(body: any): boolean {
    return body?.cip && typeof body.cip === 'string';
  }

  /**
   * 执行响应解密
   */
  private performResponseDecryption(body: any): any {
    const decryptedData = this.aesEncryption.decryptData(body.cip);
    const { cip, ...otherFields } = body;
    return { ...otherFields, ...decryptedData };
  }
}