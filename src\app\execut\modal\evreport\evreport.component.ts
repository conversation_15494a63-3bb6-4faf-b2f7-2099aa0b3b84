import { Component, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { DetailsMode } from 'src/app/@core/base/environment';
import { EvReportInfo } from '../../class/evreport';
import { ShareModuleService } from 'src/app/share/share.service';
import { RequestResult } from 'src/app/@core/base/request-result';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';

@Component({
  selector: 'app-evreport',
  templateUrl: './evreport.component.html',
  styleUrls: ['./evreport.component.scss']
})
export class EvreportComponent implements OnInit, OnDestroy {
  selectedTab: string = 'basic';
  // 数据模型
  @Input() modelInfo: EvReportInfo = new EvReportInfo();
  // 数据模式
  @Input() modelMode: DetailsMode;
  @Input() coordinate: any; //坐标
  @Input() isModal = false; //是否弹窗
  basicInfo: any;
  // 基础信息是否保存
  isSave: boolean = false;

  destroy$ = new Subject<void>();
  constructor(
    private modalCtrl: ModalController, private shareNetSer: ShareModuleService,
    private toastSer: ToastService, private userSer: UserInfoService
  ) { }

  ngOnInit(): void {
    if (this.modelMode !== DetailsMode.ADD) {
      this.basicInfo = this.modelInfo;
    }
  }

  /**
   * 切换tab
   * @param tabId
   */
  switchTab(tabId: string): void {
    this.selectedTab = tabId;
  }

  eventCodeChange(basicInfo: any) {
    this.isSave = true;
    this.basicInfo = basicInfo;
  }

  /**
   * 消除隐患
   */
  remove(): void {
    const params = {
      eventCode: this.basicInfo.eventCode,
      eventStatus: '已消除'
    }
    this.shareNetSer.putRequest(params, null, { interfaceUrl: '/work-inspect/api/v2/inspect/event/status' })
      .pipe(takeUntil(this.destroy$)).subscribe((result: RequestResult<any>) => {
        const { code, msg } = result;
        if (code === 0) {
          this.toastSer.presentToast('事件已消除', 'success');
          this.modalCtrl.dismiss('refresh');
        } else {
          this.toastSer.presentToast(msg, 'danger');
        }
      })
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 判断是否可以消除隐患
   */
  isCanRemove(): boolean {
    return this.userSer.roleType.includes('事件处理员')
      && this.modelInfo.eventStatus === '未消除'
      && this.modelMode === '查看';
  }

  /**
   * 页面关闭
   */
  goBack(): void {
    this.isSave ? this.modalCtrl.dismiss(this.basicInfo, 'confirm') : this.modalCtrl.dismiss();
  }

  @HostListener('document:ionBackButton', ['$event'])
  backButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalCtrl.dismiss();
    });
  }

}