import { ChangeDetector<PERSON><PERSON>, Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Nav<PERSON><PERSON>roller, ToastController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { MapComponent } from '../../share/map-component/map/map.component';
import { MapService } from '../../share/map-component/service/map.service';

// 关键点数据模型
export interface KeyPoint {
  id: string;
  pointCode?: string;
  pointName: string;
  name: string;
  type: 'vehicle' | 'person';
  coordinate?: [number, number];
  isRainyDay: boolean;
  isItRaining: string;
  state: string;
  status?: 'inspected' | 'pending';
  point: string;
  bufferRange: number;
  geom?: any;
  lastInspectionTime?: string;
  description?: string;
}

// 筛选条件模型
export interface FilterOptions {
  inspectionType: 'all' | 'vehicle' | 'person';
  showRainyDay: boolean;
  depCode?: string;
  pointName?: string;
}

@Component({
  selector: 'app-keypoint-view',
  templateUrl: './keypoint-view.page.html',
  styleUrls: ['./keypoint-view.page.scss'],
})
export class KeypointViewPage implements OnInit, OnDestroy {
  @ViewChild('mapComponent') mapComponent: MapComponent;

  selectedTab = 'map';

  filter: FilterOptions = {
    inspectionType: 'all',
    showRainyDay: false
  };

  layerIds: string[] = ['p_pipe_joint_info', 'inspect_point'];
  loading = false;
  legendItems = [
    {
      type: 'vehicle',
      label: '巡视',
      color: '#007bff',
      icon: 'radio-button-off'
    },
    {
      type: 'person', 
      label: '巡查',
      color: '#28a745',
      icon: 'radio-button-off'
    },
    {
      type: 'rainy',
      label: '巡检天气',
      color: '#ffc107',
      icon: 'radio-button-off'
    }
  ];

  private destroy$ = new Subject<void>();

  constructor(
    private nav: NavController,
    private cdr: ChangeDetectorRef,
    private mapService: MapService,
    private toastController: ToastController
  ) { }

  ngOnInit() {
    this.updateLayerIds(this.filter);
    setTimeout(() => {
      this.fitToDefaultExtent();
      // 注册地图组件到地图服务
      if (this.mapComponent) {
        this.mapService.registerMapComponent(this.mapComponent);
      }
    }, 500);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  goBack(): void {
    this.nav.back();
  }

  onTabChange(event: any) {
    this.selectedTab = event.detail.value;

    if (this.selectedTab === 'map' && this.mapComponent) {
      setTimeout(() => {
        // 注册地图组件到地图服务
        this.mapService.registerMapComponent(this.mapComponent);
        this.forceRefreshMapLayers();
      }, 100);
    }
  }

  onFilterChange(filter: Partial<FilterOptions>) {
    this.filter = { ...this.filter, ...filter };
    this.updateLayerIds(this.filter);

    if (this.mapComponent) {
      setTimeout(() => {
        this.forceRefreshMapLayers();
      }, 100);
    }

    this.cdr.detectChanges();
  }

  onLayerExtentRequest(layerId: string) {
    if (this.mapComponent) {
      this.forceRefreshMapLayers();
    }
  }

  /**
   * 关键点定位事件处理
   * @param keyPoint 关键点数据
   */
  onKeyPointLocate(keyPoint: KeyPoint) {
    try {
      let longitude: number;
      let latitude: number;

      // 优先使用coordinate字段
      if (keyPoint.coordinate && keyPoint.coordinate.length >= 2) {
        longitude = keyPoint.coordinate[0];
        latitude = keyPoint.coordinate[1];
      }
      // 如果没有coordinate，尝试从geom字段解析
      else if (keyPoint.geom) {
        try {
          const geom = typeof keyPoint.geom === 'string' ? JSON.parse(keyPoint.geom) : keyPoint.geom;
          if (geom.type === 'Point' && geom.coordinates && geom.coordinates.length >= 2) {
            longitude = geom.coordinates[0];
            latitude = geom.coordinates[1];
          }
        } catch (error) {
          console.warn('解析关键点坐标失败:', keyPoint.pointCode, error);
          this.showToast('无法获取关键点坐标信息');
          return;
        }
      }

      // 验证坐标有效性
      if (!longitude || !latitude || longitude === 0 || latitude === 0) {
        this.showToast('关键点坐标信息无效');
        return;
      }

      const coordinate: [number, number] = [longitude, latitude];

      // 切换到地图tab
      this.selectedTab = 'map';

      // 等待地图tab切换完成后再进行定位
      setTimeout(() => {
        // 确保地图组件已注册
        if (this.mapComponent) {
          this.mapService.registerMapComponent(this.mapComponent);
        }

        // 清除之前的标记
        this.mapService.clearMarkers();

        // 在坐标位置添加蓝色点标记
        this.mapService.addImageMarker(coordinate, '/assets/map/point_blue.png', 0.2);

        // 使用地图服务移动地图到关键点位置，设置合适的缩放级别
        this.mapService.moveMapToCoordinate(coordinate, 1000, 18);

        // 显示定位成功提示
        this.showToast(`已定位到关键点: ${keyPoint.pointName}`);
      }, 200);

    } catch (error) {
      console.error('定位关键点失败:', error);
      this.showToast('定位失败，请重试');
    }
  }

  /**
   * 显示提示信息
   */
  private async showToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 2000,
      position: 'bottom'
    });
    await toast.present();
  }


  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event: any): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.goBack();
    });
  }
  /**
   * 强制刷新地图图层
   */
  private forceRefreshMapLayers(): void {
    if (!this.mapComponent || !this.mapComponent.map) {
      return;
    }

    try {
      this.mapComponent.layerIds = [...this.layerIds];
      const map = this.mapComponent.map;
      this.mapComponent.businessLayerList.clear();
      this.mapComponent['initBusinessLayers'](this.layerIds);
      map.updateSize();
      map.renderSync();

      setTimeout(() => {
        this.fitToDefaultExtent();
      }, 500);
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 移动视野到固定范围
   */
  private fitToDefaultExtent(): void {
    try {
      if (!this.mapComponent || !this.mapComponent.map) {
        return;
      }

      const view = this.mapComponent.map.getView();
      const defaultExtent = [112.16695404052734, 35.32004928588867, 113.22920989990234, 36.11748504638672];

      view.fit(defaultExtent, {
        duration: 1000,
        padding: [50, 50, 50, 50],
        maxZoom: 18
      });
    } catch (error) {
      // 静默处理错误
    }
  }

  private updateLayerIds(filter: FilterOptions): void {
    const baseLayerIds = ['p_pipe_joint_info'];

    switch (filter.inspectionType) {
      case 'vehicle':
        this.layerIds = [...baseLayerIds, 'inspect_vehicle'];
        break;
      case 'person':
        this.layerIds = [...baseLayerIds, 'inspect_person'];
        break;
      default:
        this.layerIds = [...baseLayerIds, 'inspect_point'];
        break;
    }
  }
}