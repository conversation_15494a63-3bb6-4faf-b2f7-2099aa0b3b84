import { Injectable } from '@angular/core';
import { ScreenOrientation } from '@ionic-native/screen-orientation/ngx';
import { ToastService } from './toast.service';

@Injectable({ providedIn: 'root' })
export class OrientationService {
  constructor(
    private screenOrientation: ScreenOrientation, private toastSer: ToastService
  ) { }

  /**
   * 锁定横屏
   */
  async lockLandscape() {
    try {
      await this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.LANDSCAPE);
    } catch (e) {
      this.toastSer.presentToast('锁定横屏失败！', 'danger');
    }
  }

  /**
   * 锁定竖屏
   */
  async lockPortrait() {
    try {
      await this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.PORTRAIT);
    } catch (e) {
      this.toastSer.presentToast('恢复竖屏失败！', 'danger');
    }
  }
}