.operate-col {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  white-space: nowrap;
  text-align: center;
  justify-content: center;
  align-items: center;
  display: flex;

  .operate-btn {
    height: 24px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    ion-icon {
      font-size: 18px;
    }
  }
}

ion-grid {
  width: 100%;
  padding: 0;

  .header {
    background-color: #f2f5fc;
    font-weight: 500;
    border-bottom: 1px solid #ddd;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 2;
    height: 42px;
    min-height: 42px;
    line-height: 42px;

    ion-col {
      padding: 0 4px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .row {
    border-bottom: 1px solid #eee;
    font-size: 12px;

    ion-col {
      padding: 6px 4px;
      line-height: 1.2;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 搜索框样式优化
ion-searchbar {
  --background: var(--ion-color-light, #f4f5f8);
  --border-radius: 8px;
  --box-shadow: none;
  --placeholder-color: var(--ion-color-medium, #92949c);
  --color: var(--ion-color-dark, #222428);
  padding: 8px 16px;
  margin-right: 8px; // 添加右侧间距
}

ion-toolbar {
  --padding-start: 0;
  --padding-end: 0;

  ion-buttons {
    margin-right: 8px;

    ion-button {
      --padding-start: 8px;
      --padding-end: 8px;
      --padding-top: 4px;
      --padding-bottom: 4px;
      height: auto;
      margin: 0;
    }
  }
}