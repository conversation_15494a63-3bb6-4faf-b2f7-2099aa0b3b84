@import "../../theme/variables.scss";
@import "../../theme/dialog.scss";

.work-header {
  background: var(--ion-color-primary);
  padding: 12px 0;
}

::ng-deep.header-background {
  background: var(--ion-color-primary);
}

.search-bar {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  padding-left: 16px;
  position: relative;

  p {
    height: 40px;
    line-height: 40px;
    position: absolute;
    font-size: 14px;
    right: 16px;
    color: #fff;
  }
}

.search-bar-parent {
  color: black;
  width: 70%;
  height: 40px;
  padding: 0 15px;
  font-size: 14px;
  background-color: #fff;
  border-radius: 3px;
  display: flex;
  align-items: center;

  ::-webkit-input-placeholder {
    color: #666666;
  }

  display: flex;
  align-items: center;

  .login-form-input-icon {
    padding-right: 8px;
    text-align: center;
    height: 50%;
  }

  ion-input {
    padding-left: 6px;
  }
}

ion-content {
  height: calc(100vh - 64px);

  ion-grid {
    background: #f6f6f6;
    font-size: 12px;
    padding: 0px;

    ion-row {
      background-color: white;

      .title-status-container {
        display: flex;
        width: 100%;
        padding: 5px 12px;
        align-items: center;
      }

      .title-wrapper {
        flex: 1;
        min-width: 0;
      }

      .status-section {
        flex-shrink: 0;
        display: flex;
        justify-content: flex-end;
      }

      span {
        display: block;
        margin-left: 12px;
        padding: 5px 0px;
      }

      .title {
        font-weight: 600;
        font-size: 14px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 0;

        .task-p {
          width: 6px;
          height: 20px;
          background-color: var(--ion-color-secondary);
          float: left;
          border-radius: 16px;
          margin: 0 10px 0 0;
        }
      }

      .content {
        border-top: 1px solid #f6f6f6;
      }
    }
  }

  .status-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .status-chip {
      margin: 0;
      height: 24px;

      ion-icon {
        margin-right: 4px;
      }
    }
  }
}

.task-start {
  height: 14px;
  position: relative;
  top: -30px;

  ion-button {
      position: absolute;
      right: 12px;
      font-size: 12px;
  }
}