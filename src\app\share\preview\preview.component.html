<!-- 图片预览 -->
<div class="outerdiv" [ngClass]="{'card-mode': isCardMode}">
	<div class="header-row">
		<div class="title">{{ title }}</div>
		<div #closeBtn class="closepic" [ngClass]="{'card-mode': isCardMode}" (click)="closePic()"></div>
	</div>
	<div class="img-container">
		<img #previewImg class="bigimg" [ngClass]="{'card-mode': isCardMode}" [src]="src" [style.transform]="getTransformStyle()" />
		<div *ngIf="showClearButton" class="clear-btn-float" [ngClass]="{'card-mode': isCardMode}" (click)="onClearClick()">
			<ion-icon name="color-wand-outline" color="success"></ion-icon>
		</div>
	</div>
</div>