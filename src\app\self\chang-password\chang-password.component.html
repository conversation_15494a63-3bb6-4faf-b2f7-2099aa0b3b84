<!-- 修改密码 -->
<ion-header class="sticky">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()" *ngIf="!showClose">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>修改密码</ion-title>
    <ion-buttons slot="end">
      <ion-button></ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<form [formGroup]="infoFormGroup" (ngSubmit)="onChangPassword()">
  <ion-content>
    <ost-form-list [formGroup]="infoFormGroup">
      <ost-form-item [required]="true">
        <ion-label>账号:</ion-label>
        <ion-input formControlName="account"></ion-input>
        <ost-error errorCode="required">
          账号不能为空
        </ost-error>
      </ost-form-item>

      <ost-form-item [required]="false">
        <ion-label>旧密码:</ion-label>
        <ion-input formControlName="oldPassword" [type]=pwShow></ion-input>
        <ion-icon
          class="login-form-input-icon"
          [name]=isShow
          (click)="onPasswordShow()"
        ></ion-icon>
        <ost-error errorCode="required">
          旧密码不能为空
        </ost-error>
      </ost-form-item>

      <ost-form-item>
        <ion-label>新密码:</ion-label>
        <ion-input formControlName="password" [type]=pwShow></ion-input>
        <ost-error errorCode="required"
          >密码不能为空</ost-error
        >
        <ost-error errorCode="password"
          >密码强度太低</ost-error
        >
        <ost-error errorCode="notSameAsOldPassword">
          新密码不能与旧密码一致
        </ost-error>
      </ost-form-item>

      <ost-form-item>
        <ion-label>确认密码:</ion-label>
        <ion-input
          formControlName="confirmpassword"
          [type]=pwShow
        ></ion-input>
        <ost-error errorCode="required"
          >密码不能为空</ost-error
        >
        <ost-error errorCode="match"
          >两次输入的密码不一致</ost-error
        >
      </ost-form-item>
    </ost-form-list>
    <ion-item lines="none">
      <ion-label style="font-size:13px;">
        <ion-text color="danger">
          密码长度大于6位，包含数字、字母及!&#64;#$%^&*?特殊字符
        </ion-text>
      </ion-label>
    </ion-item>
  </ion-content>

  <ion-footer>
    <ion-toolbar>
      <ion-button type="submit" expand="block"
        [disabled]="!infoFormGroup.valid"
      >
        修改密码
      </ion-button>
    </ion-toolbar>
  </ion-footer>

</form>
