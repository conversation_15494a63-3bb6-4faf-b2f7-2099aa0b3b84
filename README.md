# 山西气田巡检应用 (xj-app)

> **当前版本**: v1.1.5  
> **最后更新**: 2025-01-30

## 📱 应用简介

新疆气田巡检应用是一款专为气田巡检作业设计的移动端应用，支持实时轨迹跟踪、关键点采集、事件上报等核心功能。

## ✨ 主要功能

- 🗺️ **实时轨迹跟踪**: 高精度GPS轨迹记录和实时显示
- 📍 **关键点采集**: 智能定位策略，支持高精度位置采集
- 📊 **事件上报**: 现场异常情况快速上报
- 📷 **多媒体记录**: 支持照片、视频、音频记录
- 🔄 **离线同步**: 网络恢复时自动同步数据
- 📱 **响应式设计**: 适配各种移动设备

## 🚀 最新更新 (v1.1.5)

### 🐛 重要修复
- **解决轨迹中断问题**: 修复了在巡检任务执行过程中添加关键点导致轨迹跟踪中断的问题
- **优化定位策略**: 实现智能定位策略，任务执行中使用实时坐标，提高响应速度和精度

### ⚡ 性能优化
- **代码重构**: LocationSelectComponent 优化，减少 10% 代码量
- **资源优化**: 减少不必要的定位请求，降低电池消耗
- **稳定性增强**: 消除双重位置监听冲突，提高系统稳定性

### 🎨 用户体验
- **统一提示**: 关键点采集提示信息统一化
- **操作一致**: 保持用户操作流程的一致性
- **响应优化**: 任务执行中关键点采集响应更快

## 🛠️ 技术栈

- **框架**: Ionic + Angular
- **地图**: OpenLayers
- **定位**: Cordova Background Geolocation
- **存储**: SQLite + IndexedDB
- **网络**: HTTP + WebSocket
- **构建**: Angular CLI + Cordova

## 📋 系统要求

- **Android**: 6.0+ (API Level 23+)
- **iOS**: 12.0+
- **权限**: 位置权限、相机权限、存储权限

## 🔧 开发环境

```bash
# 安装依赖
npm install

# 开发模式运行
ionic serve

# 构建生产版本
ionic build --prod

# 添加平台
ionic capacitor add android
ionic capacitor add ios

# 运行到设备
ionic capacitor run android
ionic capacitor run ios
```

## 📁 项目结构

```
src/
├── app/
│   ├── execut/                 # 巡检执行模块
│   │   ├── services/          # 定位服务等
│   │   ├── control-bar/       # 控制栏组件
│   │   └── execut.component.ts # 主执行组件
│   ├── share/                 # 共享组件
│   │   └── map-component/     # 地图相关组件
│   └── @core/                 # 核心服务
├── environments/              # 环境配置
└── docs/                     # 文档
    ├── 轨迹中断问题修复方案.md
    └── 更新日志.md
```

## 🧪 测试

### 功能测试
```bash
# 轨迹连续性测试
1. 开始巡检任务
2. 徒步行走一段距离
3. 添加关键点并完成提交
4. 继续行走，验证轨迹连续性

# 关键点采集测试
1. 任务执行中添加关键点
2. 任务未开始时添加关键点
3. 各种关闭方式测试
```

## 📖 文档

- [轨迹中断问题修复方案](./docs/轨迹中断问题修复方案.md)
- [更新日志](./docs/更新日志.md)

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用私有许可证，仅供内部使用。

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本应用包含敏感的位置信息和业务数据，请确保在安全的环境中使用。
