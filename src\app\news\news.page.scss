.notification-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin: 12px;
  padding: 12px;
  border-left: 4px solid #4285f4;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  &.read {
    border-left: 4px solid #e0e0e0;
    opacity: 0.8;
  }
}

.notification-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.notification-icon {
  color: #4285f4;
  font-size: 20px;
  margin-right: 10px;
  background: rgba(66, 133, 244, 0.1);
  padding: 6px;
  border-radius: 8px;
}

.notification-title {
  font-weight: 600;
  flex: 1;
  font-size: 15px;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
  --background: transparent;
  --ripple-color: transparent;
  height: 32px;

  ion-label {
    margin: 0;
    font-size: 13px;
    color: #4285f4;
    font-weight: 500;
  }

  .notification-time {
    margin-right: 12px;
    color: #999999;
    font-size: 12px;
    font-weight: 400;
  }
}

// 添加动画效果
@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.notification-card {
  animation: slideIn 0.3s ease-out;
}