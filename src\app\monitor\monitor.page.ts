import { Component, HostListener, OnInit } from '@angular/core';
import { ModalController, NavController } from '@ionic/angular';
import { MonitorDetailComponent } from './detail/detail.component';

@Component({
  selector: 'app-monitor',
  templateUrl: './monitor.page.html',
  styleUrls: ['./monitor.page.scss'],
})
export class MonitorPage implements OnInit {
  constructor(
    public nav: NavController,
    public modalController: ModalController
  ) { }

  ngOnInit(): void {
  }

  /**
   * 菜单点击事件
   */
  async onOrgMenu(event: any): Promise<void> {
    const modal = await this.modalController.create({
      component: MonitorDetailComponent,
      componentProps: {
        depCode: event.data.depCode,
        depName: event.data.depName
      }
    });
    await modal.present();
  }
  /**
   * 回退
   */
  goBack(): void {
    this.nav.back();
  }
  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.nav.back();
    });
  }

}
