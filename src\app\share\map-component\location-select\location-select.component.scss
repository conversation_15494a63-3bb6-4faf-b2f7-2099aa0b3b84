ion-label{
    margin-top: 4px;
    margin-bottom: 4px;
}

.collect-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  touch-action: none;
}

.mask-content {
  text-align: center;
  pointer-events: auto;
  padding: 32px;
  max-width: 320px;
}

.collect-info {
  margin-bottom: 32px;

  h3 {
    margin: 16px 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }

  p {
    margin: 0;
    font-size: 16px;
    color: var(--ion-color-medium);
    line-height: 1.4;
  }
}

.mask-content ion-button {
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 24px;
  --padding-end: 24px;
  font-size: 16px;
  font-weight: 600;
  --box-shadow: 0 4px 12px rgba(56, 128, 255, 0.3);
}

.countdown-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;

  p {
    margin: 0;
    font-size: 16px;
    color: var(--ion-color-medium);
  }
}

.countdown {
  font-size: 72px;
  font-weight: bold;
  color: #3880ff;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
  from {
    transform: scale(1);
    opacity: 0.8;
  }
  to {
    transform: scale(1.1);
    opacity: 1;
  }
}



// Segment样式优化
ion-segment-button {
  position: relative;

  ion-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 10px;
    min-width: 16px;
    height: 16px;
    border-radius: 50%;
  }
}

// 精度提示条样式 - 极简紧凑设计
.accuracy-tip-banner {
  position: absolute;
  top: 16px;
  left: 200px; // 避开位置信息卡片（约160px宽度 + 间距）
  right: 16px;
  z-index: 999; // 略低于位置信息卡片，避免遮挡

  .tip-content {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 193, 7, 0.95); // 半透明警告色背景
    backdrop-filter: blur(10px);
    border-radius: 6px;
    padding: 6px 10px; // 极小的内边距
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    border: 1px solid rgba(255, 152, 0, 0.5);
    font-size: 12px;
    color: #8b4513; // 深棕色文字，在黄色背景上更清晰
    font-weight: 500;

    ion-icon {
      font-size: 14px;
      color: #ff8c00; // 橙色图标
      flex-shrink: 0; // 防止图标被压缩
    }

    span {
      flex: 1;
      white-space: nowrap; // 防止文字换行
      overflow: hidden;
      text-overflow: ellipsis; // 超长文字显示省略号
    }

    ion-button {
      margin: 0;
      --padding-start: 2px;
      --padding-end: 2px;
      --padding-top: 2px;
      --padding-bottom: 2px;
      min-height: 18px;
      min-width: 18px;
      flex-shrink: 0; // 防止按钮被压缩

      ion-icon {
        font-size: 12px;
        color: #8b4513; // 与文字颜色一致
      }
    }
  }
}

// 响应式设计 - 在小屏幕上调整布局
@media (max-width: 480px) {
  .accuracy-tip-banner {
    left: 16px;
    top: 110px; // 在小屏幕上移到位置信息卡片下方
    right: 16px;

    .tip-content {
      font-size: 11px;
      padding: 5px 8px;

      span {
        font-size: 11px;
      }

      ion-icon {
        font-size: 12px;
      }
    }
  }
}

// 添加淡入动画效果
.accuracy-tip-banner {
  animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}