<span *ngIf="listItem.group">
  <ion-icon
    class="tree-list-icon ion-padding-end"
    [name]="listItem.icon"
    *ngIf="listItem.icon"
  ></ion-icon>
  {{ listItem.title }}
</span>
<!-- 子级菜单 -->
<a
  *ngIf="!listItem.children && !listItem.group"
  [attr.title]="listItem.title"
  [class.active]="listItem.selected"
  (click)="onItemClick(listItem)"
>
  <ion-icon
    class="tree-list-icon ion-padding-end"
    style="font-size:20px;"
    [name]="getSuffix(listItem.title)?'image-outline' : 'document-text-outline'"
    *ngIf="listItem.data.menuType === 0"
  ></ion-icon>
  <span class="tree-list-title">{{ listItem.title }} </span>
  <ion-button *ngIf="listItem.data.menuType === 0" 
    style="padding-right: 16px;" fill="clear"
    (click)="onItemBtnClick(listItem)">
    <ion-icon [name]="listItem.data.icon"></ion-icon>
  </ion-button>
  <span
    *ngIf="showSubBtn"
    style="padding-right: 16px;"
  >
    {{subBtnText}}
  </span>
</a>
<!-- 父级目录 -->
<a
  *ngIf="listItem.children"
  (click)="onSelectItem(listItem)"
  (click)="$event.preventDefault(); onToggleSubMenu(listItem)"
  [attr.title]="listItem.title"
  [class.active]="listItem.selected"
>
  <ion-icon
    class="tree-list-icon ion-padding-end"
    style="font-size:20px;"
    name="folder-open-outline"
    *ngIf="listItem.data.menuType === 1"
  >
  </ion-icon>
  <span class="tree-list-title">{{ listItem.title }} </span>
  <span
    *ngIf="showDirBtn"
    style="padding-right: 16px;"
    (click)="onSubItemClick(listItem)"
  >
    {{dirBtnText}}
  </span>
  <ion-icon
    class="expand-state ion-padding-end"
    [name]="getExpandStateIcon()"
  ></ion-icon>
</a>
<ul
  *ngIf="listItem.children"
  [class.collapsed]="!(listItem.children && listItem.expanded)"
  [class.expanded]="listItem.expanded"
  [@toggle]="toggleState"
  class="tree-list-itmes"
>
  <ng-container *ngFor="let item of listItem.children">
    <li
      TreeListItem
      [hidden]="item.hidden"
      [showSubBtn]="showSubBtn"
      [subBtnText]="subBtnText"
      [showDirBtn]="showDirBtn"
      [dirBtnText]="dirBtnText"
      [showSubIcon]="showSubIcon"
      [listItem]="item"
      [class.menu-group]="item.group"
      (toggleSubItem)="onToggleSubMenu($event)"
      (selectItem)="onSelectItem($event)"
      (itemClick)="onItemClick($event)"
      (subItemClick)="onSubItemClick($event)"
      (itemBtnClick)="onItemBtnClick($event)"
      class="tree-list-itme"
    ></li>
  </ng-container>
</ul>