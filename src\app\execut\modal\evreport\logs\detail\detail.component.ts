import { Component, HostListener, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ModalController, ToastController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DetailsMode } from 'src/app/@core/base/environment';
import { LogInfo } from 'src/app/execut/class/evreport';
import { ExecutService } from 'src/app/execut/execut.service';

@Component({
  selector: 'logs-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss']
})
export class LogsDetailComponent implements OnInit, OnDestroy {
  @Input() modelInfo: LogInfo = new LogInfo();
  @Input() modelMode: DetailsMode;
  DetailsMode: typeof DetailsMode = DetailsMode;
  // 表单
  infoFormGroup: FormGroup;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    public modalController: ModalController, private fb: FormBuilder,
    public netSer: ExecutService, public toastController: ToastController,
  ) { }

  ngOnInit() {
    this.infoFormGroup = this.fb.group({
      eventCode: [this.modelInfo.eventCode],
      userName: [this.modelInfo.userName],
      eventType: [this.modelInfo.eventType, [Validators.required]],
      status: [this.modelInfo.status, [Validators.required]],
      describe: [this.modelInfo.describe],
    });
  }

  /**
   * 处理图片数据
   * @param images 图片数据
   */
  handleBase64ImageDataChange(images: any) {
    this.modelInfo.fileCodes = images;
  }

  onSubmit() {
    if (!this.infoFormGroup.valid) return;
    const formData = Object.assign(this.modelInfo, this.infoFormGroup.value);
    const operate = this.modelMode === DetailsMode.ADD
      ? this.netSer.saveLogs(formData)
      : this.netSer.updateLogs(formData)
    operate
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.netSucces, this.netError);
  }

  netSucces = (result) => {
    if (result.code === 0) {
      this.errorPresentToast(result.msg, 'success');
      this.modalController.dismiss(true);
    }
  }

  netError = (error) => {
    const err = error.body;
    err.msg ? this.errorPresentToast(err.msg) : this.errorPresentToast();
  }

  /**
   * 页面关闭
   */
  goBack(): void {
    this.modalController.dismiss();
  }

  @HostListener('document:ionBackButton', ['$event'])
  backButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalController.dismiss();
    });
  }

  /**
   * 错误提醒
   */
  async errorPresentToast(mgs = '网络连接错误', color = 'danger'): Promise<void> {
    const toast = await this.toastController.create({
      message: mgs,
      duration: 2000,
      color
    });
    toast.present();
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }
}
