import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

if (environment.production) {
  enableProdMode();
}

const elem: any = document.getElementsByClassName('preloader');
setTimeout(() => {
  elem[0].style.display = 'none';
}, 3000);
platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.log(err));
