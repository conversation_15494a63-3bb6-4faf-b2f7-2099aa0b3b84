import { Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BackgroundGeolocation, BackgroundGeolocationConfig } from '@ionic-native/background-geolocation/ngx';
import { ModalController, Platform, ToastController } from '@ionic/angular';
import { StorageService } from 'src/app/@core/providers/storage.service';

@Component({
  selector: 'app-gis-config',
  templateUrl: './gis-config.page.html',
  styleUrls: ['./gis-config.page.scss'],
})
export class GisConfigPage implements OnInit {

  // 表单
  infoFormGroup: FormGroup;
  // 配置信息
  gisConfigInfo: BackgroundGeolocationConfig;
  constructor(
    public platform: Platform,
    private storageService: StorageService,
    private bgGeol: BackgroundGeolocation,
    private fb: FormBuilder,
    public toastController: ToastController,
    public modalController: ModalController) {

  }

  ngOnInit(): void {
    // 初始化表单
    this.infoFormGroup = this.fb.group({
      desiredAccuracy: [0], // 期望准确度
      stationaryRadius: [0], // 静止半径
      distanceFilter: [0], // 最小间距
      interval: [0], // 位置更新之间的最小时间间隔（毫秒）
      syncThreshold: [0],
      maxLocations: [0], // 限制存储到db中的最大位置数。
      fastestInterval: [0],
      notificationTitle: [],
      notificationText: [],

    });
    // 初始配置
    this.storageService.get('GeolocationConfig').subscribe((ret: BackgroundGeolocationConfig) => {
      this.infoFormGroup.setValue(ret);
    });
  }

  /**
   * 修改配置
   */
  async onChangeConfig(): Promise<void> {
    // 立即生效
    if (this.platform.is('cordova')) {
      this.bgGeol.setConfig(this.infoFormGroup.value);
    }
    // 记载缓存里
    await this.storageService.set('GeolocationConfig', this.infoFormGroup.value);
    const toast = await this.toastController.create({
      message: '保存成功',
      duration: 2000,
      color: 'success'
    });
    toast.present();
    toast.onDidDismiss().then(ret => {
      this.onCloseDialo();
    });
  }
  /**
   * 关闭弹窗
   */
  onCloseDialo(): void {
    this.modalController.dismiss();
  }
  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalController.dismiss();
    });
  }
}
