<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="onClose()">
        <ion-icon slot="icon-only" name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="ion-text-center ion-no-padding">数据列表</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onConfirm()">
        <ion-icon slot="icon-only" name="checkmark-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ng-template #sourceContainer>
  </ng-template>
</ion-content>