import { Component, Input, OnInit } from '@angular/core';

export type IControlerItemStyle = 'square' | 'rectangle';
export type IControlerItemSize = 'xxxl' | 'xxl' | 'xl' | 'lg' | 'md' | 'sm' | 'xs';

@Component({
  selector: 'ost-controler-item',
  templateUrl: './controler-item.component.html',
  styleUrls: ['./controler-item.component.scss']
})
export class MapControlerItemComponent implements OnInit {
  @Input() ciStyle: IControlerItemStyle = 'square';
  @Input() ciSize: IControlerItemSize = 'sm';
  @Input() select = false;
  constructor() { }
  ngOnInit(): void {
  }
}
