<!-- 快速选择模式 -->
<ng-container *ngIf="showQuickRadio">
  <!-- 横向排列模板 -->
  <ion-radio-group *ngIf="direction === 'horizontal'" [(ngModel)]="value" (ngModelChange)="onChangeRadio($event)" class="quick-radio-group">
    <ion-item-divider *ngFor="let item of list" (click)="onRadioClick(item)">
      <ion-label>{{item.name}}</ion-label>
      <ion-radio slot="start" [value]="item.value" [ngClass]="{ 'no-pointer-events': disabled || readonly }"></ion-radio>
    </ion-item-divider>
  </ion-radio-group>

  <!-- 竖向排列模板 -->
  <ion-radio-group *ngIf="direction === 'vertical'" [(ngModel)]="value" (ngModelChange)="onChangeRadio($event)" class="quick-radio-group-vertical">
    <ion-item-divider *ngFor="let item of list" (click)="onRadioClick(item)">
      <ion-label>{{item.name}}</ion-label>
      <ion-radio slot="start" [value]="item.value" [ngClass]="{ 'no-pointer-events': disabled || readonly }"></ion-radio>
    </ion-item-divider>
  </ion-radio-group>
</ng-container>

<!-- 列表选择模式 -->
<ng-container *ngIf="!showQuickRadio">
  <ost-input-search
    [placeholder]="placeholder"
    [(ngModel)]="value"
    (ngModelChange)="onChangeRadio($event)"
    [disabled]="disabled"
    [icon]="icon"
    [isShowIcon]="isShowIcon"
    [readonly]="readonly"
    [required]="required">
    <ost-option-source
      [labelName]="labelName"
      [labelValue]="labelValue"
      [radioValue]="value"
      [radioList]="list">
    </ost-option-source>
  </ost-input-search>
</ng-container>