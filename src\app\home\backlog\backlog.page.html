<ion-card-content class="backlog">
  <header class="backlog-header">
    <h3 class="backlog-title">巡检任务待办</h3>
    <time class="backlog-date">{{nowDate}}</time>
  </header>

  <main class="backlog-content">
    <div class="loading-state" *ngIf="isLoading">
      <ion-spinner name="crescent"></ion-spinner>
      <span class="loading-text">加载中...</span>
    </div>

    <div class="task-sections" *ngIf="!isLoading; else emptyState">
      <section class="task-section inspection-section">
        <div class="section-header">
          <div class="section-title">
            <ion-icon name="person-outline" class="section-icon"></ion-icon>
            <span class="section-name">待巡查任务</span>
            <span class="section-stats">({{getInspectionStats()}})</span>
          </div>
          <button class="view-all-btn" (click)="goWork()">
            查看全部
          </button>
        </div>
        
        <div class="task-cards" *ngIf="inspectionTasks.length > 0; else inspectionEmptyState">
            <div 
              *ngFor="let task of inspectionTasks; trackBy: trackByTaskCode"
              class="task-card inspection-card"
              (click)="onTaskCardClick(task)"
            >
              <div class="task-row">
                <span class="task-name">{{task.taskName}}</span>
                <span class="task-progress">{{task.completionRate}}</span>
                <button 
                  class="action-btn"
                  (click)="onStartClick(task); $event.stopPropagation()"
                  *ngIf="isActiveTask(task.status)"
                >
                  {{task.status === '未执行' ? '开始' : '继续'}}
                </button>
              </div>
            </div>
          </div>
        
        <ng-template #inspectionEmptyState>
          <div class="section-empty">
            <span class="empty-text">暂无巡查任务</span>
          </div>
        </ng-template>
      </section>


      <section class="task-section patrol-section" style="border-top: 1px solid #f0f0f0;;">
        <div class="section-header">
          <div class="section-title">
            <ion-icon name="car-outline" class="section-icon"></ion-icon>
            <span class="section-name">待巡视任务</span>
            <span class="section-stats">({{getPatrolStats()}})</span>
          </div>
          <button class="view-all-btn" (click)="goWork()">
            查看全部
          </button>
        </div>
        
        <div class="task-cards" *ngIf="patrolTasks.length > 0; else patrolEmptyState">
            <div 
              *ngFor="let task of patrolTasks; trackBy: trackByTaskCode"
              class="task-card patrol-card"
              (click)="onTaskCardClick(task)"
            >
              <div class="task-row">
                <span class="task-name">{{task.taskName}}</span>
                <span class="task-progress">{{task.completionRate}}</span>
                <button 
                  class="action-btn"
                  (click)="onStartClick(task); $event.stopPropagation()"
                  *ngIf="isActiveTask(task.status)"
                >
                  {{task.status === '未执行' ? '开始' : '继续'}}
                </button>
              </div>
            </div>
          </div>
        
        <ng-template #patrolEmptyState>
          <div class="section-empty">
            <span class="empty-text">暂无巡视任务</span>
          </div>
        </ng-template>
      </section>
    </div>
  </main>

  <ng-template #emptyState>
    <div class="empty-state" *ngIf="!isLoading">
      <img src="assets/menu/box2.png" alt="暂无数据" class="empty-icon" />
      <span class="empty-text">暂无任务数据</span>
    </div>
  </ng-template>
</ion-card-content>