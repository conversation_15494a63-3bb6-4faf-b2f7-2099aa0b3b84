import { ChangeDetectorRef, Component, EventEmitter, forwardRef, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DetailsMode } from 'src/app/@core/base/environment';
import { ShareModuleService } from '../share.service';

/**
 * 快速选择组件
 * 当选项数量小于等于3个时，直接显示单选按钮组
 * 当选项数量大于3个时，使用弹窗列表模式
 */
@Component({
  selector: 'ost-quick-option-select',
  templateUrl: './quick-option-select.component.html',
  styleUrls: ['./quick-option-select.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => QuickOptionSelectComponent),
      multi: true
    }
  ]
})
export class QuickOptionSelectComponent implements OnInit, ControlValueAccessor, OnDestroy {
  // 模态框类型（查看/编辑/添加）
  @Input() modalType: string;
  // 选项显示字段名
  @Input() labelName: string;
  // 选项值字段名
  @Input() labelValue: string;
  // 数据接口地址
  @Input() interfaceUrl: string;
  // 输入框占位文本
  @Input() placeholder = '';
  // 是否禁用
  @Input() disabled = false;
  // 图标名称
  @Input() icon = 'search-outline';
  // 是否只读
  @Input() readonly = false;
  // 是否必填
  @Input() required = false;
  // 文本对齐方式
  @Input() textAlign = 'right';
  // 是否显示图标
  @Input() isShowIcon = true;
  /** 
   * 排列方向
   * horizontal: 横向排列（默认）
   * vertical: 竖向排列
   */
  @Input() direction: 'horizontal' | 'vertical' = 'horizontal';
  /** 
   * 非表单控件场景下的值绑定
   * 仅在不使用 formControlName 时使用
   */
  @Input() set radioValue(val: any) {
    if (val !== undefined && val !== null) {
      this.value = val;
    }
  }

  /** 选中项变化事件 */
  @Output() selectedItemChange = new EventEmitter<any>();

  // 选项列表
  list: any = [];
  // 是否显示快速选择模式
  showQuickRadio = false;
  // 当前选中值
  value: any = null;

  /** 值变化回调函数 */
  private onChange = (_: any) => null;
  /** 触摸回调函数 */
  private onTouch = () => null;

  private destroy$ = new Subject<void>();

  constructor(
    private cd: ChangeDetectorRef, private shareSer: ShareModuleService,
  ) { }

  /**
   * 组件初始化时加载数据
   */
  ngOnInit() {
    this.loadData();
    this.modalType === DetailsMode.SEE ? this.readonly = true : this.readonly = false;
  }

  /**
   * 加载选项数据
   */
  private loadData() {
    if (!this.interfaceUrl) {
      this.list = [];
      this.initData([]);
      return;
    }

    const requestParams = { interfaceUrl: this.interfaceUrl };
    this.shareSer.getRequest(requestParams)
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        if (res.code === 0) {
          let responseData = [];
          if (this.interfaceUrl.includes('/dict/msg/byCode')) {
            responseData = res.data.dictValueList || [];
          } else {
            responseData = res.data?.records || res.data || [];
          }

          this.initData(responseData);
          // 强制变更检测，确保list数据更新后再判断显示模式
          this.cd.detectChanges();
        }
      });
  }

  /**
   * 初始化数据
   * @param data 原始数据
   */
  private initData(data: any) {
    if (!data) {
      this.list = [];
      return;
    }

    // 判断是否显示快速选择
    const shouldShowQuickRadio = Array.isArray(data) && data.length <= 3;
    
    // 数据格式转换 - 只在快速选择模式下进行转换
    if (shouldShowQuickRadio && this.labelName && this.labelValue) {
      // 在快速选择模式下转换数据，因为快速选择模式需要直接显示数据
      this.transformation(data);
    } else {
      // 如果是列表选择模式，直接使用原始数据，由ost-option-source组件内部转换
      this.list = Array.isArray(data) ? data : [];
    }

    // 确保数据已经完全处理好再应用显示模式
    setTimeout(() => {
      // 设置显示模式
      this.showQuickRadio = shouldShowQuickRadio;
      this.cd.detectChanges();

      // 在快速选择模式下（radio展示），如果没有选中值，默认选中第一个
      // 满足条件：1. showQuickRadio 为 true（选项数量小于等于3） 2. list 不为空（有选项可选） 3. value 为空（当前没有选中值）
      if (this.showQuickRadio && this.list.length > 0 && !this.value) {
        const firstItem = this.list[0];
        // 调用 onChangeRadio 方法，触发选项值变化回调
        this.onChangeRadio(firstItem.value);
      }
    }, 0);
  }

  /**
   * 选项值变化处理
   * @param value 选中值
   */
  onChangeRadio(value: any) {
    this.value = value;
    this.onChange(value);
    this.onTouch();

    // 发射选中项信息
    let selectedItem = this.list.find(item => item.value === value);
    
    // 如果不是快速选择模式，且未找到对应项，尝试使用labelValue查找
    if (!selectedItem && !this.showQuickRadio && this.labelValue) {
      selectedItem = this.list.find(item => item[this.labelValue] === value);
    }
    
    if (selectedItem) {
      this.selectedItemChange.emit(selectedItem);
    }
  }

  /**
   * 点击选项处理
   * @param item 选项数据
   */
  onRadioClick(item: any) {
    if (!this.readonly && !this.disabled) {
      this.onChangeRadio(item.value);
    }
  }

  /**
   * 获取选项显示名称
   * @param value 选项值
   * @returns 选项显示名称
   */
  getRadioName(value: any) {
    if (this.list.length > 0) {
      // 尝试直接使用value查找
      let selectedItem = this.list.find(item => item.value === value);
      
      // 如果不是快速选择模式，且未找到对应项，尝试使用labelValue查找
      if (!selectedItem && !this.showQuickRadio && this.labelValue) {
        selectedItem = this.list.find(item => item[this.labelValue] === value);
        // 如果找到了，返回对应的labelName字段值
        if (selectedItem && this.labelName) {
          return selectedItem[this.labelName];
        }
      }
      
      // 正常返回name属性
      return selectedItem?.name;
    }
  }

  /**
   * 转换数据格式
   * @param result 原始数据
   */
  private transformation(result: any) {
    const newList = [];
    const dataArray = Array.isArray(result) ? result : [];

    dataArray.forEach(i => {
      if (i && typeof i === 'object') {
        const { [this.labelValue]: value, [this.labelName]: name, ...rest } = i; // 解构赋值，去掉 labelValue 和 labelName
        const item = {
          value,
          name,
          ...rest // 保留其他属性
        };
        newList.push(item);
      }
    });

    this.list = newList;
    this.cd.detectChanges();
  }

  /**
   * 写入值
   * @param value 外部传入的值
   */
  writeValue(value: any): void {
    this.value = value;
    this.cd.detectChanges();
  }

  /**
   * 注册值变化回调函数
   * @param fn 回调函数
   */
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  /**
   * 注册触摸回调函数
   * @param fn 回调函数
   */
  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  /**
   * 设置禁用状态
   * @param isDisabled 是否禁用
   */
  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
} 