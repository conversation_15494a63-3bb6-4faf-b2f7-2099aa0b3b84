import { AfterViewInit, Component, ElementRef, HostListener, Input, OnDestroy, OnInit, Renderer2, ViewChild } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { OrientationService } from 'src/app/@core/providers/orientation.service';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { CameraInfoService } from './service/camera-info.service';
import { CanvasDrawingService } from './service/canvas-drawing-service';
import { VideoPlayerService } from './service/video-player-service';
import { environment } from 'src/environments/environment';
import { MapService } from '../service';

@Component({
  selector: 'app-camera-layer',
  templateUrl: './camera-layer.component.html',
  styleUrls: ['./camera-layer.component.scss'],
  // 在这里提供服务，确保它们是单例或在需要时创建
  providers: [VideoPlayerService, { provide: CanvasDrawingService, useFactory: () => new CanvasDrawingService(1920, 1080) }]
})
export class CameraLayerComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() modelInfo: any; // 父组件传入的模型信息，包含摄像头ID
  @Input() mapService: MapService; // 父组件传入的MapService实例
  @ViewChild('videoPlayer') videoPlayer: ElementRef<HTMLVideoElement>; // 视频播放器元素引用
  @ViewChild('overlayCanvas') overlayCanvas: ElementRef<HTMLCanvasElement>; // 覆盖层Canvas元素引用

  isFullScreen: boolean = false;

  videoUrl: string; // 视频流URL
  cameraInfo: any = {}; // 摄像头详细信息
  showError = false; // 是否显示错误信息
  errorMessage = ''; // 错误信息内容
  showLoading = true; // 初始化为 true，以便在初次加载时显示加载指示器
  private videoReady = false; // 视频是否准备就绪的标志
  // 绑定事件处理程序，避免在每次事件触发时重新创建函数
  private boundOnVideoCanPlay = this.onVideoCanPlay.bind(this);
  private boundResizeHandler = this.syncCanvasWithVideo.bind(this);

  // 为等待事件添加一个新的绑定事件处理程序
  private boundOnVideoPlaying = this.onVideoPlaying.bind(this);

  // 添加标志来跟踪是否是首次执行handleCameraData
  private isFirstCameraData = true;

  constructor(
    private modalCtrl: ModalController, private platform: Platform,
    private toastSer: ToastService, private renderer: Renderer2,
    private orientation: OrientationService,
    private videoPlayerService: VideoPlayerService,
    private canvasDrawingService: CanvasDrawingService,
    private cameraInfoService: CameraInfoService,
    private injectedMapService: MapService,
  ) { }

  ngOnInit() {
    // 根据初始屏幕方向设置全屏模式
    if (this.platform.isLandscape()) {
      this.isFullScreen = true;
    }

    if (this.modelInfo?.id) {
      this.initializeCamera();
    } else {
      this.toastSer.presentToast('未传入摄像头ID', 'danger');
    }
  }

  private async initializeCamera() {
    try {
      this.showLoading = true; // 获取摄像头信息时显示加载指示器
      const result = await this.cameraInfoService.getCameraInfo(this.modelInfo.id);
      if (result.code === 0 && result.data) {
        this.handleCameraData(result.data);
        // 开始轮询
        this.cameraInfoService.startPolling(
          this.modelInfo.id,
          (data) => this.handleCameraData(data),
          (error) => this.handleError(error),
        );
      }
    } catch (error) {
      console.error('初始化摄像头失败:', error);
      this.handleError(error);
    }
  }

  /**
   * 视图初始化后生命周期钩子
   */
  ngAfterViewInit() {
    // 确保视频元素存在
    if (this.videoPlayer?.nativeElement) {
      // 监听视频的canplay事件，表示视频可以开始播放
      this.videoPlayer.nativeElement.addEventListener('canplay', this.boundOnVideoCanPlay);
      this.videoPlayer.nativeElement.addEventListener('playing', this.boundOnVideoPlaying);
    }
    // 监听窗口resize事件，当窗口大小改变时同步Canvas和视频
    window.addEventListener('resize', this.boundResizeHandler);
  }

  /**
   * 视频可以开始播放时的回调
   */
  private onVideoCanPlay() {
    this.videoReady = true; // 设置视频准备就绪标志
    this.syncCanvasWithVideo(); // 同步Canvas尺寸和位置到视频内容
    // 显示Canvas覆盖层
    if (this.overlayCanvas && this.overlayCanvas.nativeElement) {
      this.overlayCanvas.nativeElement.classList.add('show-canvas');
    }
    this.initCanvas(); // 初始化Canvas上下文
    this.drawAreas(); // 绘制区域
    this.showLoading = false; // 视频可以播放时隐藏加载指示器
  }

  // 处理视频 'playing' 事件的新方法
  private onVideoPlaying() {
    this.showLoading = false; // 视频开始播放时隐藏加载指示器
  }

  /**
   * 同步Canvas的尺寸和位置与视频内容的实际渲染区域
   * 这是核心逻辑，确保Canvas精确覆盖视频内容，而非整个视频元素布局框
   */
  private syncCanvasWithVideo() {
    // 检查视频和Canvas元素是否存在
    if (!this.videoPlayer || !this.overlayCanvas) return;

    const videoEl = this.videoPlayer.nativeElement;
    // 调用 CanvasDrawingService 的方法进行同步
    this.canvasDrawingService.syncCanvasWithVideo(videoEl);
    this.drawAreas(); // 重新绘制区域
  }

  /**
   * 初始化Canvas上下文
   */
  private initCanvas() {
    if (!this.overlayCanvas) return;
    this.canvasDrawingService.initCanvas(this.overlayCanvas.nativeElement);
    this.syncCanvasWithVideo(); // 确保在获取到上下文后，立即同步尺寸
  }

  /**
   * 处理摄像头数据
   * @param data 摄像头数据
   */
  private handleCameraData(data: any): void {
    try {
      // 1. 更新摄像头信息
      this.cameraInfo = data;
      const newVideoUrl = this.buildVideoUrl(data.rtspUrl);
      console.log('新视频URL:', newVideoUrl);

      // 2. 检查视频URL是否发生变化
      if (this.videoUrl === newVideoUrl && this.videoPlayerService.isPlayerInitialized()) {
        // 如果URL没有变化且播放器已初始化，只处理预置位变化
        if (this.cameraInfoService.checkPresetBitChange(data.presetBitId)) {
          this.drawAreas();
        }
        return; // 避免重复初始化
      }

      // 3. 更新视频URL
      this.videoUrl = newVideoUrl;

      // 4. 验证视频地址
      if (!this.validateVideoUrl()) {
        return;
      }

      // 5. 初始化视频播放器
      if (!this.initializeVideoPlayer()) {
        return;
      }

      // 6. 处理预置位变化
      if (this.cameraInfoService.checkPresetBitChange(data.presetBitId)) {
        this.drawAreas();
      }

      // 7. 首次执行时移动地图到摄像头位置
      if (this.isFirstCameraData && data.geom) {
        this.moveMapToCameraLocation(data.geom);
        this.isFirstCameraData = false;
      }

    } catch (error) {
      console.error('处理摄像头数据失败:', error);
      this.handleError(error);
    }
  }

  /**
   * 构建视频URL
   * @param rtspUrl RTSP URL，可能是完整路径或相对路径
   * @returns 完整的视频URL
   */
  private buildVideoUrl(rtspUrl: string): string {
    if (!rtspUrl) {
      return '';
    }

    // 如果是完整URL直接返回
    if (rtspUrl.startsWith('http')) {
      return rtspUrl;
    }

    const { ip, port } = environment.api;
    const protocol = environment.production ? 'https' : 'http';
    // 确保rtspUrl以/开头，避免重复的斜杠
    const urlPath = rtspUrl.startsWith('/') ? rtspUrl : `/${rtspUrl}`;
    
    return `${protocol}://${ip}:${port}${urlPath}`;
  }

  /**
   * 验证视频地址
   * @returns 是否有效
   */
  private validateVideoUrl(): boolean {
    if (!this.videoUrl) {
      this.handleError('视频地址为空');
      return false;
    }

    if (!this.videoUrl.startsWith('http')) {
      this.handleError('无效的视频地址');
      return false;
    }

    // 视频地址有效，隐藏错误信息并开始轮询
    this.showError = false;
    this.errorMessage = '';
    return true;
  }

  /**
   * 初始化视频播放器
   * @returns 是否成功
   */
  private initializeVideoPlayer(): boolean {
    if (!this.videoPlayer?.nativeElement) {
      this.handleError('视频播放器未初始化');
      return false;
    }

    // 检查是否需要重新初始化播放器
    const currentUrl = this.videoPlayerService.getCurrentUrl();
    if (!this.videoPlayerService.isPlayerInitialized() || currentUrl !== this.videoUrl) {
      // **关键：设置 VideoPlayerService 的错误回调**
      this.videoPlayerService.setOnErrorCallback((errorMessageFromService: string) => {
        this.handleError(errorMessageFromService); // 使用 CameraLayerComponent 的 handleError 方法
      });

      this.videoPlayerService.initFlvPlayer(this.videoPlayer.nativeElement, this.videoUrl);
    }
    return true;
  }

  /**
   * 绘制Canvas上的区域（例如：安全范围、管道范围）
   */
  private drawAreas() {
    let scopePoints = null;
    let pipeScopePoints = null;

    if (this.cameraInfo.scope) {
      try {
        scopePoints = JSON.parse(this.cameraInfo.scope);
      } catch (e) {
        console.error('解析scope数据失败:', e);
      }
    }
    if (this.cameraInfo.pipeScope) {
      try {
        pipeScopePoints = JSON.parse(this.cameraInfo.pipeScope);
      } catch (e) {
        console.error('解析pipeScope数据失败:', e);
      }
    }
    this.canvasDrawingService.drawAreas(scopePoints, pipeScopePoints);
  }

  /**
   * 切换全屏模式
   */
  async toggleFullScreen() {
    this.isFullScreen = !this.isFullScreen;
    const modalElement = await this.modalCtrl.getTop(); // 获取当前的 ion-modal 实例
    if (modalElement) {
      if (this.isFullScreen) {
        this.renderer.addClass(modalElement, 'fullscreen-mode'); // 添加类到 ion-modal
        await this.orientation.lockLandscape();
      } else {
        this.renderer.removeClass(modalElement, 'fullscreen-mode'); // 移除类从 ion-modal
        await this.orientation.lockPortrait();
      }
      window.dispatchEvent(new Event('resize')); // 强制触发 resize 事件以在方向改变后重新同步 Canvas
    }
  }

  /**
   * 进入页面时处理屏幕方向
   */
  async ionViewDidEnter() {
    // 如果是全屏模式，锁定横屏
    if (this.isFullScreen) {
      await this.orientation.lockLandscape();
    } else {
      // 否则，确保竖屏
      await this.orientation.lockPortrait();
    }
    window.dispatchEvent(new Event('resize')); // 确保进入时 Canvas 同步
  }

  /**
   * 离开页面时释放屏幕方向锁定
   */
  async ionViewWillLeave() {
    // 清除地图上的标记点
    const mapService = this.mapService || this.injectedMapService;
    if (mapService) {
      mapService.clearMarkers();
    }
    
    await this.orientation.lockPortrait();
    const modalElement = await this.modalCtrl.getTop();
    if (modalElement) {
      // 确保离开时移除全屏类
      this.renderer.removeClass(modalElement, 'fullscreen-mode');
    }
  }

  /**
   * 关闭模态弹窗
   */
  closeModal() {
    // 清除地图上的标记点
    const mapService = this.mapService || this.injectedMapService;
    if (mapService) {
      mapService.clearMarkers();
    }
    
    this.modalCtrl.dismiss();
  }

  /**
   * 监听硬件返回按钮事件（Ionic特有）
   * @param $event 返回按钮事件对象
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event: any): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation(); // 阻止事件进一步传播
      this.closeModal(); // 关闭模态框
    });
  }

  /**
   * 组件销毁生命周期钩子
   */
  ngOnDestroy() {
    // 清除地图上的标记点
    const mapService = this.mapService || this.injectedMapService;
    if (mapService) {
      mapService.clearMarkers();
    }
    
    // 调用 VideoPlayerService 销毁播放器
    this.videoPlayerService.destroyPlayer();
    this.cameraInfoService.destroy();

    // 清除轮询定时器
    this.cameraInfoService.stopPolling();

    // 移除窗口resize事件监听器，防止内存泄漏
    window.removeEventListener('resize', this.boundResizeHandler);
    // 移除视频canplay事件监听器
    if (this.videoPlayer?.nativeElement) {
      this.videoPlayer.nativeElement.removeEventListener('canplay', this.boundOnVideoCanPlay);
      this.videoPlayer.nativeElement.removeEventListener('playing', this.boundOnVideoPlaying);
    }
    // 确保在销毁时重置方向
    this.orientation.lockPortrait();
  }

  /**
   * 重试初始化摄像头
   */
  retryInitialization(): void {
    // 重置错误状态
    this.showError = false;
    this.errorMessage = '';
    this.showLoading = true;
    
    // 重新初始化摄像头
    if (this.modelInfo?.id) {
      this.initializeCamera();
    }
  }

  private handleError(error: any): void {
    const errorMessage = typeof error === 'string' ? error : '获取摄像头信息失败';
    this.showError = true;
    this.errorMessage = errorMessage;
    this.showLoading = false; // 出现错误时立即隐藏加载指示器
    
    // 停止轮询，避免在错误状态下继续请求
    this.cameraInfoService.stopPolling();
    
    // 销毁播放器，防止资源泄露
    this.videoPlayerService.destroyPlayer();
    
    // 重置视频状态
    this.videoReady = false;
    
    // 隐藏Canvas覆盖层
    if (this.overlayCanvas && this.overlayCanvas.nativeElement) {
      this.overlayCanvas.nativeElement.classList.remove('show-canvas');
    }
    
    console.error('摄像头组件错误:', errorMessage);
  }

  /**
   * 移动地图到摄像头位置
   * @param geom 摄像头的地理坐标信息
   */
  private moveMapToCameraLocation(geom: string): void {
    try {
      // 解析地理坐标信息
      const geomData = JSON.parse(geom);
      if (geomData.type === 'Point' && geomData.coordinates && geomData.coordinates.length >= 2) {
        const coordinate: [number, number] = geomData.coordinates;
        
        // 优先使用传入的MapService实例，如果没有则使用注入的实例
        const mapService = this.mapService || this.injectedMapService;
        
        // 检查地图组件是否可用
        const mapComponent = mapService.getMapComponent();
        if (!mapComponent) {
          console.error('MapService中没有注册的地图组件实例');
          return;
        }
        
        if (!mapComponent.view) {
          console.error('地图组件的view未初始化');
          return;
        }
        
        // 清除之前的标记
        mapService.clearMarkers();
        // 在坐标位置添加蓝色点标记
        mapService.addImageMarker(coordinate, '/assets/map/point_blue.png', 0.2);
        // 使用地图服务移动地图到摄像头位置，设置缩放级别为18
        mapService.moveMapToCoordinate(coordinate, 1000, 18);
      }
    } catch (error) {
      console.error('解析摄像头坐标失败:', error);
    }
  }
}
