import { Injectable } from '@angular/core';
import { BackgroundGeolocation, BackgroundGeolocationConfig, BackgroundGeolocationEvents, BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { Geolocation } from '@ionic-native/geolocation/ngx';
import { AndroidPermissions } from '@ionic-native/android-permissions/ngx';
import { Platform } from '@ionic/angular';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { Subscription } from 'rxjs';
import { StorageService } from '../../@core/providers/storage.service';
import { UserInfoService } from '../../@core/providers/user-Info.service';
import { LocationProviderService } from '../../share/map-component/service';

@Injectable({
  providedIn: 'root'
})
// 定位服务
export class LocationService {
  private locationSubscription: Subscription;
  private providerChangeSubscription: Subscription;
  private currentTaskCode: string;
  private currentGroupCode: string;
  private isTrackingActive = false;

  constructor(
    private backgroundGeolocation: BackgroundGeolocation,
    private geolocation: Geolocation,
    private androidPermissions: AndroidPermissions,
    private platform: Platform,
    private storage: StorageService,
    private userSer: UserInfoService,
    private locationProviderService: LocationProviderService
  ) {
    // 监听定位模式变化，动态更新配置
    this.providerChangeSubscription = this.locationProviderService.onProviderChange().subscribe(newProvider => {
      this.onLocationProviderChanged(newProvider);
    });
  }

  /**
   * 定位模式变化处理
   */
  private async onLocationProviderChanged(newProvider: number): Promise<void> {
    if (this.isTrackingActive) {
      console.log('巡检过程中定位模式已切换为:', newProvider);
      try {
        // 重新配置BackgroundGeolocation以应用新的定位模式
        const config = await this.createPostTemplate(this.currentTaskCode, this.currentGroupCode);
        await this.backgroundGeolocation.configure(config);
        console.log('轨迹跟踪服务已更新定位模式');
      } catch (error) {
        console.error('更新轨迹跟踪定位模式失败:', error);
      }
    }
  }

  /**
   * 初始化定位配置
   * @param taskCode 任务代码
   * @param groupCode 组代码
   */
  async initLocationConfigure(taskCode: string, groupCode: string): Promise<void> {
    console.log('初始化定位插件配置', taskCode, groupCode);

    this.currentTaskCode = taskCode;
    this.currentGroupCode = groupCode;

    // 获取存储的配置
    const options = await this.storage.get('GeolocationConfig').toPromise();
    console.log('初始化服务', options);

    // 配置定位服务
    await this.backgroundGeolocation.configure(options);
    const taskConfig = await this.createPostTemplate(taskCode, groupCode);
    await this.backgroundGeolocation.configure(taskConfig);
    await this.backgroundGeolocation.deleteAllLocations();
  }

  /**
   * 开始位置监听
   * @param onLocationChange 位置变化回调
   */
  startLocationTracking(onLocationChange: (location: BackgroundGeolocationResponse) => void): void {
    this.isTrackingActive = true;
    this.locationSubscription = this.backgroundGeolocation.on(BackgroundGeolocationEvents.location)
      .subscribe(onLocationChange);
  }

  /**
   * 停止位置监听
   */
  stopLocationTracking(): void {
    this.isTrackingActive = false;
    if (this.locationSubscription) {
      this.locationSubscription.unsubscribe();
      this.locationSubscription = null;
    }
  }

  /**
   * 创建位置数据上传模板
   * @param taskCode 任务代码
   * @param groupCode 组代码
   */
  private async createPostTemplate(taskCode: string, groupCode: string): Promise<BackgroundGeolocationConfig> {
    // 直接从storage读取AppBootService存储的基础配置
    const baseConfig = await this.storage.get('GeolocationConfig').toPromise();

    // 在基础配置上添加任务特定的配置
    return {
      ...baseConfig, // 直接使用AppBootService存储的完整配置
      // 实时上传位置数据的端点
      url: `${ResourceGlobalConfig.url}/work-inspect/api/v2/inspect/app/trajectory/upload`,
      syncUrl: `${ResourceGlobalConfig.url}/work-inspect/api/v2/inspect/app/trajectory/upload`,
      httpHeaders: {
        Authorization: this.userSer.token
      },
      postTemplate: {
        longitude: '@longitude', // 经度
        latitude: '@latitude', // 纬度
        speed: '@speed',
        trajectoryTime: '@time', // 轨迹时间
        altitude: '@altitude', // 高程
        accuracy: '@accuracy', // 精度
        userName: this.userSer.userName, //巡检人名称
        userCode: this.userSer.userId, // 巡检人编号
        depCode: this.userSer.depCode, // 部门编号
        depName: this.userSer.depName, // 部门名称
        groupCode,
        taskCode,
      },
      debug: false,
    };
  }

  /**
   * 获取当前位置坐标
   * @param location 位置信息
   */
  getCurrentCoordinate(location: BackgroundGeolocationResponse): number[] {
    return [location.longitude, location.latitude];
  }

  /**
   * 启动后台定位服务
   */
  async startBackgroundGeolocation(): Promise<void> {
    try {
      await this.backgroundGeolocation.start();
      console.log('✅ 后台定位服务已启动');
    } catch (error) {
      console.error('❌ 启动后台定位服务失败:', error);
      throw error;
    }
  }

  /**
   * 停止后台定位服务
   */
  stopBackgroundGeolocation(): void {
    this.backgroundGeolocation.stop();
    console.log('🛑 后台定位服务已停止');
  }

  /**
   * 检查定位状态
   * @returns Promise<LocationStatus> 定位状态信息
   */
  async checkLocationStatus(): Promise<LocationStatus> {
    const status: LocationStatus = {
      hasPermission: false,
      isLocationEnabled: false,
      canUseLocation: false,
      errorMessage: ''
    };

    try {
      // 1. 检查定位权限
      if (this.platform.is('cordova')) {
        const fineLocationPermission = await this.androidPermissions.checkPermission(
          this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION
        );
        const coarseLocationPermission = await this.androidPermissions.checkPermission(
          this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION
        );

        status.hasPermission = fineLocationPermission.hasPermission || coarseLocationPermission.hasPermission;

        if (!status.hasPermission) {
          status.errorMessage = '应用缺少定位权限，请在设置中开启定位权限';
          return status;
        }
      } else {
        // 在浏览器环境中，假设有权限
        status.hasPermission = true;
      }

      // 2. 检查定位服务是否开启（通过尝试获取位置）
      try {
        const position = await this.geolocation.getCurrentPosition({
          timeout: 3000,        // 减少超时时间到3秒
          enableHighAccuracy: false,
          maximumAge: 300000    // 允许使用5分钟内的缓存位置
        });

        if (position && position.coords) {
          status.isLocationEnabled = true;
          status.canUseLocation = true;
        }
      } catch (locationError) {
        console.warn('定位服务检查失败:', locationError);

        // 根据错误码判断具体问题
        if (locationError.code === 1) {
          status.errorMessage = '定位权限被拒绝，请在设置中开启定位权限';
        } else if (locationError.code === 2) {
          status.errorMessage = '无法获取位置信息，请检查GPS是否开启';
        } else if (locationError.code === 3) {
          status.errorMessage = '定位超时，请检查网络连接和GPS信号';
        } else {
          status.errorMessage = '定位服务不可用，请检查设备设置';
        }

        status.isLocationEnabled = false;
        status.canUseLocation = false;
      }

    } catch (error) {
      console.error('检查定位状态失败:', error);
      status.errorMessage = '检查定位状态时发生错误';
    }

    return status;
  }

  /**
   * 请求定位权限
   * @returns Promise<boolean> 是否获得权限
   */
  async requestLocationPermission(): Promise<boolean> {
    if (!this.platform.is('cordova')) {
      return true; // 浏览器环境默认有权限
    }

    try {
      const permissions = [
        this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION,
        this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION
      ];

      for (const permission of permissions) {
        const result = await this.androidPermissions.requestPermission(permission);
        if (result.hasPermission) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('请求定位权限失败:', error);
      return false;
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.stopLocationTracking();
    this.stopBackgroundGeolocation();

    // 清理定位模式变化订阅
    if (this.providerChangeSubscription) {
      this.providerChangeSubscription.unsubscribe();
      this.providerChangeSubscription = null;
    }
  }
}

/**
 * 定位状态接口
 */
export interface LocationStatus {
  hasPermission: boolean;      // 是否有定位权限
  isLocationEnabled: boolean;  // 定位服务是否开启
  canUseLocation: boolean;     // 是否可以使用定位
  errorMessage: string;        // 错误信息
}