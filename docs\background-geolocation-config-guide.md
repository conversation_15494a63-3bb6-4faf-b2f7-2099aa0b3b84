# BackgroundGeolocation 配置参数详解

## 概述

本文档详细介绍了 `BackgroundGeolocationDefaultConfig` 中各个配置参数的作用、可选值和使用建议，特别针对锁屏状态下的后台定位需求。

## 🔑 核心后台运行配置

### 1. stopOnTerminate

**作用**：控制应用终止时是否停止定位服务

**数据类型**：`boolean`

**可选值**：
- `true`：应用终止时停止定位服务（默认值）
- `false`：应用终止时不停止定位服务

**推荐设置**：`false`

**详细说明**：
- 设置为 `false` 时，即使应用被系统杀死，定位服务仍会作为独立的后台服务继续运行
- 这是确保锁屏状态下持续定位的关键配置
- 需要配合 `startForeground: true` 使用

**使用场景**：
- ✅ 需要锁屏后继续定位
- ✅ 需要应用被杀死后仍然定位
- ❌ 只需要应用运行时定位

---

### 2. startForeground

**作用**：是否启用前台服务

**数据类型**：`boolean`

**可选值**：
- `true`：启用前台服务，在通知栏显示持续通知
- `false`：不启用前台服务（默认值）

**推荐设置**：`true`

**详细说明**：
- Android 8.0+ 系统要求后台定位必须使用前台服务
- 启用后会在通知栏显示持续通知（如："巡检app - 正在定位当中..."）
- 防止系统因为省电策略杀死定位服务

**使用场景**：
- ✅ Android 8.0+ 设备必须启用
- ✅ 需要稳定的后台定位
- ❌ 不希望显示通知栏提醒

---

### 3. pauseLocationUpdates

**作用**：应用暂停时是否暂停位置更新

**数据类型**：`boolean`

**可选值**：
- `true`：应用暂停时暂停位置更新（默认值）
- `false`：应用暂停时不暂停位置更新

**推荐设置**：`false`

**详细说明**：
- 设置为 `false` 确保锁屏、切换应用时位置更新继续进行
- 这是保证关键点提醒在后台正常工作的重要配置
- 与 `stopOnTerminate: false` 配合使用效果最佳

**使用场景**：
- ✅ 需要锁屏时继续定位
- ✅ 需要切换应用时继续定位
- ❌ 只需要应用前台时定位

---

### 4. saveBatteryOnBackground

**作用**：后台运行时是否启用省电模式

**数据类型**：`boolean`

**可选值**：
- `true`：后台启用省电模式，降低定位频率和精度
- `false`：后台不启用省电模式，保持正常定位频率和精度

**推荐设置**：`false`

**详细说明**：
- 设置为 `false` 确保后台定位的精度和频率不被降低
- 有助于及时触发关键点提醒
- 会增加电池消耗，需要权衡

**使用场景**：
- ✅ 需要高精度的后台定位
- ✅ 需要及时的关键点提醒
- ❌ 电池续航优先考虑

---

## 📍 基础定位配置

### 1. desiredAccuracy

**作用**：期望的定位精度

**数据类型**：`number`

**可选值**：
- `0-3`：高精度（使用GPS）
- `10`：平衡精度和电池消耗（推荐）
- `100`：低精度（仅网络定位）
- `1000`：最低精度

**推荐设置**：`10`

**详细说明**：
- 数值越小，定位越精确，但电池消耗越大
- 10米精度适合大多数巡检场景
- 关键点提醒通常需要5-10米的精度

---

### 2. stationaryRadius

**作用**：静止半径，用户在此范围内移动时不触发位置更新

**数据类型**：`number`

**可选值**：
- `0`：禁用静止检测，持续更新位置
- `20-50`：适中的静止半径
- `100+`：较大的静止半径

**推荐设置**：`0`

**详细说明**：
- 设置为0确保持续的位置更新
- 对于巡检应用，需要精确跟踪移动轨迹
- 非零值可以节省电池，但可能错过关键点

---

### 3. distanceFilter

**作用**：触发新位置事件的最小移动距离

**数据类型**：`number`（单位：米）

**可选值**：
- `0`：任何位置变化都触发更新
- `1-5`：高精度轨迹记录
- `10+`：粗略轨迹记录

**推荐设置**：`2`

**详细说明**：
- 2米的设置平衡了轨迹精度和性能
- 太小会产生过多的位置更新
- 太大可能错过重要的位置变化

---

### 4. fastestInterval

**作用**：位置更新的最短时间间隔

**数据类型**：`number`（单位：毫秒）

**可选值**：
- `100-500`：高频率更新
- `1000-5000`：中等频率更新
- `10000+`：低频率更新

**推荐设置**：`200`

**详细说明**：
- 200毫秒确保及时的位置更新
- 与interval配合使用控制更新频率
- 过小的值会增加CPU和电池消耗

---

### 5. interval

**作用**：位置更新的期望时间间隔

**数据类型**：`number`（单位：毫秒）

**可选值**：
- `200-1000`：实时跟踪
- `1000-5000`：正常跟踪
- `5000+`：节能跟踪

**推荐设置**：`200`

**详细说明**：
- 200毫秒提供流畅的实时跟踪
- 确保关键点提醒的及时性
- 可根据电池续航需求调整

---

### 6. syncThreshold

**作用**：数据同步的等待时长

**数据类型**：`number`（单位：毫秒）

**可选值**：
- `100-500`：快速同步
- `1000-5000`：正常同步
- `10000+`：延迟同步

**推荐设置**：`100`

**详细说明**：
- 100毫秒确保数据及时上传
- 网络不佳时数据会被缓存
- 过小可能导致频繁的网络请求

---

### 7. maxLocations

**作用**：允许缓存的最大位置数量

**数据类型**：`number`

**可选值**：
- `1000-10000`：短期缓存
- `10000-50000`：中期缓存
- `50000+`：长期缓存

**推荐设置**：`50000`

**详细说明**：
- 50000个位置点可支持长时间离线工作
- 防止网络中断时数据丢失
- 过大会占用更多存储空间

---

### 8. locationProvider

**作用**：位置数据来源

**数据类型**：`number`

**可选值**：
- `0`：自动选择最佳提供者
- `1`：仅使用GPS
- `2`：仅使用网络定位
- `3`：混合定位（GPS + 网络）

**推荐设置**：`3`

**详细说明**：
- 混合定位提供最佳的可用性
- 室内外环境都能获得位置信息
- 确保在各种环境下的定位稳定性

---

### 9. notificationTitle & notificationText

**作用**：前台服务通知的标题和内容

**数据类型**：`string`

**推荐设置**：
- `notificationTitle: '巡检app'`
- `notificationText: '正在定位当中...'`

**详细说明**：
- 用户可以看到应用正在后台运行
- 提供清晰的状态说明
- 有助于用户理解为什么需要后台权限

---

## 🔧 可选配置参数

### 10. startOnBoot

**作用**：设备启动时是否自动启动定位服务

**数据类型**：`boolean`

**可选值**：
- `true`：设备启动时自动启动定位服务
- `false`：设备启动时不自动启动（默认值）

**推荐设置**：`false`

**详细说明**：
- 通常设置为 `false`，避免不必要的资源消耗
- 只有在特殊需求下才设置为 `true`
- 需要额外的系统权限

**使用场景**：
- ✅ 需要开机自动开始定位的特殊应用
- ❌ 一般的巡检应用（按需启动即可）

---

## 📊 配置组合建议

### 锁屏状态下正常工作的最佳配置

```typescript
const BackgroundGeolocationDefaultConfig: BackgroundGeolocationConfig = {
  // 基础定位配置
  desiredAccuracy: 10,           // 位置精度（单位：米）
  stationaryRadius: 0,           // 静止半径
  distanceFilter: 2,             // 移动的最小距离（单位：米）
  fastestInterval: 200,          // 最短时间间隔（单位：毫秒）
  interval: 200,                 // 位置更新频率（单位：毫秒）
  notificationTitle: '巡检app',   // 通知栏标题
  notificationText: '正在定位当中...', // 通知栏文本
  syncThreshold: 100,            // 等待时长（单位：毫秒）
  maxLocations: 50000,           // 允许缓存的最大位置数量
  locationProvider: 3,           // 位置数据来源

  // 核心后台配置
  stopOnTerminate: false,        // 必须：应用终止时不停止定位
  startForeground: true,         // 必须：启用前台服务
  pauseLocationUpdates: false,   // 必须：应用暂停时不暂停位置更新
  saveBatteryOnBackground: false, // 推荐：后台不节省电池

  // 可选配置
  startOnBoot: false,            // 一般：设备启动时不自动启动
};
```

### 省电优先的配置

```typescript
const BackgroundGeolocationDefaultConfig: BackgroundGeolocationConfig = {
  // 基础定位配置（省电调整）
  desiredAccuracy: 100,          // 降低精度要求
  stationaryRadius: 20,          // 启用静止检测
  distanceFilter: 10,            // 增大移动距离阈值
  fastestInterval: 5000,         // 降低更新频率
  interval: 10000,               // 10秒更新一次
  notificationTitle: '巡检app',
  notificationText: '正在定位当中...',
  syncThreshold: 1000,           // 延长同步等待时间
  maxLocations: 10000,           // 减少缓存数量
  locationProvider: 3,

  // 后台配置
  stopOnTerminate: false,
  startForeground: true,
  pauseLocationUpdates: false,
  saveBatteryOnBackground: true,  // 启用省电模式
  startOnBoot: false,
};
```

### 仅前台定位的配置

```typescript
const BackgroundGeolocationDefaultConfig: BackgroundGeolocationConfig = {
  // 基础定位配置
  desiredAccuracy: 10,
  stationaryRadius: 0,
  distanceFilter: 2,
  fastestInterval: 1000,         // 前台可以稍微降低频率
  interval: 2000,                // 2秒更新一次
  notificationTitle: '巡检app',
  notificationText: '正在定位当中...',
  syncThreshold: 100,
  maxLocations: 1000,            // 前台不需要大量缓存
  locationProvider: 3,

  // 前台配置
  stopOnTerminate: true,          // 应用终止时停止定位
  startForeground: false,         // 不需要前台服务
  pauseLocationUpdates: true,     // 应用暂停时暂停定位
  saveBatteryOnBackground: true,
  startOnBoot: false,
};
```

## ⚠️ 重要注意事项

### 权限要求
- `startForeground: true` 需要 `FOREGROUND_SERVICE` 权限
- `stopOnTerminate: false` 需要后台定位权限
- `startOnBoot: true` 需要 `RECEIVE_BOOT_COMPLETED` 权限

### 用户体验
- 启用前台服务会显示持续通知
- 后台定位会增加电池消耗
- 需要向用户说明为什么需要这些权限

### 系统兼容性
- Android 8.0+ 强制要求前台服务
- 不同厂商的系统可能有额外限制
- 建议在目标设备上充分测试

### 测试验证
- 必须在真机上测试
- 测试锁屏状态下的功能
- 测试长时间后台运行的稳定性
- 监控电池消耗情况

## 🔍 故障排查

### 后台定位不工作
1. 检查 `stopOnTerminate` 是否为 `false`
2. 检查 `startForeground` 是否为 `true`
3. 确认应用有后台定位权限
4. 确认应用在电池优化白名单中

### 关键点提醒不触发
1. 检查 `pauseLocationUpdates` 是否为 `false`
2. 检查 `saveBatteryOnBackground` 是否为 `false`
3. 确认定位精度和频率设置合理

### 电池消耗过高
1. 考虑设置 `saveBatteryOnBackground: true`
2. 调整 `interval` 和 `fastestInterval`
3. 增大 `distanceFilter` 值
