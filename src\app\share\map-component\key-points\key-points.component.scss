.state-col {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  white-space: nowrap;
  text-align: center;
  justify-content: center;
  align-items: center;
  display: flex;
}

.index-col {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  white-space: nowrap;
  text-align: center;
  justify-content: center;
  align-items: center;
  display: flex;
}

ion-grid {
  width: 100%;
  padding: 0;

  .header {
    background-color: #f2f5fc;
    font-weight: 500;
    border-bottom: 1px solid #ddd;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 2;
    height: 42px;
    min-height: 42px;
    line-height: 42px;
    ion-col {
      padding: 0 4px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .row {
    border-bottom: 1px solid #eee;
    font-size: 12px;
    min-height: 38px; // 紧凑的行高
    height: 38px; // 固定行高
    ion-col {
      padding: 8px 4px; // 减少内边距使更紧凑
      line-height: 1.2;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.state-done {
  color: #52C41A;
}
.state-undone {
  color: #FAAD14;
}

.point-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 97%;
  display: block;
  text-align: left !important; // 关键点名称左对齐
  justify-content: flex-start !important; // 内容左对齐
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px; // 可根据弹窗内容区高度调整
}

// 搜索框样式优化
ion-searchbar {
  --background: var(--ion-color-light, #f4f5f8);
  --border-radius: 8px;
  --box-shadow: none;
  --placeholder-color: var(--ion-color-medium, #92949c);
  --color: var(--ion-color-dark, #222428);
  padding: 8px 16px;
  margin-right: 8px; // 添加右侧间距
}

ion-toolbar {
  --padding-start: 0;
  --padding-end: 0;

  ion-buttons {
    margin-right: 8px;

    ion-button {
      --padding-start: 8px;
      --padding-end: 8px;
      --padding-top: 4px;
      --padding-bottom: 4px;
      height: auto;
      margin: 0;
    }
  }
}

// 数据状态列样式
.data-status-col {
  width: 50px !important;
  min-width: 50px !important;
  max-width: 50px !important;
  white-space: nowrap;
  text-align: center !important;
  justify-content: center !important;
  align-items: center !important;
  display: flex !important;
  padding: 8px 4px !important; // 与其他列保持一致的紧凑内边距

  .data-icon {
    font-size: 15px; // 稍微减小图标尺寸适应紧凑布局
    cursor: pointer;
    transition: transform 0.2s ease;
    line-height: 1; // 避免额外的行高
    width: 15px; // 固定宽度
    height: 15px; // 固定高度
    display: flex;
    align-items: center;
    justify-content: center;

    &.clickable:hover {
      transform: scale(1.1);
    }

    &.clickable:active {
      transform: scale(0.95);
    }
  }

  .no-data {
    color: var(--ion-color-medium, #92949c);
    font-size: 12px;
    line-height: 1; // 设置为1避免额外高度
    display: flex;
    align-items: center;
    justify-content: center;
    width: 15px; // 固定宽度与图标一致
    height: 15px; // 固定高度与图标一致
    margin: 0; // 清除默认边距
    padding: 0; // 清除默认内边距
  }
}