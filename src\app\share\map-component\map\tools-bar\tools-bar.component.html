<ost-controlers-group size="xs">
  <!-- 图层定位 -->
  <ost-controler-item>
    <ion-spinner
      *ngIf="spinnerState"
      name="bubbles"
      color="primary"
    ></ion-spinner>
    <ion-icon
      *ngIf="!spinnerState"
      name="locate-outline"
      (click)="onLocation()"
    ></ion-icon>
  </ost-controler-item>
  <!-- 定位模式选择 -->
  <ost-controler-item
    *ngIf="showLocationProviderButton"
    (click)="openLocationProviderModal()"
    [title]="getLocationProviderLabel()">
    <ion-icon [name]="getLocationProviderIcon()"></ion-icon>
  </ost-controler-item>
  <!-- 图层切换 -->
  <ost-controler-item (click)="openSwitch()">
    <ion-icon name="copy-outline"></ion-icon>
  </ost-controler-item>
  <!-- 方大缩小 -->
  <!-- <ost-controler-item (click)="zoomChange.next(1)">
    <ion-icon name="add-outline"></ion-icon>
  </ost-controler-item>
  <ost-controler-item (click)="zoomChange.next(-1)">
    <ion-icon name="remove-outline"></ion-icon>
  </ost-controler-item> -->
  <ost-controler-item
    [select]="true"
    title="地图级别"
  >
    <span>{{ zoomLevel }}</span>
  </ost-controler-item>
</ost-controlers-group>