import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { BacklogPage } from './backlog/backlog.page';
import { HomePageRoutingModule } from './home-routing.module';
import { HomePage } from './home.page';
import { MenuComponent } from './menu/menu.component';
import { PatrolOverviewComponent } from './patrol-overview/patrol-overview.component';
import { TodayReportComponent } from './today-report/today-report.component';
import { ShareModule } from '../share/share.module';

// 
const COMP = [
  HomePage,
  PatrolOverviewComponent,
  MenuComponent,
  TodayReportComponent,
  BacklogPage
];
@NgModule({
  imports: [
    IonicModule,
    CommonModule,
    FormsModule,
    ShareModule,
    HomePageRoutingModule
  ],
  declarations: [
    ...COMP
  ],
})
export class HomePageModule { }
