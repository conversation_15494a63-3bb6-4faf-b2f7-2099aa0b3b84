import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { IonInfiniteScroll, LoadingController, ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';
import { PageGridResult } from 'src/app/@core/base/request-result';
import { ShareModuleService } from 'src/app/share/share.service';
import { Alarm, AlarmParams } from '../alarm-model-config';
import { AlarmImageService } from '../services/alarm-image.service';

/**
 * 告警列表组件
 * 支持分页数据格式
 * 支持下拉刷新和无限滚动加载
 */
@Component({
  selector: 'app-alarm-list',
  templateUrl: './alarm-list.component.html',
  styleUrls: ['./alarm-list.component.scss']
})
export class AlarmListComponent implements OnInit, OnDestroy {
  @ViewChild(IonInfiniteScroll) infiniteScroll: IonInfiniteScroll;

  /** 用于取消订阅的信号 */
  private destroy$ = new Subject<void>();

  /** 当前选中的标签：当前告警或历史告警 */
  readStatus: '未读' | '已读' = '未读';

  /** 告警列表数据 */
  alarms: Alarm[] = [];

  /** 当前页码 */
  private page = 1;

  /** 每页条数 */
  private readonly pageSize = 10;

  /** 是否正在加载 */
  isLoading = false;

  /** 是否还有更多数据 */
  private hasMoreData = true;

  /** 总页数 */
  private totalPages = 0;

  /** 总记录数 */
  private totalRecords = 0;

  constructor(
    private modalCtrl: ModalController,
    private netSer: ShareModuleService,
    private loadingCtrl: LoadingController,
    private alarmImageService: AlarmImageService
  ) { }

  /**
   * 组件初始化
   */
  ngOnInit(): void {
    this.loadAlarms(true);
  }

  /**
   * 加载告警数据
   * @param isInitial 是否为初始加载
   * @param event 事件对象（用于下拉刷新和无限滚动）
   */
  async loadAlarms(isInitial = false, event?: any): Promise<void> {
    // 防止重复加载
    if (this.isLoading) {
      this.completeEvent(event);
      return;
    }

    this.isLoading = true;
    let loading: HTMLIonLoadingElement | undefined;

    // 初始加载时显示加载提示
    if (isInitial) {
      loading = await this.loadingCtrl.create({
        message: '正在加载...',
      });
      await loading.present();
    }

    // 构造请求参数 - 只传递分页参数
    const params: AlarmParams = {
      readStatus: this.readStatus,
      pageNum: this.page,
      pageSize: this.pageSize
    };

    // 发起网络请求
    this.netSer.getRequest({
      interfaceUrl: '/work-inspect/api/v2/inspect/alarm/msg/grid',
    }, params)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoading = false;
          loading?.dismiss();
          this.completeEvent(event);
        })
      )
      .subscribe({
        next: (result: PageGridResult<Alarm[]>) => {
          this.handleAlarmResponse(result, isInitial, event);
        },
        error: (error) => {
          // CoreInterceptorService 已经处理了错误提示，这里只需要处理UI状态
          console.error('加载告警数据失败:', error);
          if (isInitial) {
            this.alarms = [];
          }
        }
      });
  }

  /**
   * 处理告警响应数据，仅支持分页格式
   * @param result 响应结果
   * @param isInitial 是否为初始加载
   * @param event 事件对象
   */
  private handleAlarmResponse(
    result: PageGridResult<Alarm[]>,
    isInitial: boolean,
    event?: any
  ): void {
    const { code } = result;

    if (code === 0) {
      // 只处理分页格式数据
      const newAlarms = result.data.records || [];
      this.totalPages = Math.ceil(result.data.total / this.pageSize);
      this.totalRecords = result.data.total || 0;
      this.hasMoreData = this.page < this.totalPages;

      // 处理告警图片
      this.alarmImageService.processAlarmImages(newAlarms, this.destroy$);

      // 更新数据
      if (isInitial) {
        this.alarms = newAlarms;
      } else {
        this.alarms = [...this.alarms, ...newAlarms];
      }

      // 处理无限滚动状态
      this.handleInfiniteScrollState(event);
    } else {
      // 业务错误，CoreInterceptorService 已经处理了提示
      if (isInitial) {
        this.alarms = [];
      }
    }
  }

  /**
   * 处理无限滚动状态
   * @param event 事件对象
   */
  private handleInfiniteScrollState(event?: any): void {
    if (event && !this.hasMoreData && event.target) {
      event.target.disabled = true;
    }
  }

  /**
   * 完成事件（下拉刷新或无限滚动）
   * @param event 事件对象
   */
  private completeEvent(event?: any): void {
    if (event?.target) {
      event.target.complete();
    }
  }

  /**
   * 重置组件状态
   */
  private resetState(): void {
    this.page = 1;
    this.alarms = [];
    this.hasMoreData = true;
    this.totalPages = 0;
    this.totalRecords = 0;
    if (this.infiniteScroll) {
      this.infiniteScroll.disabled = false;
    }
  }

  /**
   * 下拉刷新处理
   * @param event 刷新事件
   */
  handleRefresh(event: any): void {
    this.resetState();
    this.loadAlarms(false, event);
  }

  /**
   * 无限滚动加载更多
   * @param event 滚动事件
   */
  onIonInfinite(event: any): void {
    if (!this.hasMoreData) {
      this.completeEvent(event);
      return;
    }
    this.page++;
    this.loadAlarms(false, event);
  }

  /**
   * 标签切换处理
   * @param event 切换事件
   */
  segmentChanged(event: any): void {
    this.readStatus = event.detail.value;
    this.resetState();
    this.loadAlarms(true);
  }

  /**
   * 返回上一页
   */
  goBack(): void {
    this.modalCtrl.dismiss();
  }

  /**
   * 获取第一张图片的URL
   * @param alarm 告警对象
   * @returns 图片URL或默认图片
   */
  getFirstImageUrl(alarm: Alarm): string {
    return this.alarmImageService.getFirstImageUrl(alarm);
  }

  /**
   * 判断是否应该显示图片数量标签
   * @param alarm 告警对象
   * @returns 是否应该显示数量标签
   */
  shouldShowImageCount(alarm: Alarm): boolean {
    return this.alarmImageService.shouldShowImageCount(alarm);
  }

  seeDetails(alarm: Alarm) {
    this.netSer.getRequest({
      interfaceUrl: '/work-inspect/api/v2/inspect/alarm/msg/byCode',
      alarmCode: alarm.alarmCode,
    }).pipe(takeUntil(this.destroy$)).subscribe(res => {
      const detailInfo = {
        fileUrl: alarm.picUrls,
        ...res.data,
      }
      // 将 detailInfo 数据传递给父组件并关闭弹窗
      this.modalCtrl.dismiss(detailInfo, 'detail');
    })
  }

  /**
   * 组件销毁时清理资源
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

}
