# 管线组件搜索功能

## 概述
为管线组件（PipeMenuComponent）添加了搜索功能，使其与部门组件（OrgMenuComponent）保持一致的用户体验。

## 功能特性

### 1. 搜索界面
- **搜索框**: 支持输入管线名称进行搜索
- **搜索按钮**: 点击执行搜索操作
- **重置按钮**: 清空搜索条件，恢复完整列表
- **占位符文本**: "请输入管线名称"

### 2. 搜索逻辑
- **模糊搜索**: 支持部分匹配，不区分大小写
- **树形搜索**: 递归搜索父节点和子节点
- **智能展开**: 搜索结果自动展开相关节点
- **父子关联**: 子节点匹配时会显示其父节点

### 3. 数据处理
- **原始数据保存**: 保存完整的管线树数据用于搜索
- **实时过滤**: 根据搜索条件动态过滤显示数据
- **状态管理**: 维护搜索参数和显示状态

## 技术实现

### 组件属性
```typescript
@Input() showSearch: boolean = true;  // 控制搜索框显示
params: string = '';                  // 搜索参数
pipeTree: any;                       // 原始管线树数据
screenHeight: any;                   // 屏幕高度计算
```

### 核心方法

#### 搜索方法
```typescript
onSearch(): void {
  if (this.params !== '') {
    const searchData = this.filterTree(this.pipeTree, this.params);
    this.items = searchData;
  }
}
```

#### 重置方法
```typescript
onReset(): void {
  this.params = '';
  this.items = this.pipeTree;
}
```

#### 过滤算法
```typescript
filterTree(nodes: any[], query: string): any[] {
  // 递归搜索树形结构
  // 支持父子节点关联显示
  // 自动展开匹配节点
}
```

## 样式设计

### 搜索栏样式
- **背景色**: #f6f6f6
- **高度**: 50px
- **搜索框**: 白色背景，圆角设计
- **按钮**: 简洁的文字按钮设计

### 响应式布局
- **搜索框宽度**: 70%
- **按钮布局**: 右侧对齐
- **图标**: 搜索图标提升用户体验

## 使用方式

### 基本用法
```html
<ost-pipe-menu
  [depCode]="depCode"
  [interfaceUrl]="interfaceUrl"
  [showSearch]="true"
  (toggleSubMenu)="onItemClick($event)"
  (itemClick)="onItemClick($event)">
</ost-pipe-menu>
```

### 禁用搜索
```html
<ost-pipe-menu
  [showSearch]="false"
  ...其他属性>
</ost-pipe-menu>
```

## 测试覆盖

### 单元测试
- ✅ 组件创建测试
- ✅ 屏幕高度初始化测试
- ✅ 数据加载和转换测试
- ✅ 搜索功能测试
- ✅ 重置功能测试
- ✅ 大小写不敏感搜索测试
- ✅ 子节点搜索测试
- ✅ 空搜索条件处理测试

### 测试命令
```bash
ng test --include="**/pipe-menu.component.spec.ts"
```

## 兼容性

### 向后兼容
- 默认启用搜索功能（showSearch=true）
- 现有使用方式无需修改
- 保持原有的事件和数据流

### 浏览器支持
- 支持现代浏览器
- 响应式设计适配移动端
- Ionic 框架兼容

## 性能优化

### 搜索优化
- 递归算法优化，避免重复遍历
- 智能节点展开，减少DOM操作
- 原始数据缓存，提升搜索速度

### 内存管理
- 及时清理搜索状态
- 避免内存泄漏
- 合理的数据结构设计

## 未来扩展

### 可能的增强功能
- 搜索历史记录
- 高级搜索选项
- 搜索结果高亮
- 键盘快捷键支持
- 搜索建议/自动完成

### 配置选项
- 搜索延迟设置
- 最小搜索字符数
- 搜索范围配置
- 自定义搜索算法
