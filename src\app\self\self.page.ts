import { Component, NgZ<PERSON>, OnInit } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, ModalController, NavController, Platform } from '@ionic/angular';
import { environment } from 'src/environments/environment';
import { UserInfoService } from '../@core/providers/user-Info.service';
import { AppBootService } from '../app.boot';
import { ChangPasswordComponent } from './chang-password/chang-password.component';
import { GisConfigPage } from './gis-config/gis-config.page';
import { PdfViewersComponent } from './pdf-viewer/pdf-viewer.component';
import { SelfService } from './self.service';
import { KeyPointAlertService } from '../@core/services/key-point-alert.service';
import { DataSyncUiService } from '../@core/services/data-sync-ui.service';
import { TestDataSimulatorService } from '../@core/services/test-data-simulator.service';

@Component({
  selector: 'app-self',
  templateUrl: './self.page.html',
  styleUrls: ['./self.page.scss'],
})
export class SelfPage implements OnInit {
  environmentVersion = environment.version;
  // 部门
  depName: string;
  // 头像
  userAvatar: string = '';
  // 失败数据数量
  failedDataCount: number = 0;

  constructor(
    public userSer: UserInfoService, private ngZone: NgZone,
    private platform: Platform, private netSer: SelfService,
    private alertCtrl: AlertController, private modalCtrl: ModalController,
    private nav: NavController, private appBootService: AppBootService,
    private keyPointAlertSer: KeyPointAlertService,
    private dataSyncUiService: DataSyncUiService,
    private testDataSimulator: TestDataSimulatorService
  ) { }

  ngOnInit(): void {
    this.depName = this.userSer.depName.replace(/,/g, ' | ');
    this.userSer.Avatar ? this.getAvatar() : this.userAvatar = '/assets/images/user.png';
  }

  ionViewWillEnter(): void {
    // 每次进入页面时检查失败数据数量
    this.checkFailedDataCount();
  }

  // 获取用户头像
  getAvatar(): void {
    const avatarCode = this.userSer.Avatar.length > 1 ? this.userSer.Avatar[0] : this.userSer.Avatar;
    this.netSer.GetHeadSculpture([...avatarCode]).subscribe(res => {
      const { code, data } = res;
      if (code === 0) {

        const ip = environment.api.ip;
        let prot = 0;
        environment.production ? prot = environment.api.port : prot = 8100;

        this.userAvatar = `${environment.production ? 'https' : 'http'}://` + ip + ':' + prot + data[0].urlPath;
      }
    });
  }

  /**
   * 修改密码
   */
  async changePassword(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ChangPasswordComponent,
      backdropDismiss: false
    });
    return await modal.present();
  }

  /**
   * GIS配置
   */
  async gisConfig(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: GisConfigPage
    });
    await modal.present();
  }
  /**
   * 检查更新
   */
  async checkUpdate(): Promise<void> {
    if (this.platform.is('android')) {
      this.appBootService.checkVersion();
    }
  }

  /**
   * 使用手册
   */
  async usePDF(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: PdfViewersComponent,
      cssClass: 'app-pdf-viewer'
    });
    await modal.present();
  }

  /**
   * 退出登陆
   */
  async loginOut(): Promise<void> {
    const alertObj = this.loginOutObj();
    const alert = await this.alertCtrl.create({
      header: alertObj.header,
      message: alertObj.message,
      buttons: [
        alertObj.cancel,
        {
          text: alertObj.confirm,
          handler: () => {
            this.userSer.logout();
            this.ngZone.run(() => {
              this.nav.navigateRoot('/auth');
            });
          }
        }]
    });
    await alert.present();
  }

  testVibration() {
    const pointObj = {
      pointName: '001+12945.3',
      pointArr: [0, 0]
    }
    this.keyPointAlertSer.triggerAlert(0, pointObj)
  }

  // 退出登陆
  loginOutObj(): any {
    return {
      header: '退出/切换账户',
      message: '温馨提醒：退出后将无法使用相关应用，您确定要退出吗？',
      cancel: '取消',
      confirm: '确定',
    };
  }

  /**
   * 检查失败数据数量
   */
  async checkFailedDataCount(): Promise<void> {
    this.failedDataCount = await this.dataSyncUiService.getFailedDataCount();
  }

  /**
   * 打开数据同步管理
   */
  async openDataSyncManager(): Promise<void> {
    await this.dataSyncUiService.openDataSyncManager();
    // 操作完成后刷新失败数据数量
    await this.checkFailedDataCount();
  }

  /**
   * 模拟同步失败数据 - 用于测试导出功能
   * 使用专门的测试数据模拟器服务
   */
  async simulateFailedSyncData(): Promise<void> {
    const userInfo = {
      userId: this.userSer.userId,
      depCode: this.userSer.depCode
    };

    await this.testDataSimulator.simulateFailedSyncData(
      userInfo,
      () => this.checkFailedDataCount()
    );
  }

}
