# 用户选择器遮挡问题解决方案

## 问题描述
在真机上，当用户过多时，用户选择器的下拉列表会向下展开，遮挡地图内容，影响用户体验。

## 解决方案

### 1. 改变展开方向
- 将用户列表从向下展开（`top: 100%`）改为向上展开（`bottom: 100%`）
- 添加适当的间距（`margin-bottom: 5px`）避免与选择器重叠

### 2. 提升层级和样式优化
- 提高 z-index 到 1000，确保列表显示在最上层
- 优化用户项的内边距和边框，提升视觉效果
- 为激活状态添加字体加粗效果

### 3. 多列自适应布局
- 使用 CSS Grid 实现多列布局，根据屏幕宽度自动适配
- 超小屏幕（≤320px）：单列布局
- 小屏幕（321px-480px）：两列布局
- 中等屏幕（481px-768px）：三列布局
- 大屏幕（≥769px）：四列或更多列布局

### 4. 交互体验优化
- 添加点击外部区域关闭列表的功能
- 添加平滑的展开/收起动画效果
- 防止事件冒泡，确保点击列表内部不会关闭列表

### 5. 动画效果
- 添加透明度和位移动画
- 使用 CSS transition 实现平滑过渡
- 通过 pointer-events 控制交互状态

## 主要修改文件

### track-control-bar.page.scss
```scss
.user-list {
  position: absolute;
  bottom: 100%; // 向上展开
  left: 10px;
  right: 10px;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000; // 提升层级
  max-height: 160px;
  overflow-y: auto;
  margin-bottom: 5px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease-in-out;
  pointer-events: none;
  padding: 8px;

  // CSS Grid 多列布局
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 6px;

  &.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
  }

  .user-item {
    padding: 8px 10px;
    cursor: pointer;
    font-size: 13px;
    border-radius: 4px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e9ecef;
      transform: translateY(-1px);
    }

    &.active {
      background-color: #e6f0ff;
      color: #4d80cf;
      font-weight: 500;
      border-color: #4d80cf;
    }
  }
}

// 响应式布局
@media (max-width: 320px) {
  .user-list {
    grid-template-columns: 1fr; // 单列
  }
}

@media (min-width: 321px) and (max-width: 480px) {
  .user-list {
    grid-template-columns: repeat(2, 1fr); // 两列
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .user-list {
    grid-template-columns: repeat(3, 1fr); // 三列
  }
}

@media (min-width: 769px) {
  .user-list {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); // 四列+
  }
}
```

### track-control-bar.page.html
```html
<!-- 添加事件阻止冒泡 -->
<div class="user-selector" (click)="$event.stopPropagation()">
  <div class="user-selected" (click)="toggleUserList()">
    <span>巡检人员: {{ trackUsers[selectedUserIndex]?.userName || '无数据' }}</span>
    <ion-icon name="chevron-down-outline" [class.rotate-icon]="showUserList"></ion-icon>
  </div>
  <!-- 添加动画类 -->
  <div class="user-list" [class.show]="showUserList" [hidden]="!showUserList">
    <div class="user-item" *ngFor="let user of trackUsers; let i = index" 
      [class.active]="i === selectedUserIndex"
      (click)="selectUserTrack(i)">
      {{user.userName}}
    </div>
  </div>
</div>
```

### track-control-bar.page.ts
```typescript
// 添加 HostListener 导入
import { ChangeDetectorRef, Component, HostListener, Input, OnDestroy, OnInit } from '@angular/core';

// 添加点击外部关闭功能
@HostListener('document:click', ['$event'])
onDocumentClick(event: Event): void {
  if (this.showUserList) {
    this.showUserList = false;
  }
}
```

## 效果预期
1. 用户列表向上展开，不再遮挡地图内容
2. 多列布局充分利用屏幕宽度，减少空白区域
3. 根据屏幕尺寸自动适配列数，提供最佳显示效果
4. 平滑的动画过渡和悬停效果，提升用户体验
5. 点击外部区域可以关闭列表，操作更加直观
6. 自定义滚动条样式，更加美观

## 测试建议
1. 在不同尺寸的移动设备上测试展开效果
2. 验证动画是否流畅
3. 确认点击外部区域关闭功能正常工作
4. 测试用户选择功能是否正常
