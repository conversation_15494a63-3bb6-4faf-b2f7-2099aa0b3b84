import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod } from '@ngx-resource/core';
import { PageGridResult, RequestResult } from '../@core/base/request-result';
import { NewsInfo, NewsParams } from './class/news';
/**
 * 消息服务
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root'
})
export class NewsService extends Resource {

  constructor() {
    super();
  }

  /**
   * 消息详情
   */
  @ResourceAction({
    path: '/work-user/api/v2/userCenter/mail/receive/byCode',
  })
  update!: IResourceMethodObservable<{receiveId: string}, RequestResult<any>>;

  /**
   * 消息中心-列表
   */
   @ResourceAction({
    path: '/work-user/api/v2/userCenter/mail/receive/grid',
  })
  gridByNotice!: IResourceMethodObservable<NewsParams, PageGridResult<NewsInfo[]>>;
}
