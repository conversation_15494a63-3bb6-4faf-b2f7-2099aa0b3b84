import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod } from '@ngx-resource/core';
import { PageGridResult, RequestResult } from '../@core/base/request-result';
import { Task } from '../work/class/work';
import { TaskInfo } from './class/home';

@ResourceParams({})
@Injectable({
  providedIn: 'root'
})
/**
 * 首页模块服务
 */
export class HomeModuleService extends Resource {
  constructor() { super(); }

  /**
   * 获取巡检统计信息
   */
  @ResourceAction({
    method: ResourceRequestMethod.Get,
    path: '/work-inspect/api/v2/inspect/app/home/<USER>/statistics',
  })
  task!: IResourceMethodObservable<null, RequestResult<TaskInfo>>;

  /**
   * 今日待办
   */
  @ResourceAction({
    method: ResourceRequestMethod.Get,
    path: '/work-inspect/api/v2/inspect/app/today/toDo',
  })
  backlog!: IResourceMethodObservable<null, PageGridResult<Task[]>>;

  /**
   * 今日事件上报统计
   */
  @ResourceAction({
    method: ResourceRequestMethod.Get,
    path: '/work-inspect/api/v2/inspect/app/event/statistics/work',
  })
  todayReported!: IResourceMethodObservable<null, RequestResult<any[]>>;

  /**
   * 上传错误日志
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    url: 'https://zmpis.geoi.cn:8141/api/v1/basic/info/upload',
  })
  uploadErrorLogs!: IResourceMethodObservable<any, RequestResult<any>>;

}
