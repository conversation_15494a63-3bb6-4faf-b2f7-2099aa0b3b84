import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ModalController } from '@ionic/angular';
import { KeyPoint } from '../../keypoint-view.page';

@Component({
  selector: 'app-keypoint-edit-modal',
  templateUrl: './keypoint-edit-modal.component.html',
  styleUrls: ['./keypoint-edit-modal.component.scss']
})
export class KeypointEditModalComponent implements OnInit {
  @Input() keyPoint: KeyPoint;
  @Output() save = new EventEmitter<KeyPoint>();
  @Output() cancel = new EventEmitter<void>();

  form: FormGroup;
  isSubmitting = false;

  constructor(
    private formBuilder: FormBuilder,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.initializeForm();
  }

  /**
   * 初始化表单
   */
  private initializeForm() {
    this.form = this.formBuilder.group({
      pointName: [
        this.keyPoint?.pointName || '', 
        [Validators.required, Validators.minLength(2), Validators.maxLength(50)]
      ],
      inspectionMethod: [
        this.keyPoint?.type === 'vehicle' ? '巡视' : '巡查',
        [Validators.required]
      ],
      isItRaining: [
        this.keyPoint?.isItRaining || '否',
        [Validators.required]
      ],
      bufferRange: [
        this.keyPoint?.bufferRange || 10,
        [Validators.required, Validators.min(1), Validators.max(1000)]
      ]
    });
  }

  /**
   * 获取表单控件错误信息
   */
  getFieldError(fieldName: string): string {
    const control = this.form.get(fieldName);
    if (control && control.errors && control.touched) {
      if (control.errors['required']) {
        return `${this.getFieldLabel(fieldName)}不能为空`;
      }
      if (control.errors['minlength']) {
        return `${this.getFieldLabel(fieldName)}长度不能少于${control.errors['minlength'].requiredLength}个字符`;
      }
      if (control.errors['maxlength']) {
        return `${this.getFieldLabel(fieldName)}长度不能超过${control.errors['maxlength'].requiredLength}个字符`;
      }
      if (control.errors['min']) {
        return `${this.getFieldLabel(fieldName)}不能小于${control.errors['min'].min}`;
      }
      if (control.errors['max']) {
        return `${this.getFieldLabel(fieldName)}不能大于${control.errors['max'].max}`;
      }
    }
    return '';
  }

  /**
   * 获取字段标签
   */
  private getFieldLabel(fieldName: string): string {
    const labels = {
      pointName: '关键点名称',
      inspectionMethod: '巡检方式',
      isItRaining: '巡检天气',
      bufferRange: '缓冲范围'
    };
    return labels[fieldName] || fieldName;
  }

  /**
   * 检查字段是否有错误
   */
  hasFieldError(fieldName: string): boolean {
    const control = this.form.get(fieldName);
    return !!(control && control.errors && control.touched);
  }

  /**
   * 保存关键点信息
   */
  async onSave() {
    if (this.form.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      
      try {
        const formValue = this.form.value;
        const updatedKeyPoint: KeyPoint = {
          ...this.keyPoint,
          pointName: formValue.pointName,
          name: formValue.pointName,
          type: formValue.inspectionMethod === '巡视' ? 'vehicle' : 'person',
          isItRaining: formValue.isItRaining,
          isRainyDay: formValue.isItRaining === '是',
          bufferRange: formValue.bufferRange
        };

        this.save.emit(updatedKeyPoint);
        await this.modalController.dismiss(updatedKeyPoint, 'save');
      } catch (error) {
        console.error('保存关键点信息失败:', error);
      } finally {
        this.isSubmitting = false;
      }
    } else {
      // 标记所有字段为已触摸，以显示验证错误
      Object.keys(this.form.controls).forEach(key => {
        this.form.get(key)?.markAsTouched();
      });
    }
  }

  /**
   * 取消编辑
   */
  async onCancel() {
    this.cancel.emit();
    await this.modalController.dismiss(null, 'cancel');
  }

  /**
   * 重置表单
   */
  onReset() {
    this.form.reset();
    this.initializeForm();
  }
}
