import { Component, HostListener, Inject, Input, NgZone, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { <PERSON><PERSON><PERSON>ontroller, ModalController, Platform } from '@ionic/angular';
import { Subject, Subscription, BehaviorSubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CurrentTime, DetailsMode } from '../@core/base/environment';
import { ToastService } from '../@core/providers/toast.service';
import { KeyPointsComponent } from '../share/map-component/key-points/key-points.component';
import { LocationSelectComponent } from '../share/map-component/location-select/location-select.component';
import { MapComponent } from '../share/map-component/map/map.component';
import { KeyPoint, KeyPointService } from '../share/map-component/service';
import { MapService } from '../share/map-component/service';
import { Task } from '../work/class/work';
import { CameraListComponent } from './modal/camera-list/camera-list.component';
import { ControlBarComponent } from './control-bar/control-bar.component';
import { EvreportComponent } from './modal/evreport/evreport.component';
import { KeyPointManagerService, LocationService, ModalManagerService, WeatherStateService } from './services';

@Component({
  selector: 'ost-execut',
  templateUrl: './execut.component.html',
  styleUrls: ['./execut.component.scss']
})
export class ExecutComponent implements OnInit, OnDestroy {
  // 地图组件
  @ViewChild('ostMap', { static: false }) ostMap: MapComponent;
  // 任务栏
  @ViewChild('trackBar', { static: false }) ctrlBar: ControlBarComponent;
  // 模式  create: 新任务  continue: 继续任务
  @Input() executState: ExecutState = 'create';
  // 中心点
  @Input() centerPoint: number[];
  // 任务
  @Input() task: Task;
  @Input() isModal = false;
  // 地图点击事件
  @Input() mapClick: boolean;
  // 需要操作的业务图层ID
  layerIds = ['p_pipe_joint_info', 'df_camera'];
  // 关键点列表
  keyPoints: KeyPoint[] = [];
  // 响应式关键点数据和加载状态
  loading$ = new BehaviorSubject<boolean>(false);
  keyPoints$ = new BehaviorSubject<KeyPoint[]>([]);
  // 加载状态
  loading: boolean = false;
  // 打卡按钮显示状态
  canPunchIn: boolean = false;
  // 当前关键点
  currentKeyPoint: any = null;
  // 当前坐标
  coordinate: number[];

  // 订阅
  private weatherSubscription: Subscription;
  private destroy$ = new Subject<void>();

  constructor(
    public modalCtrl: ModalController,
    public platform: Platform,
    private alertCtrl: AlertController,
    @Inject(CurrentTime) public currentTime: string,
    private locationService: LocationService,
    private modalManagerService: ModalManagerService,
    private keyPointManagerService: KeyPointManagerService,
    private weatherStateService: WeatherStateService,
    private keyPointService: KeyPointService,
    private toastSer: ToastService,
    private ngZone: NgZone,
    private mapService: MapService
  ) {
    // 初始化时间
    this.currentTime = String(Date.now());
  }

  async ngOnInit(): Promise<void> {
    // 获取关键点数据
    this.getkeyPoints();

    if (this.executState === 'continue') {
      this.currentTime = this.task.groupCode;
    }

    // 初始化定位配置
    const taskCode = this.task ? this.task.taskCode : '';
    await this.locationService.initLocationConfigure(taskCode, this.currentTime);

    // 🔄 位置监听现在由ControlBarComponent统一管理，通过事件传递位置信息

    // 订阅天气状态变化
    this.weatherSubscription = this.weatherStateService.getWeatherState().subscribe(weatherState => {
      this.task.isItRaining = weatherState.isItRaining;
    });

    // 异步检查定位状态，不阻塞页面加载
    this.checkLocationStatusAsync();
  }

  /**
   * 获取关键点（支持智能状态合并）
   */
  getkeyPoints() {
    console.log(`📍 [执行页面] 开始获取任务 ${this.task.taskCode} 的关键点数据`);
    this.loading$.next(true);
    this.keyPointService.getKeyPointsByTaskCode(this.task.taskCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe(async result => {
        const { code, data, msg } = result;
        if (code === 0) {
          console.log(`📍 [执行页面] 服务器返回 ${(data || []).length} 个关键点`);

          // 智能合并服务器数据和本地缓存状态
          const mergedKeyPoints = await this.keyPointService.mergeKeyPointsWithCache(data || [], this.task.taskCode);

          this.keyPoints = mergedKeyPoints;
          this.keyPoints$.next(this.keyPoints);

          console.log(`📍 [执行页面] 关键点数据更新完成，已巡: ${mergedKeyPoints.filter(p => p.state === '已巡').length}/${mergedKeyPoints.length}`);

          // 初始化关键点
          this.keyPointManagerService.initKeyPoints(this.keyPoints);
          // 新增：渲染关键点及范围
          if (this.ostMap && this.ostMap.setKeyPoints) {
            this.ostMap.setKeyPoints(this.keyPoints);
          }
        } else {
          console.error(`📍 [执行页面] 获取关键点失败: ${msg}`);
          this.toastSer.presentToast(msg, 'danger');
        }
        this.loading$.next(false);
      }, (error) => {
        console.error(`📍 [执行页面] 获取关键点异常:`, error);
        this.loading$.next(false);
      });
  }

  /**
   * 是否展示天气开关
   */
  get showWeatherSwitch(): boolean {
    return this.weatherStateService.shouldShowWeatherSwitch(this.task.inspectionMethod, this.executState);
  }

  /**
   * 获取天气状态
   */
  get isSunny(): boolean {
    return this.weatherStateService.isSunny;
  }

  /**
   * 获取关键点图层
   */
  get KeyPointLayer() {
    return this.keyPointManagerService.getKeyPointLayer();
  }

  /**
   * 获取模态框状态
   */
  get modalStates() {
    return this.modalManagerService.getModalStates();
  }

  /**
   * 切换天气状态
   */
  onToggleChange(event: any) {
    this.weatherStateService.toggleWeather();
  }

  /**
   * 事件上报 新增
   * @param coordinate 坐标信息 
   */
  async onEvReportClick(coordinate): Promise<void> {
    await this.modalManagerService.createModalWithGuard('evReport', {
      component: EvreportComponent,
      componentProps: {
        coordinate,
        keyPoints: this.keyPoints,
        isModal: this.isModal,
        modelMode: DetailsMode.ADD
      },
    }, (data, role) => {
      if (role === 'confirm') {
        // this.ostMap.refreshBusinessLayer('inspect_event', data.eventGeom);
      }
    });
  }

  /**
   * 关键点 新增
   * @param coordinate 坐标信息
   */
  async onKeyPointClick(coordinate): Promise<void> {
    await this.modalManagerService.createModalWithGuard('keyPoint', {
      component: LocationSelectComponent,
      componentProps: {
        coordinate,
        taskCode: this.task.taskCode,
        modelMode: DetailsMode.ADD,
      },
      cssClass: 'app-location-select',
    }, (data, role) => {
      if (role === 'confirm') {
        this.ostMap.refreshBusinessLayer('inspect_point', data.centerCoord);
      }
    });
  }

  /**
   * 位置变化回调
   */
  onLocationChange = (location: BackgroundGeolocationResponse) => {
    // 使用 NgZone.run 确保在后台状态下也能正常执行
    this.ngZone.run(() => {
      // 获取当前位置
      const currentCoord = this.locationService.getCurrentCoordinate(location);
      this.coordinate = currentCoord;

      // 更新关键点（这里会触发关键点提醒服务）
      this.keyPointManagerService.updateLocation(currentCoord);

      // 使用服务方法判断是否在范围内，并获取当前关键点
      const { inRange, keyPoint } = this.keyPointManagerService.isInRange(currentCoord);

      // 检查是否移出了关键点范围且打卡页面已打开
      // if (!inRange && this.modalManagerService.isModalOpen('punchIn')) {
      //   // 自动关闭打卡页面
      //   this.modalManagerService.closeModal('punchIn');
      // }

      this.canPunchIn = inRange;
      this.currentKeyPoint = inRange ? keyPoint : null;

      // 新增：到达关键点时前端变色，并同步更新keyPoints数组
      if (inRange && keyPoint) {
        // 1. 同步更新keyPoints数组
        const idx = this.keyPoints.findIndex(p => p.id === keyPoint.id);
        if (idx !== -1) {
          this.keyPoints[idx].state = '已巡';
        }

        // 2. 地图变色（如果地图可用）
        if (this.ostMap && this.ostMap.updateKeyPointStatus) {
          this.ostMap.updateKeyPointStatus(keyPoint.id, '已巡');
        }

        // 3. 自动打卡并缓存/批量上传，根据结果决定是否刷新关键点数据
        this.keyPointService.clockInWithCacheBatch(
          this.task.taskCode,
          keyPoint.pointCode,
          this.coordinate[0],
          this.coordinate[1]
        ).then(result => {
          // 如果网络通畅且自动打卡成功，延迟重新获取关键点数据
          if (result.shouldRefreshKeyPoints) {
            console.log('🔄 自动打卡成功，准备刷新关键点数据');

            // 临时禁用提醒，避免数据刷新时重复提醒
            this.keyPointManagerService.temporarilyDisableAlert(5000);

            // 延迟3秒刷新数据，确保语音提醒完成
            setTimeout(() => {
              this.getkeyPoints();
            }, 3000);
          }
        }).catch(error => {
          console.error('自动打卡失败:', error);
        });
      }
    });
  };

  /**
   * 视频列表
   */
  async onVideoClick() {
    await this.modalManagerService.createModalWithGuard('video', {
      component: CameraListComponent,
      componentProps: {
        mapService: this.mapService  // 传递MapService实例
      },
      cssClass: 'camera-list-modal',
    });
  }

  /**
   * 关键点列表
   */
  async seePoints() {
    await this.modalManagerService.createModalWithGuard('points', {
      component: KeyPointsComponent,
      componentProps: {
        disabledClick: false,
        taskCode: this.task.taskCode,
        loading$: this.loading$,
        keyPoints$: this.keyPoints$,
        showDataStatus: false, // 在执行页面不显示数据状态列
        currentCoordinate: this.coordinate, // 传递当前实时坐标
      },
      cssClass: 'camera-list-modal'
    }, (data, role) => {
      // 处理关键点列表模态框关闭后的回调
      if (data && data.shouldRefreshKeyPoints) {
        console.log('🔄 手动打卡成功，准备刷新关键点数据');

        // 临时禁用提醒，避免数据刷新时重复提醒
        this.keyPointManagerService.temporarilyDisableAlert(5000);

        // 延迟3秒刷新数据，确保语音提醒完成
        setTimeout(() => {
          this.getkeyPoints();
        }, 3000);
      }
    });
  }

  /**
   * 地图加载完成后的回调
   */
  onMapLoaded() {
    // 注册地图组件到地图服务
    this.mapService.registerMapComponent(this.ostMap);
  }

  @HostListener('document:ionBackButton', ['$event'])
  async onCloseClick($event: CustomEvent): Promise<void> {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      $event.stopPropagation();
      $event.preventDefault();
      if (this.ctrlBar.trackRunState !== 'running' &&
        this.ctrlBar.trackRunState !== 'pause') {
        await this.modalCtrl.dismiss();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // 🔄 位置监听现在由ControlBarComponent管理，这里只清理LocationService的其他资源
    // 注意：不调用locationService.destroy()，因为位置监听由ControlBarComponent管理
    this.locationService.stopBackgroundGeolocation();

    // 清理关键点服务
    this.keyPointManagerService.destroy();

    // 清理天气订阅
    if (this.weatherSubscription) {
      this.weatherSubscription.unsubscribe();
    }

    // 重置模态框状态
    this.modalManagerService.resetModalStates();
  }

  /**
   * 异步检查定位状态（非阻塞）
   */
  private checkLocationStatusAsync(): void {
    // 使用setTimeout确保页面加载完成后再检查
    setTimeout(async () => {
      try {
        const locationStatus = await this.locationService.checkLocationStatus();

        if (!locationStatus.canUseLocation) {
          await this.showLocationAlert(locationStatus);
        }
      } catch (error) {
        console.error('检查定位状态失败:', error);
        // 异步检查失败时只记录日志，不影响用户操作
      }
    }, 500); // 延迟500ms，确保页面渲染完成
  }

  /**
   * 显示定位状态提醒
   */
  private async showLocationAlert(locationStatus: any): Promise<void> {
    // 如果没有权限，优先显示简单的Toast提示
    if (!locationStatus.hasPermission) {
      this.toastSer.presentToast('请开启定位权限以获得更好的巡检体验', 'warning');
      return;
    }

    // 如果有权限但GPS未开启，显示简化的提醒
    if (!locationStatus.isLocationEnabled) {
      const alert = await this.alertCtrl.create({
        header: '定位提醒',
        message: '建议开启GPS定位服务以获得更精确的巡检轨迹',
        backdropDismiss: true,
        buttons: [
          {
            text: '知道了',
            role: 'cancel'
          },
          {
            text: '去设置',
            handler: () => {
              this.toastSer.presentToast('请在系统设置中开启GPS定位服务', 'primary');
            }
          }
        ]
      });

      await alert.present();

      // 3秒后自动关闭提醒
      setTimeout(() => {
        alert.dismiss().catch(() => {});
      }, 3000);
    }
  }
}

/**
 * 执行状态
 */
export declare type ExecutState = 'create' | 'continue';