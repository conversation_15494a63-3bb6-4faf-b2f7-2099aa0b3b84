<ion-content>
  <div class="login-bg">
    <img
      class="bgCircle bgCircle2"
      src="/assets/login/bgCircle2.png"
    />
    <img
      class="bgCircle bgCircle3"
      src="/assets/login/bgCircle3.png"
    />
    <img
      class="bgCircle bgCircle4"
      src="/assets/login/bgCircle4.png"
    />
  </div>

  <!-- 登录容器 -->
  <div>
    <form
      class="login-form"
      [formGroup]="loginForm"
      (ngSubmit)="onLogin(loginForm.value)"
    >
      <!-- 项目名 -->
      <div [ngClass]="smallScreen? 'login-form-subtitle-small':'login-form-subtitle'">山西巡检</div>
      <!-- 用户名 -->
      <div class="login-form-input">
        <ion-icon
          class="login-form-input-icon"
          name="person"
        ></ion-icon>
        <ion-input
          formControlName="account"
          clearInput
        ></ion-input>
        <span
          class="error"
          *ngIf="loginForm.get('account').touched && loginForm.get('account').hasError('required')"
        >
          用户名不能为空
        </span>
      </div>
      <!-- 密码 -->
      <div class="login-form-input">
        <ion-icon
          class="login-form-input-icon"
          [name]=isShow
          (click)="onPasswordShow()"
        ></ion-icon>
        <ion-input
          [type]=pwShow
          formControlName="userPasd"
          clearInput
        ></ion-input>
        <span
          class="error"
          *ngIf="loginForm.get('userPasd').touched && loginForm.get('userPasd').hasError('required')"
        >
          密码不能为空
        </span>
      </div>
      <!-- 记住密码 -->
      <div class="login-form-assist">
        <div style="display:flex;align-items: center;">
          <ion-checkbox color="light" mode="md" formControlName="autoLogin" (ngModelChange)="onAutoLoginChange($event)"></ion-checkbox>
          <span style="margin-left: 10px;color: #ffffff;">记住密码</span>
        </div>
      </div>
      <div [ngClass]="smallScreen? 'login-form-foot-small':'login-form-foot'">
        <!-- 登录按钮 -->
        <ion-button
          type="submit"
          color="primary"
          style="width: 100%;"
          [disabled]="!loginForm.valid || isLoggingIn"
        >登录</ion-button>
      </div>

    </form>
  </div>

</ion-content>