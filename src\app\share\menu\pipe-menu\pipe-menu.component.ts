import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Platform } from '@ionic/angular';
import { map } from 'rxjs/operators';
import { RequestResult } from 'src/app/@core/base/request-result';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { OstTreeListComponent } from '../../ost-tree-list/ost-tree-list.component';
import { OstTreeListItem } from '../../ost-tree-list/ost-tree-list.service';
import { ShareModuleService } from '../../share.service';
import { InputSearchSourceService } from '../../input-search/input-search-source.service';

@Component({
  selector: 'ost-pipe-menu',
  templateUrl: './pipe-menu.component.html',
  styleUrls: ['./pipe-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PipeMenuComponent implements OnInit, OnChanges {
  @ViewChild('menu', { static: false }) menu: OstTreeListComponent;
  @Input() interfaceUrl: string;
  @Input() depCode: string;
  @Input() showSearch: boolean = true;

  @Output() toggleSubMenu = new EventEmitter<any>();
  @Output() itemClick = new EventEmitter<any>();

  loading: boolean;
  items: OstTreeListItem[] = [];
  pipeTree: any; // 保存原始管线树数据
  screenHeight: any;
  // 搜索参数
  params = '';

  constructor(
    public cd: ChangeDetectorRef,
    public userSer: UserInfoService,
    public netSer: ShareModuleService,
    private searchSer: InputSearchSourceService,
    public platform: Platform
  ) { }

  ngOnInit(): void {
    this.screenHeight = (this.platform.height() - 56 - 50);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.depCode && !changes.depCode.firstChange) {
      // 使用setTimeout将清空操作推迟到下一个事件循环
      setTimeout(() => {
        this.searchSer.change('', '');
      });
      
      if (this.depCode) {
        this.loadMenuTree(this.depCode);
      }
    }
  }

  /**
   * 设置选中的菜单项
   */
  setSelectItemById(id: string): void {
    // 根据id查找对应的菜单项
    const selectItem = this.items.find(itme => (itme.data.pipelineCode === id));
    if (selectItem) {
      selectItem.expanded = true;
      this.menu.setSelectItem(selectItem);
      this.cd.markForCheck();
    }
  }

  /**
   * 设置选中的菜单项
   */
  setSelectItem(item: OstTreeListItem): void {
    this.menu.setSelectItem(item);
  }

  /**
   * 切换子菜单
   */
  onToggleSubMenu(item: OstTreeListItem): void {
    this.toggleSubMenu.emit(item);
  }

  /**
   * 点击菜单项
   */
  onItemClick(item: OstTreeListItem): void {
    this.itemClick.emit(item);
  }

  /**
   * 获取管线数据
   */
  loadMenuTree(depCode: string): void {
    this.depCode = depCode;
    this.loading = true;
    this.netSer.getRequest({ interfaceUrl: this.interfaceUrl, depCode })
      .pipe(map((data: RequestResult<any[]>) => this.transformation(data)))
      .subscribe(res => {
        this.items = res;
        this.pipeTree = res; // 保存原始数据用于搜索
        this.loading = false;
        this.cd.markForCheck();
      }, error => {
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  /**
   * 数据转换
   */
  transformation(res: any): OstTreeListItem[] {
    const data = res.data || res;
    return this.transformPipelineData(data);
  }

  /**
   * 将管道数据转换为树形结构
   */
  public transformPipelineData(data: any[]): OstTreeListItem[] {
    if (!data || !data.length) {
      return [];
    }

    const convertToTreeItem = (item: any): OstTreeListItem => {
      const treeItem: OstTreeListItem = {
        title: item.pipelineName,
        data: item
      };

      if (item.children && item.children.length) {
        treeItem.children = item.children.map(child => convertToTreeItem(child));
      }

      return treeItem;
    };

    return data.map(item => convertToTreeItem(item));
  }

  /**
   * 重置搜索
   */
  onReset(): void {
    this.params = '';
    this.items = this.pipeTree;
  }

  /**
   * 搜索管线
   */
  onSearch() {
    // 过滤并显示匹配结果的树形结构
    if (this.params !== '') {
      const searchData = this.filterTree(this.pipeTree, this.params);
      this.items = searchData;
    }
  }

  /**
   * 过滤搜索树形数据
   * @param nodes 树形节点数组
   * @param query 搜索关键词
   */
  filterTree(nodes: any[], query: string): any[] {
    let results: any[] = [];
    if (!nodes) return results;

    nodes.forEach(node => {
      // 如果该节点的标题包含查询字符串
      if (node.title && node.title.toLowerCase().includes(query.toLowerCase())) {
        // 设置节点为已展开状态
        node.expanded = true;
        // 将节点添加至结果数组
        results.push(node);
      }
      // 如果节点有子节点
      if (node.children) {
        // 对子节点进行递归模糊搜索
        const childResults = this.filterTree(node.children, query);
        // 如果子节点中有匹配且当前节点尚未被添加到结果数组中
        if (childResults.length > 0 && !results.find(res => res.data && res.data.pipelineCode === node.data.pipelineCode)) {
          // 添加该节点至结果数组
          results.push({ ...node, expanded: true, children: childResults });
        }
      }
    });

    return results;
  }
}
