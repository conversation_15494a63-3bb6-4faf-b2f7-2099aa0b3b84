.div-box {
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .p-img {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    border-radius: 3px;
    height: 40px;
    width: 40px;
    text-align: center;

    img {
      width: 26px;
      height: 26px
    }

    // 红色数字徽章样式
    .badge {
      position: absolute;
      top: -6px;
      right: -6px;
      background: #ff4444;
      color: white;
      border-radius: 10px;
      min-width: 18px;
      height: 18px;
      font-size: 11px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 4px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      border: 2px solid white;
      z-index: 10;
      
      // 动画效果
      animation: badgePulse 2s infinite;
    }
  }

  .name {
    font-size: 14px;
    text-align: center;
    margin-top: 5px;
    color: #000;
  }
}

// 徽章脉冲动画
@keyframes badgePulse {
  0% {
    transform: scale(1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(255, 68, 68, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
}