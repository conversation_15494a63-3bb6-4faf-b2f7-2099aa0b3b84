import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Feature } from 'ol';
import Point from 'ol/geom/Point';
import { Circle as CircleStyle, Fill, Stroke, Style, Text } from 'ol/style';
import { fromCircle } from 'ol/geom/Polygon';
import { Circle as CircleGeom } from 'ol/geom';

const POINT_RADIUS = 5;

export class KeyPointRenderer {
  static getKeyPointStyle(point: any, index: number, showLabel: boolean) {
    return new Style({
      image: new CircleStyle({
        radius: POINT_RADIUS,
        fill: new Fill({
          color: point.state === '已巡' ? '#4CD964' : '#248CFF'
        }),
        stroke: new Stroke({
          color: '#fff',
          width: 1
        })
      }),
      text: showLabel ? new Text({
        text: point.pointName || '',
        font: '16px Open Sans',
        fill: new Fill({ color: '#222' }),
        offsetY: -10,
        offsetX: (index % 2 === 0) ? -20 : 20,
        padding: [2, 4, 2, 4],
        overflow: true
      }) : undefined
    });
  }

  static renderKeyPoints(layer: VectorLayer<VectorSource<any>>, points: any[], view: any) {
    layer.getSource().clear();
    const showLabel = view && view.getZoom && view.getZoom() > 15;
    points.forEach((point, index) => {
      let coord: [number, number];
      try {
        coord = JSON.parse(point.point);
      } catch {
        return;
      }
      // 范围圈
      if (point.bufferTrans && !isNaN(point.bufferTrans) && point.bufferTrans > 0 && point.bufferTrans < 10000) {
        try {
          const bufferCircle = new CircleGeom(coord, point.bufferTrans);
          const bufferFeature = new Feature(fromCircle(bufferCircle, 64));
        bufferFeature.setId(point.id + '_buffer');
        bufferFeature.setStyle(
          new Style({
            fill: new Fill({
              color: point.state === '已巡'
                ? 'rgba(76,217,100,0.2)'
                : 'rgba(36,140,255,0.2)'
            }),
            stroke: new Stroke({
              color: point.state === '已巡'
                ? '#4CD964'
                : '#248CFF',
              width: 1
            })
          })
        );
        layer.getSource().addFeature(bufferFeature);
         } catch (error) {
           console.warn('创建关键点范围圈失败:', point.id, 'bufferTrans:', point.bufferTrans, error);
         }
      }
      // 关键点本身
      const pointFeature = new Feature(new Point(coord));
      pointFeature.setId(point.id);
      pointFeature.setStyle(this.getKeyPointStyle(point, index, showLabel));
      layer.getSource().addFeature(pointFeature);
    });
  }

  public static setupAutoRefresh(layer: VectorLayer<VectorSource<any>>, view: any, getPoints: () => any[]) {
    view.on('change:resolution', () => {
      const showLabel = view.getZoom() > 15;
      const points = getPoints();
      if (layer && points && points.length) {
        const features = layer.getSource().getFeatures();
        let pointIdx = 0;
        features.forEach((feature) => {
          if (feature.getGeometry() instanceof Point) {
            const point = points.find(p => p.id === feature.getId());
            if (point) {
              feature.setStyle(this.getKeyPointStyle(point, pointIdx, showLabel));
              pointIdx++;
            }
          }
        });
      }
    });
  }

  public static updateKeyPointStatus(layer: VectorLayer<VectorSource<any>>, points: any[], id: string, status: '已巡' | '未巡', view: any) {
    const feature = layer.getSource().getFeatureById(id);
    if (feature) {
      const point = points.find(p => p.id === id);
      const index = points.findIndex(p => p.id === id);
      if (point) {
        point.state = status;
        feature.setStyle(this.getKeyPointStyle(point, index, view.getZoom() > 15));
      }
    }
    const bufferFeature = layer.getSource().getFeatureById(id + '_buffer');
    if (bufferFeature) {
      const point = points.find(p => p.id === id);
      if (point) {
        bufferFeature.setStyle(
          new Style({
            fill: new Fill({
              color: status === '已巡'
                ? 'rgba(76,217,100,0.2)'
                : 'rgba(36,140,255,0.2)'
            }),
            stroke: new Stroke({
              color: status === '已巡' ? '#4CD964' : '#248CFF',
              width: 1
            })
          })
        );
      }
    }
  }
}