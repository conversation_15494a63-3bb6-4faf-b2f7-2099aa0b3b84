<ion-header [translucent]="true" *ngIf="this.radioList.length >= 10">
  <div class="search-bar">
    <div class="search-bar-parent">
      <ion-icon class="login-form-input-icon" name="search-outline"></ion-icon>
      <ion-input
        placeholder="请输入查询字段"
        [(ngModel)]="params"
      >
      </ion-input>
    </div>
    <ion-note slot="end" class="title-end-operate" style="padding-right: 60px;" (click)="onReset()">
      重置
    </ion-note>
    <ion-note slot="end" class="title-end-operate" style="padding-right:10px;" (click)="onSearch()">
      搜索
    </ion-note>
  </div>
</ion-header>

<ion-content>
  <ng-container *ngIf="list?.length>0;else noData">
    <ion-radio-group [(ngModel)]="radioValue" (ngModelChange)="onChangeRadio($event)">

      <ng-container *ngFor="let item of list">
        <ion-item-divider (click)="onRadioClick(item)">
          <ion-label>{{item.name}}</ion-label>
          <ion-radio slot="start" value="{{item.value}}"></ion-radio>
        </ion-item-divider>
      </ng-container>
  
    </ion-radio-group>
  </ng-container>
</ion-content>

<ng-template #noData>
  <div class="no-data">
    <img src="assets/menu/box2.png" style="padding-top: 50px;" />
    <!-- 暂无数据 -->
    <span class="no-data-span">暂无数据</span>
  </div>
</ng-template>