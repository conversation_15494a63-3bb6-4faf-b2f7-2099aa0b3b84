@import "../../../theme/variables.scss";

// --ion-color-primary-contrast 白色
// --ion-color-primary 默认蓝色
.body-ios {
  padding: 0px;
  background-color: var(--ion-color-primary);
  padding-top: 48px;
  height: 130px;
}

.body {
  padding: 0px;
  background-color: var(--ion-color-primary);
  padding-top: 8px;
  height: 90px;
}

.guide {
  color: var(--ion-color-primary-contrast);
  padding-left: 16px;
}

.box {
  text-align: center;
  height: 80px;
  // border: 1px solid #adb0b8;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  margin: 12px 16px;
  background-color: var(--ion-color-primary-contrast);
  padding-top: 6px;
  margin-top: 12px;

  span {
    text-align: center;
    color: var(--ion-color-primary);
    // font-weight: 600;
    display: block;
    padding-bottom: 4px;
  }

  img {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}

.row {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
  padding-top: 12px;

  .row-item {
    width: 30%;
    position: relative;
    text-align: center;

    .item-headerInfo {
      display: inline-block;
      // margin-bottom: 4px;
      color: #888888;
      font-size: 14px;
      line-height: 20px;
    }

    .item-cont {
      margin: 0;
      color: var(--ion-color-primary);
      font-size: 18px;
      line-height: 20px;
      font-weight: 600;
    }

    em {
      position: absolute;
      top: 10px;
      right: 0;
      width: 2px;
      height: 26px;
      background-color: #f6f6f6;
    }
  }
}