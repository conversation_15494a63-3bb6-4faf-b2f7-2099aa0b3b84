import { Injectable, ElementRef } from '@angular/core';
import { ImageGestureState, GestureConfig } from './image-gesture.model';
import { GestureManagerService } from './gesture-manager.service';
import { PerformanceOptimizerService } from './performance-optimizer.service';
import { BoundaryCalculatorService } from './boundary-calculator.service';
import { InertiaScrollHandlerService } from './inertia-scroll-handler.service';
import { PinchGestureHandlerService } from './pinch-gesture-handler.service';
import { PanGestureHandlerService } from './pan-gesture-handler.service';

/**
 * 图片手势服务（重构版）
 * 作为各个手势处理器的协调者，负责统一管理图片的缩放、拖拽、双击重置等手势操作
 */
@Injectable({
  providedIn: 'root'
})
export class ImageGestureService {
  private state: ImageGestureState;
  private config: GestureConfig;

  constructor(
    private gestureManager: GestureManagerService,
    private performanceOptimizer: PerformanceOptimizerService,
    private boundaryCalculator: BoundaryCalculatorService,
    private inertiaScrollHandler: InertiaScrollHandlerService,
    private pinchGestureHandler: PinchGestureHandlerService,
    private panGestureHandler: PanGestureHandlerService
  ) {
    this.initializeState();
    this.initializeConfig();
  }

  /**
   * 初始化手势状态
   */
  private initializeState(): void {
    this.state = {
      scale: 1,
      lastScale: 1,
      translateX: 0,
      translateY: 0,
      lastTranslateX: 0,
      lastTranslateY: 0,
      isDragging: false,
      // 惯性滑动状态
      velocityX: 0,
      velocityY: 0,
      isInertiaScrolling: false
    };
  }

  /**
   * 初始化默认配置
   */
  private initializeConfig(): void {
    this.config = {
      minScale: 0.8,
      maxScale: 5,
      dampingFactor: 0.3, // 降低阻尼系数，减少过度敏感
      resetAnimationDuration: 300,
      minVisibleRatio: 0.25, // 默认保持25%可见
      // 惯性滑动配置
      inertiaEnabled: true,
      inertiaFriction: 0.92, // 稍微降低摩擦力，让惯性更快停止
      inertiaMinVelocity: 1.0, // 提高最小速度阈值，减少微小移动的惯性
      inertiaMaxDuration: 800 // 减少最大持续时间
    };
  }

  /**
   * 获取图片尺寸信息（使用性能优化器）
   */
  private calculateImageDimensions() {
    return this.performanceOptimizer.calculateImageDimensions(
      this.state.scale,
      this.gestureManager.getImageElement(),
      this.gestureManager.getContainerElement()
    );
  }



  /**
   * 初始化手势识别
   * @param imageElement 图片元素引用
   * @param containerElement 容器元素（可选，默认使用图片的父元素）
   * @param config 手势配置（可选）
   */
  initializeGestures(
    imageElement: ElementRef,
    containerElement?: HTMLElement,
    config?: Partial<GestureConfig>
  ): void {
    // 确保每次初始化时都重置状态，避免保留上次的缩放和位移
    this.initializeState();

    // 合并配置
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // 初始化手势管理器
    const hammer = this.gestureManager.initializeGestures(imageElement, containerElement, this.config);

    if (hammer) {
      this.bindGestureEvents();
      this.addTouchListeners();
    }
  }

  /**
   * 绑定手势事件（重构版）
   */
  private bindGestureEvents(): void {
    this.gestureManager.bindEvents({
      onPinch: (ev: any) => this.handlePinch(ev),
      onPinchStart: (ev: any) => this.handlePinchStart(ev),
      onPinchEnd: () => this.handlePinchEnd(),
      onPanStart: () => this.handlePanStart(),
      onPan: (ev: any) => this.handlePan(ev),
      onPanEnd: () => this.handlePanEnd(),
      onTap: () => this.resetTransform()
    });
  }

  /**
   * 添加触摸事件监听器
   */
  private addTouchListeners(): void {
    this.gestureManager.addTouchListeners({
      onTouchStart: this.handleTouchStart,
      onTouchMove: this.handleTouchMove,
      onTouchEnd: this.handleTouchEnd
    });
  }

  /**
   * 处理缩放开始
   */
  private handlePinchStart(ev: any): void {
    // 添加缩放状态类，禁用CSS过渡
    this.toggleClass('pinching', true);

    this.pinchGestureHandler.handlePinchStart(
      ev,
      () => this.performanceOptimizer.clearCache(),
      () => this.inertiaScrollHandler.stopInertiaScroll()
    );
  }

  /**
   * 处理缩放手势
   */
  private handlePinch(ev: any): void {
    // 缩放过程中减少约束计算，提高流畅度
    const newScale = this.pinchGestureHandler.handlePinch(
      ev,
      this.config,
      () => this.performanceOptimizer.clearCache(),
      () => {} // 缩放过程中暂时不进行位移约束，在结束时统一处理
    );
    this.state.scale = newScale;
    this.pinchGestureHandler.setLastScale(newScale);
  }

  /**
   * 处理缩放结束
   */
  private handlePinchEnd(): void {
    // 移除缩放状态类，恢复CSS过渡
    this.toggleClass('pinching', false);

    this.pinchGestureHandler.handlePinchEnd(
      this.state.scale,
      () => this.constrainTranslation()
    );

    // 如果缩放回到初始大小或更小，重置位移
    if (this.state.scale <= 1) {
      this.updateTranslation(0, 0, true);
    }
  }

  /**
   * 处理触摸事件
   */
  private handleTouchStart = (ev: TouchEvent): void => {
    if (ev.touches.length > 1) ev.preventDefault();
  }

  private handleTouchMove = (ev: TouchEvent): void => {
    // 只有在图片放大时或多点触控时才阻止默认行为
    if ((this.state.scale > 1 && this.state.isDragging) || ev.touches.length > 1) {
      ev.preventDefault();
    }
  }

  private handleTouchEnd = (_ev: TouchEvent): void => {
    // 触摸结束处理（如果需要的话）
  }

  /**
   * 处理拖拽开始
   */
  private handlePanStart(): void {
    // 如果图片处于初始大小，不允许开始拖拽
    if (this.state.scale <= 1) {
      return;
    }

    this.panGestureHandler.handlePanStart(
      () => this.inertiaScrollHandler.stopInertiaScroll(),
      (className: string, add: boolean) => this.toggleClass(className, add),
      () => this.performanceOptimizer.clearCache(),
      () => this.inertiaScrollHandler.clearVelocityTracker()
    );
    this.state.isDragging = true;
  }

  /**
   * 处理拖拽
   */
  private handlePan(ev: any): void {
    // 如果图片处于初始大小，不允许移动
    if (this.state.scale <= 1) {
      return;
    }

    // 性能优化：节流控制，避免过于频繁的更新
    if (!this.performanceOptimizer.shouldUpdate()) {
      return;
    }

    const dimensions = this.calculateImageDimensions();
    const bounds = dimensions ? this.boundaryCalculator.calculateDragBounds(
      dimensions.containerRect,
      dimensions.scaledWidth,
      dimensions.scaledHeight,
      this.config,
      this.state.scale
    ) : null;

    this.panGestureHandler.handlePan(
      ev,
      this.config,
      dimensions,
      bounds,
      (deltaX: number, deltaY: number) => this.inertiaScrollHandler.addVelocitySample(deltaX, deltaY),
      (x: number, y: number) => this.updateTranslation(x, y),
      (x: number, y: number, bounds) => this.boundaryCalculator.constrainToBounds(x, y, bounds),
      this.state.scale // 传递当前缩放级别
    );
  }

  /**
   * 约束当前位移在有效范围内
   */
  private constrainTranslation(): void {
    // 如果图片处于初始大小或更小，强制位移为0
    if (this.state.scale <= 1) {
      this.updateTranslation(0, 0, true);
      return;
    }

    const dimensions = this.calculateImageDimensions();
    if (!dimensions) return;

    // 计算边界限制
    const bounds = this.boundaryCalculator.calculateDragBounds(
      dimensions.containerRect,
      dimensions.scaledWidth,
      dimensions.scaledHeight,
      this.config,
      this.state.scale
    );

    // 约束当前位移在有效范围内
    const constrained = this.boundaryCalculator.constrainToBounds(this.state.translateX, this.state.translateY, bounds);
    this.updateTranslation(constrained.x, constrained.y, true);
  }

  /**
   * 智能边界调整：当图片接近边界时进行微调
   * 确保用户体验的流畅性
   */
  private smartBoundaryAdjustment(): void {
    const dimensions = this.calculateImageDimensions();
    if (!dimensions) return;

    // 检查是否需要边界调整
    const bounds = this.boundaryCalculator.calculateDragBounds(
      dimensions.containerRect,
      dimensions.scaledWidth,
      dimensions.scaledHeight,
      this.config,
      this.state.scale
    );

    // 如果当前位置超出边界，进行平滑调整
    const needsAdjustment = this.boundaryCalculator.needsBoundaryAdjustment(
      this.state.translateX,
      this.state.translateY,
      bounds
    );

    if (needsAdjustment) {
      // 添加平滑过渡效果
      this.toggleClass('resetting', true);

      // 调整到边界内
      const constrained = this.boundaryCalculator.constrainToBounds(this.state.translateX, this.state.translateY, bounds);
      this.updateTranslation(constrained.x, constrained.y, true);

      // 移除过渡效果
      setTimeout(() => {
        this.toggleClass('resetting', false);
      }, 200);
    }
  }

  /**
   * 处理拖拽结束
   */
  private handlePanEnd(): void {
    // 如果图片处于初始大小，确保重置位移并清除拖拽状态
    if (this.state.scale <= 1) {
      this.state.isDragging = false;
      this.updateTranslation(0, 0, true); // 重置到原始位置
      return;
    }

    this.panGestureHandler.handlePanEnd(
      this.state.translateX,
      this.state.translateY,
      (className: string, add: boolean) => this.toggleClass(className, add),
      () => this.startInertiaScroll(),
      () => this.smartBoundaryAdjustment(),
      (x: number, y: number, updateLast: boolean) => this.updateTranslation(x, y, updateLast),
      () => this.inertiaScrollHandler.isScrolling()
    );
    this.state.isDragging = false;
  }

  /**
   * 启动惯性滑动
   */
  private startInertiaScroll(): void {
    this.inertiaScrollHandler.startInertiaScroll(
      this.config,
      (x: number, y: number) => this.updateTranslation(x, y),
      () => ({ x: this.state.translateX, y: this.state.translateY }),
      () => {
        const dimensions = this.calculateImageDimensions();
        return dimensions ? this.boundaryCalculator.calculateDragBounds(
          dimensions.containerRect,
          dimensions.scaledWidth,
          dimensions.scaledHeight,
          this.config,
          this.state.scale
        ) : null;
      },
      this.state.scale // 传递当前缩放级别
    );
    this.state.isInertiaScrolling = this.inertiaScrollHandler.isScrolling();
  }

  /**
   * 通用样式类管理
   */
  private toggleClass(className: string, add: boolean): void {
    const imageElement = this.gestureManager.getImageElement();
    if (imageElement?.nativeElement) {
      imageElement.nativeElement.classList.toggle(className, add);
    }
  }

  /**
   * 更新位移状态
   */
  private updateTranslation(x: number, y: number, updateLast: boolean = false): void {
    this.state.translateX = x;
    this.state.translateY = y;
    this.panGestureHandler.updateTranslation(x, y, updateLast);

    if (updateLast) {
      this.state.lastTranslateX = x;
      this.state.lastTranslateY = y;
    }
  }

  /**
   * 重置图片变换
   */
  resetTransform(): void {
    this.toggleClass('resetting', true);

    // 重置状态
    this.initializeState();
    this.pinchGestureHandler.resetScale();
    this.panGestureHandler.resetTranslation();
    this.inertiaScrollHandler.reset();

    // 移除重置样式类
    setTimeout(() => {
      this.toggleClass('resetting', false);
    }, this.config.resetAnimationDuration);
  }

  /**
   * 获取当前变换状态
   */
  getTransformState(): ImageGestureState {
    return { ...this.state };
  }

  /**
   * 获取变换样式字符串
   */
  getTransformStyle(): string {
    return `scale(${this.state.scale}) translate(${this.state.translateX}px, ${this.state.translateY}px)`;
  }

  /**
   * 销毁手势识别
   */
  destroy(): void {
    // 停止惯性滑动
    this.inertiaScrollHandler.stopInertiaScroll();

    // 清理移动端事件监听器
    this.gestureManager.removeTouchListeners({
      onTouchStart: this.handleTouchStart,
      onTouchMove: this.handleTouchMove,
      onTouchEnd: this.handleTouchEnd
    });

    // 销毁手势管理器
    this.gestureManager.destroy();

    // 清理缓存和追踪器
    this.performanceOptimizer.clearCache();
    this.inertiaScrollHandler.reset();

    // 重置状态到初始值，确保下次使用时是干净的状态
    this.initializeState();
    this.pinchGestureHandler.resetScale();
    this.panGestureHandler.resetTranslation();
  }
}
