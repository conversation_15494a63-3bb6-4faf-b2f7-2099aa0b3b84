<ion-header
  class="sticky"
  [translucent]="true"
>
  <ion-toolbar color="primary">
    <ion-buttons
      slot="start"
      class="title-start-back"
    >
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <!-- 实时监控 -->
    <ion-title>实时监控</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ost-org-menu
    (itemClick)="onOrgMenu($event)"
    [showSearch]="false"
    [isDefaultSelect]="false"
    [showDirBtn]="true"
    dirBtnText="实时监控"
    [autoExpandLevels]="4"
  ></ost-org-menu>
</ion-content>