import { DatePipe } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BackgroundGeolocation, BackgroundGeolocationConfig, BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { Modal<PERSON>ontroller, ToastController } from '@ionic/angular';
import centroid from '@turf/centroid';
import { merge, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PageGridResult } from 'src/app/@core/base/request-result';
import { ExecutComponent } from 'src/app/execut/execut.component';
import { ExecutService } from 'src/app/execut/execut.service';
import { Task } from 'src/app/work/class/work';
import { PageEventService } from '../home.event';
import { HomeModuleService } from '../home.service';

@Component({
  selector: 'app-backlog',
  templateUrl: './backlog.page.html',
  styleUrls: ['./backlog.page.scss'],
})
export class BacklogPage implements OnInit, OnDestroy {
  onUploadLength = 0;
  locationConfig: BackgroundGeolocationConfig;
  nowDate = this.dateTransform(new Date(), 'yyyy-MM-dd');
  listDtata: any = [];
  patrolTasks: any[] = [];
  inspectionTasks: any[] = [];
  isLoading = false;
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    private datePipe: DatePipe, private dataEvent: PageEventService,
    private netSer: HomeModuleService, private exeSer: ExecutService,
    private router: Router, private modalCtrl: ModalController,
    private backgroundGeolocation: BackgroundGeolocation,
    private toastCtrl: ToastController,
  ) { }
  ngOnInit(): void {
    // 页面数据更新
    merge(
      this.dataEvent.receiveDataForPageType('home'),
      this.dataEvent.receiveDataForPageType('refreshCompletionRate')
    )
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: boolean) => {
        if (state) {
          this.loadBacklog();
        }
      });
    this.loadBacklog();
    // 检测未上传数据
    this.checkData();
  }

  loadBacklog(): void {
    this.isLoading = true;
    this.netSer.backlog()
      .pipe(takeUntil(this.destroy$))
      .subscribe(this.loadBacklogSuccess, this.loadBacklogError);
  }

  loadBacklogSuccess = (result: PageGridResult<Task[]>) => {
    this.listDtata = result.data || [];
    this.classifyTasks(this.listDtata);
    this.isLoading = false;
  }

  /**
   * 分类任务：将任务分为巡视和巡查两类
   */
  private classifyTasks(tasks: Task[]): void {
    this.patrolTasks = [];
    this.inspectionTasks = [];

    if (!tasks || tasks.length === 0) {
      return;
    }

    const activeTasks = tasks.filter(task => this.isActiveTask(task.status));

    activeTasks.forEach(task => {
      const inspectionMethod = (task as any).inspectionMethod;
      
      if (inspectionMethod === '徒步' || inspectionMethod === '巡视') {
        this.patrolTasks.push(task);
      } else if (inspectionMethod === '车辆' || inspectionMethod === '巡查') {
        this.inspectionTasks.push(task);
      } else {
        this.patrolTasks.push(task);
      }
    });
  }

  loadBacklogError = (error: any) => {
    this.isLoading = false;
    this.onToast('巡检任务加载失败,请重试！');
  }

  goWork(): void {
    this.router.navigateByUrl('tabs/work');
  }

  onStartClick(task: Task): void {
    task.status === '执行中' ? this.getTaskInfo(task) : this.startTask(task);
  }

  getTaskInfo(task: Task) {
    const originalTaskCode = task.taskCode;
    this.exeSer.continueTasck({ taskCode: originalTaskCode }).subscribe((ret) => {
      if (ret.code === 0 && ret.data) {
        const { taskCode: apiTaskCode, ...apiData } = ret.data;
        const newTaskInfo = {
          ...task,
          ...apiData,
          taskCode: originalTaskCode
        };
        this.startTask(newTaskInfo);
      } else {
        console.warn('getTaskInfo: API 返回数据为空，使用原始任务信息', ret);
        this.startTask(task);
      }
    });
  }

  async startTask(task: Task): Promise<void> {
    let geomData = JSON.parse(task.geom);
    geomData = this.cleanGeomCoordinates(geomData);
    const centerPoint = centroid(geomData);

    // 发送暂停定时器消息
    this.dataEvent.sendData('pauseTimer', true);

    const modal = await this.modalCtrl.create({
      component: ExecutComponent,
      componentProps: {
        isModal: true, mapClick: true, task,
        centerPoint: centerPoint.geometry.coordinates,
        executState: task.status === '执行中' ? 'continue' : 'create'
      },
      backdropDismiss: false
    });

    // 监听模态框关闭事件
    modal.onDidDismiss().then(() => {
      // 模态框关闭后发送恢复定时器消息
      this.dataEvent.sendData('resumeTimer', true);
    });

    await modal.present();
  }

  private cleanGeomCoordinates(geomData: any): any {
    if (geomData.coordinates && Array.isArray(geomData.coordinates)) {
      geomData.coordinates = geomData.coordinates.filter((coord: number[]) => {
        return coord[0] !== 0 && coord[1] !== 0;
      });
    }
    return geomData;
  }

  async checkData(): Promise<void> {
    try {
      this.onUploadLength = (await this.backgroundGeolocation.getValidLocations() as BackgroundGeolocationResponse[]).length;
      this.locationConfig = await this.backgroundGeolocation.getConfig() as BackgroundGeolocationConfig;
      if (this.onUploadLength > 0) {
        this.backgroundGeolocation.forceSync();
      }
    } catch (error) {
      console.error('检测未上传数据失败:', error);
      this.onUploadLength = 0;
    }
  }
  dateTransform(date: any, format: string): any {
    return this.datePipe.transform(date ? new Date(date) : new Date(), format);
  }

  async onToast(mgs = '出现错误请重试！', color = 'danger'): Promise<void> {
    const toast = await this.toastCtrl.create({
      message: mgs,
      duration: 2000,
      color
    });
    toast.present();
  }

  trackByTaskCode(index: number, task: Task): string {
    return task.taskCode;
  }

  isActiveTask(status: string): boolean {
    return ['未执行', '执行中'].includes(status);
  }

  getPatrolStats(): string {
    const completed = this.patrolTasks.filter(task => task.status === '已完成').length;
    const total = this.patrolTasks.length;
    return `${completed}/${total}`;
  }

  getInspectionStats(): string {
    const completed = this.inspectionTasks.filter(task => task.status === '已完成').length;
    const total = this.inspectionTasks.length;
    return `${completed}/${total}`;
  }

  onTaskCardClick(task: Task): void {
    console.log('Task card clicked:', task);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
