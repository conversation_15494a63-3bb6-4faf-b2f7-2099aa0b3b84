<!-- 使用手册 -->
<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <!-- 使用手册 -->
    <ion-title>使用手册</ion-title>
    <ion-buttons slot="end">
      <ion-button>
        <ion-icon></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<!-- 内容 -->
<ion-content class="body" style="height: 100%;">
  <div style="margin-top: 70px;">
    <section id="viewer">
        <simple-pdf-viewer #pdfViewer [removePageBorders]="true"
          (onLoadComplete)="onLoadComplete()" [src]="src"
        >
        </simple-pdf-viewer>
    </section>
  </div>

  <ion-grid style="margin-top: 10px;">
    <ion-row>
      <ion-col (click)="pdfViewer.prevPage()" class="icon">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-col>
      <ion-col size="2">
        {{ pdfViewer.getCurrentPage() }}/{{ pdfViewer.getNumberOfPages() }}
      </ion-col>
      <ion-col (click)="pdfViewer.nextPage()" class="icon">
        <ion-icon name="chevron-forward-outline"></ion-icon>
      </ion-col>
      <ion-col (click)="pdfViewer.zoomIn()" class="icon">
        <ion-icon name="add-outline"></ion-icon>
      </ion-col>
      <ion-col>
        {{ pdfViewer.getZoomPercent() }}%
      </ion-col>
      <ion-col (click)="pdfViewer.zoomOut()" class="icon">
        <ion-icon name="remove-outline"></ion-icon>
      </ion-col>
    </ion-row>
  </ion-grid>

</ion-content>
