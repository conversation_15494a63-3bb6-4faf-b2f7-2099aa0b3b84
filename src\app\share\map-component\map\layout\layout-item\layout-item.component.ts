import { Component, Input, OnInit } from '@angular/core';
export type ILayoutItemAlign = 'top-left' | 'top-center' | 'top-right' | 'right-center' | 'right-bottom' | 'bottom-center' | 'bottom-left' | 'left-center' | 'center';
@Component({
  selector: 'ost-layout-item',
  templateUrl: './layout-item.component.html',
  styleUrls: ['./layout-item.component.scss']
})
export class LayoutItemComponent implements OnInit {
  @Input() align: ILayoutItemAlign = 'top-left';
  constructor() { }
  ngOnInit(): void {
  }

}
