import { ChangeDetectorRef, Component, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, ModalController } from '@ionic/angular';
import { Feature } from 'ol';
import { Coordinate } from 'ol/coordinate';
import GeoJSON from 'ol/format/GeoJSON';
import Point from 'ol/geom/Point';
import { MapComponent } from 'src/app/share/map-component/map/map.component';
import { Task } from 'src/app/work/class/work';
import { TrackPlayHelpsService } from './track-play-helps.service';
import { TrackUserData } from '../../class/playback.interface';
import { WorkService } from '../../work.service';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';

@Component({
  selector: 'app-track-control-bar',
  templateUrl: './track-control-bar.page.html',
  styleUrls: ['./track-control-bar.page.scss'],
})
export class TrackControlBarPage implements OnInit, OnD<PERSON>roy {
  @Input() ostMap: MapComponent;
  @Input() modelInfo: Task;
  // 速度
  speed = 50;
  // 进度
  progress = 0;
  // 播放状态
  playState = false;
  // 显示状态
  showState = true;
  // 轨迹数据
  trackData: { time: string, coordinates: Coordinate }[] = [];
  trackDate = '轨迹时间';
  // 用户轨迹数据
  trackUsers: TrackUserData[] = [];
  // 当前选中的用户索引
  selectedUserIndex = 0;
  // 是否显示用户选择列表
  showUserList = false;

  constructor(
    private netSer: WorkService, private trackPlay: TrackPlayHelpsService,
    private modalCtrl: ModalController, private alertCtrl: AlertController,
    private cd: ChangeDetectorRef, private userSer: UserInfoService
  ) { }

  ngOnInit(): void {
    this.trackPlay.init(this.ostMap);
    this.getTrailPointsByTask();
    this.trackPlay.onPlayProgress().subscribe(ret => this.onPlayProgressChange(ret));
  }
  /**
   * 查询轨迹点
   */
  getTrailPointsByTask(): void {
    this.netSer.getTrailPointsByTask({ taskCode: this.modelInfo.taskCode }).subscribe((ret) => {
      if (!ret.data || ret.data.length === 0) {
        // 弹窗提示
        this.noDataAlert();
      } else {
        this.trackUsers = ret.data;
        if (this.trackUsers.length > 0) {
          // 查找当前登录用户的索引
          const currentUserIndex = this.trackUsers.findIndex(user => user.userName === this.userSer.userName);
          // 如果找到当前用户，则选中当前用户，否则选中第一个用户
          this.selectedUserIndex = currentUserIndex >= 0 ? currentUserIndex : 0;
          this.setTrackByUserIndex(this.selectedUserIndex);
        }
      }
    });
  }
  /**
   * 根据用户索引设置轨迹
   */
  setTrackByUserIndex(index: number): void {
    this.selectedUserIndex = index;
    const userData = this.trackUsers[index];
    if (userData && userData.geom && userData.geom.features && userData.geom.features.length > 0) {
      this.setTrackByGeoJSONPoints(userData.geom);
    } else {
      this.showState = false;
    }
  }
  /**
   * 无数据弹窗提示
   */
  async noDataAlert(): Promise<void> {
    const alert = await this.alertCtrl.create({
      header: '系统提示',
      message: '当前无历史轨迹数据',
      backdropDismiss: false,
      buttons: [{
        text: '确认',
        handler: () => {
          this.modalCtrl.dismiss();
        }
      }]
    });
    await alert.present();

  }
  /**
   * 进度变化
   */
  onPlayProgressChange = (progress: number) => {
    this.progress = Math.round(progress * 100);
    const pr = (Math.round(progress * this.trackData.length));
    if (this.trackData[pr]) {
      this.trackDate = this.trackData[pr].time;
    }
    if (this.progress === 100) {
      this.playState = false;
    }
    this.cd.markForCheck();
  }
  /**
   * 暂停播放切换
   */
  onPlayStateChange(): void {
    this.playState = !this.playState;
    this.playState
      ? this.trackPlay.play()
      : this.trackPlay.pause();
  }
  /**
   * 停止
   */
  onStop(): void {
    this.playState = false;
    this.showState = false;
    this.trackPlay.stop();
    this.modalCtrl.dismiss();

  }
  /**
   * 设置带时间的轨迹点
   */
  setTrackByGeoJSONPoints(json: any): void {
    this.trackPlay.reset();
    this.progress = 0;
    this.trackDate = '轨迹时间';
    this.playState = false;
    if (json && json.features && json.features.length > 0) {
      const points = new GeoJSON().readFeatures(json) as Feature<Point>[];
      const lineCoordinate = points.map(point => point.getGeometry()?.getCoordinates()) as Coordinate[];
      this.trackData = points.map(point => ({
        time: point.getProperties().createTime as string,
        coordinates: point.getGeometry()?.getCoordinates() || [0, 0]
      }));
      this.trackPlay.setTrackByCoordinates(lineCoordinate);
      this.showState = true;
    }
    else {
      this.showState = false;
    }
  }
  /**
   * 速度变化
   */
  onSpeedChange(): void {
    this.trackPlay.setSpeed(this.speed);
  }
  /**
   * 进度变化
   */
  onProgressChange(progress: any): void {
    this.trackPlay.setProgress(progress);
    if (this.progress === 100) {
      this.playState = false;
    }
  }
  /**
   * 切换用户列表显示状态
   */
  toggleUserList(): void {
    this.showUserList = !this.showUserList;
  }
  /**
   * 选择用户轨迹
   */
  selectUserTrack(index: number): void {
    if (this.selectedUserIndex !== index) {
      this.playState = false;
      this.trackPlay.stop();
      this.setTrackByUserIndex(index);
    }
    this.showUserList = false;
  }

  /**
   * 监听全局点击事件，点击外部区域关闭用户列表
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.showUserList) {
      this.showUserList = false;
    }
  }
  ngOnDestroy(): void {
    this.showState = false;
    this.trackPlay.destroy();
  }
}
