import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { OstUtilsModule } from 'ost-utils';
import { FormErrorComponent } from './form/form-error/form-error.component';
import { FormItemComponent } from './form/form-item/form-item.component';
import { FormListComponent } from './form/form-list/form-list.component';
import { ImagesComponent } from './images/images.component';
import { LocationSelectComponent } from './map-component/location-select/location-select.component';
import { OnPressDirective } from './on-press/on-press.directive';
import { OstTreeComponent, OstTreeListComponent } from './ost-tree-list/ost-tree-list.component';
import { OstTreeLisstInternalService } from './ost-tree-list/ost-tree-list.service';
import { PreviewComponent } from './preview/preview.component';
import { MapControlersGroupComponent } from './map-component/map/controler-group/controler-group.component';
import { MapControlerItemComponent } from './map-component/map/controler-group/controler-item/controler-item.component';
import { LayoutItemComponent } from './map-component/map/layout/layout-item/layout-item.component';
import { MapLayoutComponent } from './map-component/map/layout/ost-map-layout.component';
import { MapComponent } from './map-component/map/map.component';
import { MapSwitchComponent } from './map-component/map/tools-bar/ost-map-switch/ost-map-switch.component';
import { ToolsBarComponent } from './map-component/map/tools-bar/tools-bar.component';
import { OrgMenuComponent } from './menu/org-menu/org-menu.component';
import { InputSearchSourceComponent } from './input-search/input-search-source.component';
import { InputSearchComponent } from './input-search/input-search.component';
import { OptionSourceComponent } from './input-search/source/option-source/option-source.component';
import { SearchSourceOrgComponent } from './input-search/source/search-source-org/search-source-org.component';
import { SearchSourcePipeComponent } from './input-search/source/search-source-pipe/search-source-pipe.component';
import { SearchSourceStakeComponent } from './input-search/source/search-source-stake/search-source-stake.component';
import { PipeMenuComponent } from './menu/pipe-menu/pipe-menu.component';
import { StakeMenuComponent } from './menu/stake-menu/stake-menu.component';
import { OstFormService } from './form/ost-form.service';
import { QuickOptionSelectComponent } from './quick-option-select/quick-option-select.component';
import { TreeMenuComponent } from './menu/tree-menu/tree-menu.component';
import { SearchSourceTreeComponent } from './input-search/source/search-source-tree/search-source-tree.component';
import { CameraLayerComponent } from './map-component/camera-layer/camera-layer.component';
import { MapService } from './map-component/service';
import { KeyPointsComponent } from './map-component/key-points/key-points.component';
import { ImageGestureService } from './preview/services/image-gesture.service';
import { LocationProviderModalComponent } from './map-component/map/tools-bar/location-provider-modal/location-provider-modal.component';

// 部门树
const TREELIST = [OstTreeListComponent, OstTreeComponent];
const FORM = [FormListComponent, FormItemComponent, FormErrorComponent];
const SEARCH = [
  InputSearchComponent, InputSearchSourceComponent,
  SearchSourceOrgComponent, OptionSourceComponent,
  SearchSourcePipeComponent, SearchSourceStakeComponent,
  QuickOptionSelectComponent, SearchSourceTreeComponent
];
const MENU = [
  OrgMenuComponent, PipeMenuComponent,
  StakeMenuComponent, TreeMenuComponent
];
const DIALOG = [PreviewComponent];
// 图片组件
const IMAGES = [ImagesComponent];
const MAP = [
  MapComponent,
  LayoutItemComponent,
  MapLayoutComponent,
  MapControlersGroupComponent,
  MapControlerItemComponent,
  ToolsBarComponent,
  MapSwitchComponent,
  CameraLayerComponent,
  KeyPointsComponent,
  LocationProviderModalComponent
];
const MAPCOMP = [LocationSelectComponent];
@NgModule({
  imports: [
    CommonModule,
    OstUtilsModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule
  ],
  providers: [OstTreeLisstInternalService, OstFormService, MapService, ImageGestureService],
  declarations: [...TREELIST, ...SEARCH, ...MENU, ...FORM, ...MAP, ...DIALOG, ...MAPCOMP, ...IMAGES, OnPressDirective],
  exports: [...TREELIST, ...SEARCH, ...MENU, ...FORM, ...MAP, ...DIALOG, ...MAPCOMP, ...IMAGES, OnPressDirective],
  entryComponents: [...MAP, ...DIALOG, ...MAPCOMP, ...IMAGES]
})
export class ShareModule { }
