import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FilterOptions } from '../../keypoint-view.page';

@Component({
  selector: 'app-map-filter-panel',
  templateUrl: './map-filter-panel.component.html',
  styleUrls: ['./map-filter-panel.component.scss']
})
export class MapFilterPanelComponent implements OnInit {
  @Input() filter: FilterOptions;
  @Output() filterChange = new EventEmitter<Partial<FilterOptions>>();
  @Output() layerExtentRequest = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {}

  /**
   * 巡检类型筛选
   */
  onInspectionTypeChange(type: 'all' | 'vehicle' | 'person') {
    this.filterChange.emit({ inspectionType: type });
    
    let layerId = '';
    switch (type) {
      case 'vehicle':
        layerId = 'inspect_vehicle';
        break;
      case 'person':
        layerId = 'inspect_person';
        break;
      case 'all':
      default:
        layerId = 'inspect_point';
        break;
    }
    
    this.layerExtentRequest.emit(layerId);
  }

  /**
   * 检查按钮是否激活
   */
  isInspectionTypeActive(type: 'all' | 'vehicle' | 'person'): boolean {
    return this.filter?.inspectionType === type;
  }
}