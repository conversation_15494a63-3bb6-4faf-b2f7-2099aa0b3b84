<ion-card-content [ngClass]="platform.is('android')=== true? 'body':'body-ios' ">
  <!-- 引导语 -->   
  <span class="guide">欢迎，{{userSer.userName?userSer.userName:userSer.account}}</span>
  <div class="box">
    <!-- 巡检任务统计 -->
    <div class="row">
      <div class="row-item">
        <!-- 总任务 -->
        <span class="item-headerInfo">总任务</span>
        <p class="item-cont" style="color: #4d80cf;">{{TaskInfo.taskNum}}</p>
        <em></em>
      </div>
      <div class="row-item">
        <!-- 已巡检 -->
        <span class="item-headerInfo" style="color:#888888;">已巡检</span>
        <p class="item-cont" style="color: #5fb8aa;">{{TaskInfo.completedNum}}</p>
        <em></em>
      </div>
      <div class="row-item">
        <!-- 未巡检 -->
        <span class="item-headerInfo" style="color:#888888;">未巡检</span>
        <p class="item-cont" style="color: #ffa200;">{{TaskInfo.unExecutedNum}}</p>
      </div>
    </div>
  </div>

</ion-card-content>