# 样式文件结构说明

## 目录结构

```
src/theme/
├── variables.scss    # 全局变量定义
├── dialog.scss       # 对话框和通用组件样式
├── modal.scss        # 模态框样式管理
└── README.md         # 本说明文档
```

## 文件说明

### variables.scss
- **用途**: 定义全局 SCSS 变量
- **内容**: 颜色、尺寸、字体等变量定义
- **引用**: 被其他样式文件引用

### dialog.scss
- **用途**: 对话框和通用组件样式
- **内容**: 
  - 对话框尺寸类（.dialog-box-xs, .dialog-box-sm 等）
  - 列表项样式
  - 通用组件样式
  - 布局辅助类

### modal.scss
- **用途**: 专门管理所有模态框相关样式
- **内容**:
  - 事件穿透模态框样式
  - 摄像头层模态框样式
  - 各种自定义模态框样式类
  - 模态框动画效果

## 样式类说明

### 模态框样式类

| 类名 | 用途 | 特点 |
|------|------|------|
| `.alarm-list-modal` | 报警列表模态框 | 居中显示，最大高度70vh |
| `.camera-list-modal` | 摄像头列表模态框 | 居中显示，最大高度60vh |
| `.app-evreport` | 事件上报模态框 | 居中显示，最大高度80vh |
| `.ost-preview` | 图片预览模态框 | 居中显示，自适应尺寸 |
| `.my-custom-class` | 自定义模态框 | 居中显示，最大高度80vh |

### 特殊模态框

| 类名 | 用途 | 特点 |
|------|------|------|
| `ion-modal.allow-events-through` | 事件穿透模态框 | 允许背景点击穿透 |
| `ion-modal.app-camera-layer` | 摄像头层模态框 | 小窗口模式，右上角显示 |
| `ion-modal.app-camera-layer.fullscreen-mode` | 全屏摄像头模态框 | 全屏显示模式 |

## 使用方式

### 在组件中使用模态框样式

```typescript
// 在组件中创建模态框时指定样式类
const modal = await this.modalCtrl.create({
  component: YourComponent,
  cssClass: 'alarm-list-modal', // 使用预定义的样式类
  componentProps: { /* ... */ }
});
```

### 添加新的模态框样式

1. 在 `modal.scss` 文件中添加新的样式类
2. 遵循现有的命名规范
3. 添加详细的注释说明
4. 更新本说明文档

## 最佳实践

1. **样式分离**: 模态框样式统一在 `modal.scss` 中管理
2. **命名规范**: 使用语义化的类名，如 `{功能}-{类型}-modal`
3. **响应式设计**: 使用 vh/vw 单位确保在不同设备上的适配
4. **性能优化**: 避免过度使用 `!important`，优先使用 CSS 选择器优先级
5. **维护性**: 添加清晰的注释，便于后续维护

## 注意事项

- 模态框样式必须在全局样式中定义，因为模态框被渲染到 DOM 根级别
- 避免在组件样式中定义模态框样式，因为会受到样式封装的影响
- 新增样式时注意与现有样式的兼容性 