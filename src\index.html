<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <title>巡检APP</title>

  <base href="/" />

  <meta name="color-scheme" content="light dark" />
  <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="msapplication-tap-highlight" content="no" />

  <link rel="icon" type="image/png" href="assets/icon/favicon.png" />

  <!-- add to homescreen for ios -->
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />
</head>

<style>
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #49a9ee;
  z-index: 9999;
  transition: opacity .65s;
}
.preloader-hidden {
  display: none
}
.cs-loader {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%
}
.cs-loader-inner {
  transform: translateY(-50%);
  top: 50%;
  position: absolute;
  width: 100%;
  color: #fff;
  text-align: center
}
.cs-loader-inner label {
  font-size: 20px;
  opacity: 0;
  display: inline-block
}
@keyframes lol {
  0% {
    opacity: 0;
    transform: translateX(-300px)
  }
  33% {
    opacity: 1;
    transform: translateX(0)
  }
  66% {
    opacity: 1;
    transform: translateX(0)
  }
  100% {
    opacity: 0;
    transform: translateX(300px)
  }
}
.cs-loader-inner label:nth-child(6) {
  animation: lol 2.5s infinite ease-in-out
}
.cs-loader-inner label:nth-child(5) {
  animation: lol 2.5s .1s infinite ease-in-out
}
.cs-loader-inner label:nth-child(4) {
  animation: lol 2.5s .2s infinite ease-in-out
}
.cs-loader-inner label:nth-child(3) {
  animation: lol 2.5s .3s infinite ease-in-out
}
.cs-loader-inner label:nth-child(2) {
  animation: lol 2.5s .4s infinite ease-in-out
}
.cs-loader-inner label:nth-child(1) {
  animation: lol 2.5s .5s infinite ease-in-out
}

</style>

<body>
  <div class="preloader">
    <div class="cs-loader">
      <div class="cs-loader-inner">
        <label> L </label>
        <label> O </label>
        <label> A </label>
        <label> D </label>
        <label> I </label>
        <label> N </label>
        <label> G </label>
      </div>
    </div>
  </div>
  <app-root></app-root>
</body>

</html>
