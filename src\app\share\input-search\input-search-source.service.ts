import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

@Injectable()
export class InputSearchSourceService {
  private valueChange$ = new Subject<{ name: string, value: any }>();

  onValueChange(): Observable<{ name: string, value: any }> {
    return this.valueChange$;
  }
  change(name: string, value: any): void {
    this.valueChange$.next({ name, value });
  }

}
