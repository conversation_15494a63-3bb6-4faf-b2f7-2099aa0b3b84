import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { Subject } from 'rxjs';
import { DetailsMode } from 'src/app/@core/base/environment';
import { DataSyncManagerService, SyncDataType } from 'src/app/@core/providers/data-sync';
import { ShareMethodService } from 'src/app/@core/providers/share-method.service';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';

/**
 * 打卡组件
 */
@Component({
  selector: 'app-clock-in',
  templateUrl: './clock-in.component.html',
  styleUrls: ['./clock-in.component.scss']
})
export class ClockInComponent implements OnInit, OnDestroy {
  // 坐标
  @Input() coordinate: number[];
  // 当前关键点
  @Input() currentKeyPoint: any;
  // 任务Code
  @Input() taskCode: string;
  // 数据模式（新增或查看）
  @Input() modelMode: DetailsMode = DetailsMode.ADD;
  // 是否为详情模式
  @Input() isDetailMode: boolean = false;
  // 提交成功事件
  @Output() submitSuccess = new EventEmitter<{ shouldRefreshKeyPoints: boolean }>();
  // 图片上传状态
  isImageUploading = false;
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  base64Images: string[] = [];
  // 查看模式下的文件编码列表
  fileCodes: string[] = [];

  constructor(
    private toastService: ToastService,
    private shareMethod: ShareMethodService,
    private UserInfoSer: UserInfoService,
    private dataSyncManager: DataSyncManagerService
  ) { }

  ngOnInit() {
    // 如果是查看模式且有打卡数据，回显数据
    if (this.modelMode === DetailsMode.SEE && this.currentKeyPoint?.picCode) {
      this.loadExistingData();
    }
  }

  /**
   * 加载已有的打卡数据
   */
  private loadExistingData(): void {
    if (this.currentKeyPoint?.picCode) {
      // 在查看模式下，使用fileCodes来显示已有图片，不影响base64Images
      if (Array.isArray(this.currentKeyPoint.picCode)) {
        this.fileCodes = [...this.currentKeyPoint.picCode];
      }
      // 如果picCode是字符串，转换为数组
      else if (typeof this.currentKeyPoint.picCode === 'string') {
        this.fileCodes = [this.currentKeyPoint.picCode];
      }
    }
  }

  get formattedPoint(): string {
    return this.shareMethod.formatPoint(this.currentKeyPoint.point);
  }

  /**
   * 是否为查看模式
   */
  get isViewMode(): boolean {
    return this.modelMode === DetailsMode.SEE;
  }

  onBase64ImagesChange(images: string[]) {
    this.base64Images = images;
  }

  /**
   * 提交按钮事件
   */
  async onSubmit() {
    if (!this.base64Images || this.base64Images.length === 0) {
      this.toastService.presentToast('请拍照并上传关键点打卡照片', 'warning');
      return;
    }

    // 优先使用传入的实时坐标，如果没有则使用关键点的预设坐标
    let longitude: number, latitude: number;

    if (this.coordinate && this.coordinate.length >= 2) {
      // 使用ExecutComponent传入的实时坐标
      longitude = this.coordinate[0];
      latitude = this.coordinate[1];
      console.log('使用实时坐标进行打卡:', { longitude, latitude });
    } else {
      // 回退到关键点的预设坐标
      const pointAsString: string = this.currentKeyPoint.point;
      const coordinateArray: [number, number] = JSON.parse(pointAsString);
      longitude = coordinateArray[0];
      latitude = coordinateArray[1];
      console.log('使用关键点预设坐标进行打卡:', { longitude, latitude });
    }

    const cacheItem = {
      taskCode: this.taskCode,
      userCode: this.UserInfoSer.userId,
      userName: this.UserInfoSer.userName,
      depCode: this.UserInfoSer.depCode,
      longitude: longitude,
      latitude: latitude,
      pointCode: this.currentKeyPoint.pointCode,
      trajectoryTime: Date.now(),
      picCode: this.base64Images
    };

    // 调用服务进行缓存，并根据返回值动态提示
    const result = await this.dataSyncManager.addToCache(
      SyncDataType.MANUAL_CLOCK_IN,
      cacheItem,
      `${ResourceGlobalConfig.url}/work-inspect/api/v2/inspect/app/trajectory/clock`
    );

    if (result.status === 'uploaded' && result.code === 0) {
      this.toastService.presentToast('打卡成功', 'success');
      // 网络通畅且提交成功时，发送刷新关键点数据的信号
      this.submitSuccess.emit({ shouldRefreshKeyPoints: true });
    } else if (result.status === 'cached') {
      this.toastService.presentToast('数据已缓存，网络恢复后自动上传', 'success');
      // 数据缓存时不刷新关键点数据
      this.submitSuccess.emit({ shouldRefreshKeyPoints: false });
    }
  }

  /**
   * 判断提交按钮是否应该禁用
   */
  get isSubmitDisabled(): boolean {
    if (this.isImageUploading) {
      return true; // 图片上传中禁用
    }

    // 关键点打卡：需要上传照片
    return !this.base64Images || this.base64Images.length === 0;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
