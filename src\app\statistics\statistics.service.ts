import { Injectable } from "@angular/core";
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams } from "@ngx-resource/core";
import { EventParams } from "../execut/class/evreport";

/**
 * 巡检统计服务
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root'
})
export class StatisticsService extends Resource {

  constructor() {
    super();
  }

  /**
   * 获取统计数据
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/statistics/work',
  })
  getStatisticsData!: IResourceMethodObservable<any, any>;

  /**
   * 事件列表
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/event/msg/grid',
  })
  getEventList!: IResourceMethodObservable<EventParams, any>;

}