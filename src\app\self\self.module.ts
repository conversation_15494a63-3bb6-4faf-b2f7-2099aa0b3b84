import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SimplePdfViewerModule } from 'simple-pdf-viewer';
import { ShareModule } from '../share/share.module';
import { GisConfigPage } from './gis-config/gis-config.page';
import { PdfViewersComponent } from './pdf-viewer/pdf-viewer.component';
import { SelfPageRoutingModule } from './self-routing.module';
import { SelfPage } from './self.page';
import { ChangPasswordComponent } from './chang-password/chang-password.component';

const PAGE = [
  SelfPage,
  ChangPasswordComponent, // 修改密码
  GisConfigPage, // GIS配置
  PdfViewersComponent // 使用手册
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    CommonModule,
    ShareModule,
    SimplePdfViewerModule,
    SelfPageRoutingModule
  ],
  declarations: [...PAGE]
})
export class SelfPageModule { }
