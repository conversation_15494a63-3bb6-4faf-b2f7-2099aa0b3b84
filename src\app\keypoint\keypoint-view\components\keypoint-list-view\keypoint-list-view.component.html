<div class="list-view-container">
  <div class="filter-toolbar">

    <ion-searchbar 
      placeholder="搜索关键点名称"
      (ionChange)="onSearchChange($event)"
      debounce="300"
      class="search-bar">
    </ion-searchbar>
    
    <div class="filter-buttons-container">
      <div class="filter-group">
        <ion-button 
          size="small" 
          [fill]="isInspectionTypeActive('all') ? 'solid' : 'outline'"
          [color]="isInspectionTypeActive('all') ? 'primary' : 'medium'"
          (click)="onInspectionTypeChange('all')">
          <ion-icon name="apps-outline" slot="start"></ion-icon>
          全部
        </ion-button>
        
        <ion-button 
            size="small" 
            [fill]="isInspectionTypeActive('vehicle') ? 'solid' : 'outline'"
            [color]="isInspectionTypeActive('vehicle') ? 'success' : 'medium'"
            (click)="onInspectionTypeChange('vehicle')">
            <ion-icon name="car-outline" slot="start"></ion-icon>
            巡视
          </ion-button>
          
          <ion-button 
            size="small" 
            [fill]="isInspectionTypeActive('person') ? 'solid' : 'outline'"
            [color]="isInspectionTypeActive('person') ? 'warning' : 'medium'"
            (click)="onInspectionTypeChange('person')">
            <ion-icon name="person-outline" slot="start"></ion-icon>
            巡查
          </ion-button>
      </div>
      

    </div>
  </div>

  <div class="action-toolbar" *ngIf="!isSelectionMode">
    <ion-button fill="outline" size="small" (click)="toggleSelectionMode()">
      <ion-icon name="checkbox-outline" slot="start"></ion-icon>
      批量操作
    </ion-button>
  </div>

  <div class="selection-toolbar" *ngIf="isSelectionMode">
    <div class="selection-info">
      <ion-icon name="checkmark-circle-outline"></ion-icon>
      已选择 {{ selectedItems.size }} 项
    </div>
    <div class="selection-actions">
      <ion-button fill="clear" color="danger" (click)="deleteSelected()" [disabled]="selectedItems.size === 0">
        <ion-icon name="trash-outline" slot="start"></ion-icon>
        删除
      </ion-button>
      <ion-button fill="clear" (click)="toggleSelectionMode()">
        <ion-icon name="close-outline" slot="start"></ion-icon>
        取消
      </ion-button>
    </div>
  </div>

  <ion-content class="keypoint-content">
    <div class="keypoint-list" *ngIf="!loading || keyPoints.length > 0; else loadingTemplate">
      <ion-list>
        <div
          *ngFor="let keyPoint of filteredKeyPoints; let i = index"
          class="inspection-card"
          [class.inspected]="keyPoint.status === 'inspected'"
          [class.selected]="selectedItems.has(keyPoint.pointCode)"
          (click)="onKeyPointClick(keyPoint)">

          <ion-checkbox
            *ngIf="isSelectionMode"
            class="selection-checkbox"
            [checked]="selectedItems.has(keyPoint.pointCode)"
            (ionChange)="onItemSelect(keyPoint.pointCode, $event)">
          </ion-checkbox>

          <div class="card-header" *ngIf="!isSelectionMode">
            <span class="badge">{{ i + 1 }}</span>
            <h2>{{ keyPoint.pointName }}</h2>
          </div>
          <div class="card-body">
            <div class="field">
              <label>巡检类型：</label>
              <span class="tag" [class.vehicle-tag]="keyPoint.type === 'vehicle'" [class.person-tag]="keyPoint.type === 'person'">{{ getTypeText(keyPoint) }}</span>
            </div>
            <div class="field">
              <label>巡检天气：</label>
              <span>{{ keyPoint.isItRaining === '是' ? '☀️' : '☀️🌧️' }}</span>
            </div>
            <div class="field">
              <label>缓冲范围：</label>
              <span>{{ keyPoint.bufferRange }}米</span>
            </div>
          </div>

          <div class="card-footer" *ngIf="!isSelectionMode">
            <div class="action-item delete-action" (click)="deleteKeyPoint(keyPoint); $event.stopPropagation();">
              <ion-icon name="trash-outline"></ion-icon>
              <span>删除</span>
            </div>
            <div class="action-item edit-action" (click)="editKeyPoint(keyPoint); $event.stopPropagation();">
              <ion-icon name="create-outline"></ion-icon>
              <span>编辑</span>
            </div>
          </div>
        </div>
      </ion-list>
      
      <div class="empty-state" *ngIf="filteredKeyPoints.length === 0 && !loading">
        <ion-icon name="location-outline" class="empty-icon"></ion-icon>
        <div class="empty-text">暂无关键点数据</div>
        <div class="empty-subtext">请检查筛选条件或稍后重试</div>
      </div>
    </div>
    
      <ion-infinite-scroll 
        threshold="100px" 
        class="infinite-scroll"
        *ngIf="keyPoints.length > 0"
        (ionInfinite)="loadMore($event)"
        [disabled]="!hasMoreData || isLoadingMore"
        >
       <ion-infinite-scroll-content
         style="margin-top: 16px;"
         [loadingSpinner]="spinnerType"
         [loadingText]="currentLoadingText">
       </ion-infinite-scroll-content>
     </ion-infinite-scroll>
  </ion-content>

  <ng-template #loadingTemplate>
    <div class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <div class="loading-text">加载中...</div>
    </div>
  </ng-template>
</div>