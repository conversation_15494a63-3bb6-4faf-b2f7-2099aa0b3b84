import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { RequestResult } from 'src/app/@core/base/request-result';
import { ShareModuleService } from '../../../share.service';

@Injectable({
  providedIn: 'root'
})
export class CameraInfoService {
  private pollingTimer: any;
  private destroy$ = new Subject<void>();
  private lastPresetBitId: string | null = null;

  constructor(
    private netSer: ShareModuleService
  ) { }

  /**
   * 获取摄像头信息
   * @param id 摄像头ID
   * @returns Promise<RequestResult<any>>
   */
  getCameraInfo(id: string): Promise<RequestResult<any>> {
    if (!id) {
      return Promise.reject('摄像头ID不能为空');
    }

    return new Promise((resolve, reject) => {
      this.netSer.getRequest({ interfaceUrl: '/work-inspect/api/v2/inspect/camera/byId', id })
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (result: RequestResult<any>) => {
            const { code, data, msg } = result;
            if (code === 0 && data) {
              resolve(result);
            } else {
              reject(msg || '获取摄像头信息失败');
            }
          },
          error: (error) => {
            console.error('获取摄像头信息失败:', error);
            reject(error);
          }
        });
    });
  }

  /**
   * 开始轮询获取摄像头信息
   * @param id 摄像头ID
   * @param callback 回调函数，用于处理获取到的数据
   * @param errorCallback 错误回调函数
   * @param interval 轮询间隔，默认10秒
   */
  startPolling(
    id: string, 
    callback: (data: any) => void, 
    errorCallback: (error: any) => void,
    interval: number = 10000
  ): void {
    // 如果已经在轮询，先清除之前的定时器
    this.stopPolling();
    
    this.pollingTimer = setInterval(async () => {
      try {
        const result = await this.getCameraInfo(id);
        if (result.code === 0 && result.data) {
          callback(result.data);
        } else {
          // 如果返回码不为0，也视为错误
          errorCallback(result.msg || '获取摄像头信息失败');
        }
      } catch (error) {
        console.error('轮询获取摄像头信息失败:', error);
        errorCallback(error);
        // 发生错误时停止轮询，避免持续错误
        this.stopPolling();
      }
    }, interval);
  }

  /**
   * 停止轮询
   */
  stopPolling(): void {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;
    }
  }

  /**
   * 检查预置位是否发生变化
   * @param presetBitId 预置位ID
   * @returns boolean 是否发生变化
   */
  checkPresetBitChange(presetBitId: string): boolean {
    if (presetBitId !== this.lastPresetBitId) {
      this.lastPresetBitId = presetBitId;
      return true;
    }
    return false;
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopPolling();
    this.destroy$.next();
    this.destroy$.complete();
  }
} 