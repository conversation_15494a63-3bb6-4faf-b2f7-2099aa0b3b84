import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DetailsMode } from 'src/app/@core/base/environment';
import { RequestResult } from 'src/app/@core/base/request-result';
import { ImagePickerService } from 'src/app/@core/providers/image-picker.service';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { PreviewComponent } from '../preview/preview.component';
import { ShareModuleService } from '../share.service';
import { compressImage, getBase64WithPrefix } from './image-utils';
import { IMAGE_CONFIG, ImageFile } from './images.config';

@Component({
  selector: 'ost-images',
  templateUrl: './images.component.html',
  styleUrls: ['./images.component.scss']
})
export class ImagesComponent implements OnInit, OnD<PERSON>roy {
  @Input() label: string = '现场照片';
  @Input() modelMode: DetailsMode;
  @Input() fileCodes: string[] = [];
  @Input() uploadMode: 'base64' | 'fileCode' = 'fileCode'; // 新增
  @Input() base64Images: string[] = []; // 新增
  @Output() fileCodesChange = new EventEmitter<string[]>();
  @Output() uploadStatusChange = new EventEmitter<boolean>(); // 新增：上传状态变化事件
  @Output() base64ImagesChange = new EventEmitter<string[]>(); // 上传失败图片base64变更事件
  @Input() maxCount?: number; // 最大照片数量，默认不限制
  @Input() cameraOnly: boolean = false; // 是否仅用相机，默认false

  base64Image: ImageFile[] = [];
  private localFileCodes: string[] = [];
  DetailsMode: typeof DetailsMode = DetailsMode;
  private destroy$ = new Subject<void>();

  constructor(
    private cd: ChangeDetectorRef, private imagePicker: ImagePickerService,
    private modalCtrl: ModalController, private toastService: ToastService,
    private shareNetSer: ShareModuleService,
  ) { }

  ngOnInit(): void {
    // 初始化本地fileCodes
    this.localFileCodes = [...this.fileCodes];
    this.getFiles(this.fileCodes);

    if (this.uploadMode === 'base64') {
      // base64模式：只展示 base64Images，所有图片状态为“待上传”
      this.base64Image = this.base64Images.map(base64 => ({ base64, status: 'pending' }));
      this.checkUploadStatus();
      return;
    }
    // fileCode模式：走原有 fileCodes 上传流程
    if (!this.fileCodes || this.fileCodes.length <= 0) {
      // 初始化时通知父组件没有上传中的图片
      this.checkUploadStatus();
      return;
    }
  }

  /**
   * 获取文件详情
   */
  private getFiles(fileCodes: string[]): void {
    if (!fileCodes?.length) return;

    fileCodes.forEach((fileCode) => {
      this.shareNetSer.GetFile([fileCode]).pipe(
        takeUntil(this.destroy$)
      ).subscribe({
        next: (result: RequestResult<any>) => {
          const { code, data, msg } = result;
          if (code === 0) {
            const { protocol, host, port } = IMAGE_CONFIG.server;
            const fileInfo: ImageFile = {
              url: `${protocol}://${host}:${port}${data[0].urlPath}`,
              status: 'success',
              fileCode
            };
            this.base64Image.push(fileInfo);
          } else {
            this.toastService.presentToast(msg, 'danger');
          }
        },
        error: () => {
          this.toastService.presentToast('获取图片失败', 'danger');
        }
      });
    });
  }

  /**
   * 同步文件编码到父组件
   */
  private syncFileCodes(): void {
    // 过滤出所有上传成功的图片的fileCode
    const successFileCodes = this.base64Image
      .filter(img => img.status === 'success' && img.fileCode)
      .map(img => img.fileCode);

    this.localFileCodes = successFileCodes;
    this.fileCodesChange.emit(this.localFileCodes);

    // 检查是否有图片正在上传并通知父组件
    this.checkUploadStatus();
  }

  /**
   * 检查上传状态并通知父组件
   */
  private checkUploadStatus(): void {
    const hasUploading = this.base64Image.some(img => img.status === 'uploading');
    this.uploadStatusChange.emit(hasUploading);
  }

  /**
   * 触发未上传成功图片的base64变更事件
   */
  private emitBase64Images(): void {
    if (this.uploadMode === 'base64') {
      const base64Arr = this.base64Image.map(img => img.base64);
      this.base64ImagesChange.emit(base64Arr);
    } else {
      const base64Arr = this.base64Image
        .filter(img => img.status !== 'success' && img.base64)
        .map(img => img.base64);
      this.base64ImagesChange.emit(base64Arr);
    }
  }

  /**
   * 选择图片入口，调用ImagePickerService
   */
  async presentActionSheet(): Promise<void> {
    // 数量限制判断
    if (this.maxCount && this.base64Image.length >= this.maxCount) {
      this.toastService.presentToast('已达到最大上传数量', 'warning');
      return;
    }
    const base64 = await this.imagePicker.selectImage({ cameraOnly: this.cameraOnly });
    if (base64) {
      this.combination(base64);
    }
  }

  /**
   * 根据上传模式处理后续逻辑
   */
  private handleUploadMode(base64: string): void {
    if (this.uploadMode === 'base64') {
      this.checkUploadStatus();
      this.emitBase64Images();
      this.cd.markForCheck();
    } else {
      this.checkUploadStatus();
      this.cd.markForCheck();
      this.emitBase64Images();
      this.uploadImg(base64);
    }
  }

  /**
   * base64Image数组移除图片
   */
  private removeImage(index: number): void {
    this.base64Image.splice(index, 1);
  }

  /**
   * base64Image数组更新图片状态
   */
  private updateImageStatus(index: number, status: 'pending' | 'uploading' | 'success' | 'failed', fileCode?: string): void {
    this.base64Image[index].status = status;
    if (fileCode) {
      this.base64Image[index].fileCode = fileCode;
    }
  }

  /**
   * 数据组合
   */
  private async combination(imageData: string): Promise<void> {
    if (!imageData) return;
    try {
      const base64 = getBase64WithPrefix(imageData);
      // 1. 立即回显原图，状态为 'optimizing'
      const tempIndex = this.base64Image.length;
      this.base64Image = [...this.base64Image, { base64, status: 'optimizing' }];
      this.cd.markForCheck();
      // 2. 异步压缩
      const compressedBase64 = await compressImage(base64);
      // 3. 替换为压缩图，状态恢复为 pending 或 uploading
      if (this.uploadMode === 'base64') {
        this.base64Image[tempIndex] = { base64: compressedBase64, status: 'pending' };
      } else {
        this.base64Image[tempIndex] = { base64: compressedBase64, status: 'uploading' };
      }
      this.cd.markForCheck();
      this.handleUploadMode(compressedBase64);
    } catch (error) {
      // 报错时移除最后一张“优化中”图片，避免残留
      this.base64Image.pop();
      this.cd.markForCheck();
      this.toastService.presentToast('图片处理失败', 'danger');
    }
  }

  /**
   * 上传图片
   */
  private uploadImg(base64: string): void {
    const index = this.base64Image.length - 1;
    this.startUpload(base64, index);
  }

  /**
   * 发起上传
   */
  private startUpload(base64: string, index: number): void {
    this.shareNetSer.addFile(base64).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (result: RequestResult<any>) => {
        const { code, data, msg } = result;
        if (code === 0 && data?.length > 0) {
          this.handleUploadSuccess(index, data[0]);
        } else {
          this.handleUploadFailure(index);
        }
      },
      error: () => {
        this.handleUploadFailure(index);
      }
    });
  }

  /**
   * 上传成功处理
   */
  private handleUploadSuccess(index: number, fileCode: string): void {
    this.updateImageStatus(index, 'success', fileCode);
    this.cd.markForCheck();
    this.syncFileCodes();
    this.emitBase64Images();
  }

  /**
   * 上传失败处理
   */
  private handleUploadFailure(index: number): void {
    this.updateImageStatus(index, 'failed');
    this.cd.markForCheck();
    this.emitBase64Images();
    this.onDelete(index, false);
  }

  /**
   * 删除图片
   */
  onDelete(index: number, emit: boolean = true): void {
    this.removeImage(index);
    this.cd.markForCheck();
    this.syncFileCodes();
    if (emit) {
      this.emitBase64Images();
    }
  }

  /**
   * 头像预览
   * @param file 文件信息
   */
  async previewAvatar(file: ImageFile): Promise<void> {

    const modal = await this.modalCtrl.create({
      component: PreviewComponent,
      componentProps: { file }
    });
    await modal.present();
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }
}

