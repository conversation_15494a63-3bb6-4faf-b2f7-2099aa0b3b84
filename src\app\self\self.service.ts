import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod } from '@ngx-resource/core';
import { RequestResult } from '../@core/base/request-result';

/**
 * 个人中心
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root',
})

export class SelfService extends Resource {

  constructor() { super(); }

  /**
   * 修改密码
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/update/userPasdByOldAndNewPasd',
    method: ResourceRequestMethod.Post,
  })
  ResetPassword!: IResourceMethodObservable<{ account: string, oldUserPasd: string, newUserPasd: string }, RequestResult<any>>;

  // 获取头像
  @ResourceAction({
    path: '/file/fileDes',
    method: ResourceRequestMethod.Post,
  })
  GetHeadSculpture!: IResourceMethodObservable<string[], RequestResult<any>>;
}
