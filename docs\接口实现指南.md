# @ngx-resource 使用指南

基于 `keypoint-view.service.ts` 的 @ngx-resource 库使用最佳实践。

## 快速开始

### 1. 安装依赖（有了就不用安装了）

```bash
npm install @ngx-resource/core
```

### 2. 基础服务结构

```typescript
import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams } from '@ngx-resource/core';

@ResourceParams({
  pathPrefix: '', // 全局路径前缀
})
@Injectable({ providedIn: 'root' })
export class KeypointViewService extends Resource {
  constructor() {
    super();
  }
}
```

## 核心装饰器

### @ResourceParams
全局配置，应用于整个服务类。

```typescript
@ResourceParams({
  pathPrefix: '/api/v1',     // 所有接口的公共前缀
  headers: {                // 公共请求头
    'Content-Type': 'application/json'
  }
})
```

### @ResourceAction
单个接口配置。

```typescript
@ResourceAction({
  path: '/users/{id}',       // 接口路径，支持路径参数
 method: ResourceRequestMethod.Get,            // HTTP方法，可选，默认GET
  headers: {},              // 接口专用请求头
  params: {}                // 默认查询参数
})
```

## 接口定义模式

### GET 请求（默认）

```typescript
// 简单GET请求
@ResourceAction({ path: '/points' })
getPoints!: IResourceMethodObservable<void, PointData[]>;

// 带参数的GET请求
@ResourceAction({ path: '/points/search' })
searchPoints!: IResourceMethodObservable<SearchParams, PointData[]>;

// 路径参数
@ResourceAction({ path: '/points/{id}' })
getPoint!: IResourceMethodObservable<{id: string}, PointData>;
```

### POST 请求

```typescript
@ResourceAction({ 
  path: '/points', 
  method: 'POST' 
})
createPoint!: IResourceMethodObservable<CreatePointData, PointData>;
```

### 分页接口

```typescript
@ResourceAction({ path: '/points/grid' })
getPointsGrid!: IResourceMethodObservable<PageParams, PageGridResult<PointData[]>>;
```

## 类型定义

### 请求参数类型

```typescript
interface SearchParams {
  keyword?: string;
  pageSize?: number;
  pageNum?: number;
}
```

### 响应数据类型

```typescript
interface PointData {
  id: string;
  name: string;
  coordinates: [number, number];
}

interface PageGridResult<T> {
  data: T;
  total: number;
  pageNum: number;
  pageSize: number;
}
```

### 泛型声明

```typescript
// IResourceMethodObservable<请求参数类型, 响应数据类型>
getPoints!: IResourceMethodObservable<SearchParams, PointData[]>;
```

## 实际使用

### 1. 注入服务

```typescript
export class PointComponent {
  constructor(private pointService: KeypointViewService) {}
}
```

### 2. 调用接口

```typescript
// 无参数调用
this.pointService.getPoints().subscribe(points => {
  console.log(points);
});

// 带参数调用
this.pointService.searchPoints({ keyword: 'test' }).subscribe(result => {
  console.log(result);
});

// 路径参数
this.pointService.getPoint({ id: '123' }).subscribe(point => {
  console.log(point);
});

// 分页调用
this.pointService.getPointsGrid({ 
  pageNum: 1, 
  pageSize: 10 
}).subscribe(result => {
  console.log(result.data, result.total);
});
```

## 常见配置

### 路径参数

```typescript
// 定义：/users/{userId}/posts/{postId}
@ResourceAction({ path: '/users/{userId}/posts/{postId}' })
getUserPost!: IResourceMethodObservable<{userId: string, postId: string}, Post>;

// 调用
this.service.getUserPost({ userId: '1', postId: '2' });
```

### 查询参数

```typescript
// 自动转换为查询字符串
this.service.searchPoints({ 
  keyword: 'test', 
  page: 1 
});
// 实际请求：GET /points/search?keyword=test&page=1
```

### 请求体

```typescript
@ResourceAction({ 
  path: '/points', 
  method: 'POST' 
})
createPoint!: IResourceMethodObservable<CreatePointData, PointData>;

// POST请求的参数自动作为请求体
this.service.createPoint({ 
  name: 'New Point',
  coordinates: [120, 30]
});
```

## 错误处理

```typescript
this.pointService.getPoints().subscribe({
  next: (data) => console.log(data),
  error: (error) => {
    console.error('请求失败:', error);
    // 处理错误
  }
});
```

## 最佳实践

### 1. 类型安全

```typescript
// ✅ 推荐：明确的类型定义
getPoints!: IResourceMethodObservable<SearchParams, PointData[]>;

// ❌ 避免：使用any类型
getPoints!: IResourceMethodObservable<any, any>;
```

### 2. 接口命名

```typescript
// ✅ 推荐：语义化命名
getPointsList!: IResourceMethodObservable<void, PointData[]>;
getPointsGrid!: IResourceMethodObservable<PageParams, PageGridResult<PointData[]>>;
createPoint!: IResourceMethodObservable<CreatePointData, PointData>;

// ❌ 避免：模糊命名
api1!: IResourceMethodObservable<any, any>;
```

### 3. 路径设计

```typescript
// ✅ 推荐：RESTful风格
@ResourceAction({ path: '/points' })          // GET 获取列表
@ResourceAction({ path: '/points/{id}' })     // GET 获取单个
@ResourceAction({ path: '/points', method: 'POST' })  // POST 创建

// ❌ 避免：非标准路径
@ResourceAction({ path: '/getPointsList' })
@ResourceAction({ path: '/createNewPoint' })
```

### 4. 版本兼容性说明

```typescript
// ✅ 推荐：使用枚举（稳定版本的标准写法）
@ResourceAction({ 
  path: '/points',
  method: ResourceRequestMethod.Get
})

// ✅ 可选：省略默认的GET方法（某些新版本支持）
@ResourceAction({ path: '/points' })

// ❌ 可能不支持：直接使用字符串（版本兼容性问题）
@ResourceAction({ 
  path: '/points',
  method: 'GET'  // 某些版本可能不识别
})

// 必需的导入
import { ResourceRequestMethod } from '@ngx-resource/core';
```

**版本差异说明：**
- 稳定版本通常需要使用 `ResourceRequestMethod` 枚举
- 较新版本可能支持省略默认GET方法或使用字符串
- 建议根据项目实际使用的版本选择合适的写法

## 完整示例

```typescript
import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams } from '@ngx-resource/core';

interface PointData {
  pointCode: string;
  pointName: string;
  geom: string;
}

interface QueryParams {
  keyword?: string;
  pageSize?: number;
  pageNum?: number;
}

@ResourceParams({ pathPrefix: '/api/v2' })
@Injectable({ providedIn: 'root' })
export class PointService extends Resource {
  
  @ResourceAction({ path: '/points/list' })
  getPointsList!: IResourceMethodObservable<QueryParams, PointData[]>;
  
  @ResourceAction({ path: '/points/grid' })
  getPointsGrid!: IResourceMethodObservable<QueryParams, PageGridResult<PointData[]>>;
  
  @ResourceAction({ path: '/points/{id}' })
  getPoint!: IResourceMethodObservable<{id: string}, PointData>;
  
  @ResourceAction({ path: '/points', method: 'POST' })
  createPoint!: IResourceMethodObservable<Partial<PointData>, PointData>;
}
```

这就是 @ngx-resource 的核心用法，简单高效的 Angular HTTP 客户端解决方案。