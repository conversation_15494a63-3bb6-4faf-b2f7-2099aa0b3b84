<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start" class="title-start-back">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>消息详情</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="body">
  <div class="news-title">
    {{ newsInfo.msgTitle }}
  </div>
  <div class="news-meta">
    <span style="margin-left: 8px;">发布人：{{ newsInfo.sendUserName }}</span>
    <span style="float: right;margin-right: 8px;">{{ newsInfo.sendTime }}</span>
  </div>
  <div class="news-content">
    {{ newsInfo.msgContent }}
  </div>
</ion-content>
