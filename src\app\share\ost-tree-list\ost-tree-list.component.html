<ul class="tree-list-itmes">
  <ng-container *ngFor="let item of items">
    <li
      TreeListItem
      *ngIf="!item.hidden"
      [showDirBtn]="showDirBtn"
      [dirBtnText]="dirBtnText"
      [showSubBtn]="showSubBtn"
      [showSubIcon]="showSubIcon"
      [subBtnText]="subBtnText"
      [listItem]="item"
      [class.tree-list-group]="item.group"
      (toggleSubItem)="onToggleSubMenu($event)"
      (itemClick)="onItemClick($event)"
      (subItemClick)="onSubItemClick($event)"
      (itemBtnClick)="onItemBtnClick($event)"
      class="tree-list-itme"
    >
    </li>
  </ng-container>
</ul>