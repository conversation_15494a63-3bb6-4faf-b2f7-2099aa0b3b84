import { Injectable } from '@angular/core';
import { StorageService } from '../storage.service';
import { SyncDataType, SyncCacheItem } from './types/sync-data.types';

/**
 * 数据同步缓存管理服务
 * 负责本地缓存的增删改查操作
 */
@Injectable({
  providedIn: 'root'
})
export class SyncCacheManagerService {

  constructor(private storageService: StorageService) { }

  /**
   * 获取本地缓存的存储key（每种业务类型单独一个key）
   */
  private getStorageKey(type: SyncDataType): string {
    return `sync_cache_${type}`;
  }

  /**
   * 添加数据到本地缓存
   */
  async addToCache(type: SyncDataType, data: any, uploadUrl: string, method: string = 'POST'): Promise<SyncCacheItem> {
    const cacheKey = this.getStorageKey(type);
    const cacheList: SyncCacheItem[] = (await this.storageService.get(cacheKey).toPromise()) || [];
    
    const cacheItem: SyncCacheItem = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      uploadUrl,
      method
    };
    
    cacheList.push(cacheItem);
    await this.storageService.set(cacheKey, cacheList).toPromise();
    
    return cacheItem;
  }

  /**
   * 获取指定类型的本地缓存数据
   */
  async getCache(type: SyncDataType): Promise<SyncCacheItem[]> {
    const cacheKey = this.getStorageKey(type);
    return (await this.storageService.get(cacheKey).toPromise()) || [];
  }

  /**
   * 获取所有类型的缓存数据
   */
  async getAllCache(): Promise<Map<SyncDataType, SyncCacheItem[]>> {
    const types = Object.values(SyncDataType);
    const cacheMap = new Map<SyncDataType, SyncCacheItem[]>();
    
    for (const type of types) {
      const cacheList = await this.getCache(type);
      if (cacheList.length > 0) {
        cacheMap.set(type, cacheList);
      }
    }
    
    return cacheMap;
  }

  /**
   * 移除指定类型的缓存项
   */
  async removeFromCache(type: SyncDataType, id: string): Promise<void> {
    const cacheKey = this.getStorageKey(type);
    const cacheList = await this.getCache(type);
    const newList = cacheList.filter(item => item.id !== id);
    await this.storageService.set(cacheKey, newList).toPromise();
  }

  /**
   * 批量移除缓存项
   */
  async removeBatchFromCache(type: SyncDataType, ids: string[]): Promise<void> {
    if (ids.length === 0) return;
    
    const cacheKey = this.getStorageKey(type);
    const cacheList = await this.getCache(type);
    const idsSet = new Set(ids);
    const newList = cacheList.filter(item => !idsSet.has(item.id));
    await this.storageService.set(cacheKey, newList).toPromise();
  }

  /**
   * 更新缓存中指定项目的状态（主要用于更新重试次数）
   */
  async updateCacheItems(type: SyncDataType, itemsToUpdate: SyncCacheItem[]): Promise<void> {
    if (!itemsToUpdate.length) return;

    const cacheKey = this.getStorageKey(type);
    const allItems = await this.getCache(type);

    // 创建一个查找映射以提高效率
    const updateMap = new Map(itemsToUpdate.map(item => [item.id, item]));

    const updatedList = allItems.map(cachedItem => {
      return updateMap.get(cachedItem.id) || cachedItem;
    });

    await this.storageService.set(cacheKey, updatedList).toPromise();
    console.log(`[SyncCacheManager] 已更新 ${itemsToUpdate.length} 个项目的状态 (类型=${type})。`);
  }

  /**
   * 清空指定类型的所有缓存
   */
  async clearCache(type: SyncDataType): Promise<void> {
    const cacheKey = this.getStorageKey(type);
    await this.storageService.set(cacheKey, []).toPromise();
  }

  /**
   * 清空所有类型的缓存
   */
  async clearAllCache(): Promise<void> {
    const types = Object.values(SyncDataType);
    await Promise.all(types.map(type => this.clearCache(type)));
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<{ type: SyncDataType, count: number, totalSize: number }[]> {
    const types = Object.values(SyncDataType);
    const stats = [];
    
    for (const type of types) {
      const cacheList = await this.getCache(type);
      const totalSize = JSON.stringify(cacheList).length;
      stats.push({
        type,
        count: cacheList.length,
        totalSize
      });
    }
    
    return stats;
  }
}
