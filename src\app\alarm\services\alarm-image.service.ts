import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ShareModuleService } from 'src/app/share/share.service';
import { RequestResult } from 'src/app/@core/base/request-result';
import { Alarm } from '../alarm-model-config';
import { IMAGE_CONFIG } from 'src/app/share/images/images.config';

/**
 * 告警图片处理服务
 * 负责处理告警数据中的图片获取和URL构建
 */
@Injectable({
  providedIn: 'root'
})
export class AlarmImageService {

  constructor(private netSer: ShareModuleService) { }

  /**
   * 处理告警图片，通过picCode获取图片URL
   * @param alarms 告警数据数组
   * @param destroy$ 用于取消订阅的信号
   */
  processAlarmImages(alarms: Alarm[], destroy$: Subject<void>): void {
    alarms.forEach(alarm => {
      // 只有当remark包含"图片"字样时才处理文件详情
      if (this.shouldProcessImages(alarm)) {
        // 初始化图片URL数组
        alarm.picUrls = [];
        
        // 为每个picCode获取图片URL
        alarm.picCode.forEach(fileCode => {
          this.getImageUrl(fileCode, alarm, destroy$);
        });
      }
    });
  }

  /**
   * 判断是否应该处理图片
   * @param alarm 告警对象
   * @returns 是否应该处理图片
   */
  private shouldProcessImages(alarm: Alarm): boolean {
    return alarm.remark && 
          //  alarm.remark.includes('图片') && 
           alarm.picCode && 
           alarm.picCode.length > 0;
  }

  /**
   * 通过文件编码获取图片URL
   * @param fileCode 文件编码
   * @param alarm 告警对象
   * @param destroy$ 用于取消订阅的信号
   */
  private getImageUrl(fileCode: string, alarm: Alarm, destroy$: Subject<void>): void {
    this.netSer.GetFile([fileCode]).pipe(
      takeUntil(destroy$)
    ).subscribe({
      next: (result: RequestResult<any>) => {
        const { code, data, msg } = result;
        if (code === 0 && data && data.length > 0) {
          const { protocol, host, port } = IMAGE_CONFIG.server;
          const imageUrl = `${protocol}://${host}:${port}${data[0].urlPath}`;
          
          // 将图片URL添加到告警对象的picUrls数组中
          if (!alarm.picUrls) {
            alarm.picUrls = [];
          }
          alarm.picUrls.push(imageUrl);
        } else {
          console.warn('获取图片失败:', msg);
        }
      },
      error: (error) => {
        console.error('获取图片失败:', error);
      }
    });
  }

  /**
   * 获取第一张图片的URL
   * @param alarm 告警对象
   * @returns 图片URL或默认图片
   */
  getFirstImageUrl(alarm: Alarm): string {
    // 只有当remark包含"图片"字样且有图片URL时才显示图片
    if (this.shouldProcessImages(alarm) && alarm.picUrls && alarm.picUrls.length > 0) {
      return alarm.picUrls[0];
    }
    return 'https://ionicframework.com/docs/img/demos/thumbnail.svg';
  }

  /**
   * 判断是否应该显示图片数量标签
   * @param alarm 告警对象
   * @returns 是否应该显示数量标签
   */
  shouldShowImageCount(alarm: Alarm): boolean {
    return this.shouldProcessImages(alarm) && 
           alarm.picUrls && 
           alarm.picUrls.length > 1;
  }
} 