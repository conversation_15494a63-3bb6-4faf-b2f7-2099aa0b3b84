p {
  color: #000;
  // letter-spacing: 1px; //字符间距
}

ion-card {
  pointer-events: all;
}

.tips-layout {
  position: relative;
  width: 100vw;
  height: 100px;
  top: -35vh;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 16px;

  .start-msg {
    justify-content: center;
    align-items: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: row;
    padding: 5px;
    background-color: #4d80cf;
    font: 900;
    color: #fff;
  }
}

.click-events-none {
  pointer-events: none;
  touch-action: none;
}


.header {
  padding-top: 12px;
  padding-bottom: 12px;

  .title {
    color: black;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
  }
}

.task-item {
  display: flex;

  .item-img {
    background: #5499f7;
    width: 40px;
    height: 40px;
    img{
      padding: 3px;
    }
  }

  .name {
    padding-left: 12px;
    font-size: 14px;
    color: #000;

    .subtitle {
      font-size: 12px;
      color: #737373;
    }
    span{
      padding-left: 10px;
    }
  }
}

.row {
  display: flex;
  flex-flow: row wrap;
  justify-content: center;
  padding-top: 6px;

  .row-item {
    width: 30%;
    position: relative;
    text-align: center;

    .item-headerInfo {
      display: inline-block;
      color: #888888;
      font-size: 12px;
      line-height: 20px;
    }

    .item-cont {
      margin: 0;
      color: var(--ion-color-primary);
      font-size: 18px;
      line-height: 20px;
      font-weight: 600;
    }

    em {
      position: absolute;
      top: 10px;
      right: 0;
      width: 2px;
      height: 26px;
      background-color: #f6f6f6;
    }
  }
}