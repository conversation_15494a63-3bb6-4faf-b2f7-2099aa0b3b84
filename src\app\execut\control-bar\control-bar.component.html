<!-- 提醒框布局 -->
<div class="tips-layout">
  <div
    class="start-msg"
    *ngIf="showStartMsg"
  >
    <ion-spinner></ion-spinner>
    获取位置中...请勿关闭手机，长时间无法获取请尝试移动到空旷地带
  </div>
  <ost-ring
    *ngIf="showRing"
    [rate]="ringRate"
  ></ost-ring>
</div>

<ion-card [hidden]="!controlBarShowState">
  <ion-card-header class="header">
    <ion-card-subtitle class="title">
      <ng-container *ngIf="task">{{task.taskName}}</ng-container>
      <ng-container *ngIf="!task">{{'巡检任务'+ nowDate }}</ng-container>
    </ion-card-subtitle>
  </ion-card-header>

  <ion-card-content>
    <div class="task-item">
      <div class="item-img">
        <img src="/assets/map/key-point.png" alt="巡检点">
      </div>
      <div class="name">
        <span style="padding: 0;">X:{{currentLocation.longitude.toFixed(4)}}</span>
        <span>Y:{{currentLocation.latitude.toFixed(4)}}</span>
        <span>速度:{{ (currentLocation.speed * 3.6).toFixed(2) }} km/h</span>
        <P class="subtitle">已巡检时间 <span>{{trackTime}}</span>&nbsp;&nbsp;|&nbsp;&nbsp;偏差值&nbsp;&nbsp;{{currentLocation.accuracy.toFixed(2)}}m</P>
      </div>
    </div>
    <ion-button color="primary"expand="block"
      [hidden]="trackRunState !== 'notstart'"
      (click)="onPlayClick()">
      开始巡检
    </ion-button>
    <ion-button color="danger"expand="block"
      [hidden]="trackRunState !== 'running'"
      (ngPress)="onFinishStartPress()"
      (ngRelease)="onFinishStopPress()"
      (ngPressing)="onFinishPressing($event)"
    >
      长按结束巡检
    </ion-button>
    <div class="row">
      <div class="row-item" (click)="onKeyPointClick()">
        <!-- 关键点采集 -->
        <p class="item-cont" style="color: #5fb8aa;">
          <ion-icon name="location-outline"></ion-icon>
        </p>
        <span class="item-headerInfo">关键点采集</span>
        <em></em>
      </div>
      <div class="row-item" (click)="onEvReportClick()">
        <!-- 事项上报 -->
        <p class="item-cont" style="color: #ffa200;">
          <ion-icon name="create-outline"></ion-icon>
        </p>
        <span class="item-headerInfo">事项上报</span>
        <em *ngIf="trackRunState === 'running'"></em>
      </div>
      <div class="row-item" *ngIf="trackRunState === 'running'" (click)="onClockInClick()">
          <p class="item-cont" style="color: #21c97a;">
          <ion-icon name="checkmark-done-outline"></ion-icon>
        </p>
        <span class="item-headerInfo">手动打卡</span>
      </div>
    </div>
  </ion-card-content>
</ion-card>
