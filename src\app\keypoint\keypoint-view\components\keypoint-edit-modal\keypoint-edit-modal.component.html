<ion-header>
  <ion-toolbar color="primary">
    <ion-title>编辑关键点</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="onCancel()" [disabled]="isSubmitting">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="edit-modal-content">
  <form [formGroup]="form" class="edit-form">
    
    <!-- 关键点名称 -->
    <div class="form-section">
      <ion-item class="form-item" [class.error]="hasFieldError('pointName')">
        <ion-label position="stacked" color="primary">
          <ion-icon name="location-outline" class="field-icon"></ion-icon>
          关键点名称 <span class="required">*</span>
        </ion-label>
        <ion-input 
          formControlName="pointName"
          placeholder="请输入关键点名称"
          maxlength="50"
          [disabled]="isSubmitting">
        </ion-input>
      </ion-item>
      <div class="error-message" *ngIf="hasFieldError('pointName')">
        {{ getFieldError('pointName') }}
      </div>
    </div>

    <!-- 巡检方式 -->
    <div class="form-section">
      <ion-item class="form-item" [class.error]="hasFieldError('inspectionMethod')">
        <ion-label position="stacked" color="primary">
          <ion-icon name="walk-outline" class="field-icon"></ion-icon>
          巡检方式 <span class="required">*</span>
        </ion-label>
        <ion-select 
          formControlName="inspectionMethod"
          placeholder="请选择巡检方式"
          interface="popover"
          [disabled]="isSubmitting">
          <ion-select-option value="巡视">
            <ion-icon name="car-outline" slot="start"></ion-icon>
            巡视
          </ion-select-option>
          <ion-select-option value="巡查">
            <ion-icon name="walk-outline" slot="start"></ion-icon>
            巡查
          </ion-select-option>
        </ion-select>
      </ion-item>
      <div class="error-message" *ngIf="hasFieldError('inspectionMethod')">
        {{ getFieldError('inspectionMethod') }}
      </div>
    </div>

    <!-- 巡检天气 -->
    <div class="form-section">
      <ion-item class="form-item" [class.error]="hasFieldError('isItRaining')">
        <ion-label position="stacked" color="primary">
          <ion-icon name="partly-sunny-outline" class="field-icon"></ion-icon>
          巡检天气 <span class="required">*</span>
        </ion-label>
        <ion-select 
          formControlName="isItRaining"
          placeholder="请选择巡检天气"
          interface="popover"
          [disabled]="isSubmitting">
          <ion-select-option value="否">
            ☀️🌧️
          </ion-select-option>
          <ion-select-option value="是">
            ☀️
          </ion-select-option>
        </ion-select>
      </ion-item>
      <div class="error-message" *ngIf="hasFieldError('isItRaining')">
        {{ getFieldError('isItRaining') }}
      </div>
    </div>

    <!-- 缓冲范围 -->
    <div class="form-section">
      <ion-item class="form-item" [class.error]="hasFieldError('bufferRange')">
        <ion-label position="stacked" color="primary">
          <ion-icon name="resize-outline" class="field-icon"></ion-icon>
          缓冲范围 (米) <span class="required">*</span>
        </ion-label>
        <ion-input 
          formControlName="bufferRange"
          type="number"
          placeholder="请输入缓冲范围"
          min="1"
          max="1000"
          [disabled]="isSubmitting">
        </ion-input>
      </ion-item>
      <div class="error-message" *ngIf="hasFieldError('bufferRange')">
        {{ getFieldError('bufferRange') }}
      </div>
    </div>

    <!-- 表单提示信息 -->
    <div class="form-tips">
      <ion-icon name="information-circle-outline" color="medium"></ion-icon>
      <span>请确保填写的信息准确无误，保存后将更新关键点数据</span>
    </div>

  </form>
</ion-content>

<ion-footer class="edit-modal-footer">
  <ion-toolbar>
    <div class="footer-buttons">
      <!-- 重置按钮 -->
      <ion-button 
        fill="outline" 
        color="medium"
        (click)="onReset()"
        [disabled]="isSubmitting"
        class="reset-button">
        <ion-icon name="refresh-outline" slot="start"></ion-icon>
        重置
      </ion-button>
      
      <!-- 取消按钮 -->
      <ion-button 
        fill="outline" 
        color="medium"
        (click)="onCancel()"
        [disabled]="isSubmitting"
        class="cancel-button">
        <ion-icon name="close-outline" slot="start"></ion-icon>
        取消
      </ion-button>
      
      <!-- 保存按钮 -->
      <ion-button 
        expand="block" 
        color="primary"
        (click)="onSave()"
        [disabled]="!form.valid || isSubmitting"
        class="save-button">
        <ion-spinner name="crescent" *ngIf="isSubmitting" slot="start"></ion-spinner>
        <ion-icon name="checkmark-outline" *ngIf="!isSubmitting" slot="start"></ion-icon>
        {{ isSubmitting ? '保存中...' : '保存' }}
      </ion-button>
    </div>
  </ion-toolbar>
</ion-footer>
