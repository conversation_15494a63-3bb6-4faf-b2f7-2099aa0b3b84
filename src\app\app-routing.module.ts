import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { ChangPasswordComponent } from './self/chang-password/chang-password.component';
import { AuthGuard } from './auth/auth.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: '/tabs/home',
    pathMatch: 'full',
  },
  {
    path: 'tabs',
    loadChildren: () => import('./tabs/tabs.module').then(m => m.TabsPageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
  },
  // 实时监控
  {
    path: 'app/monitor',
    loadChildren: () => import('./monitor/monitor.module').then(m => m.MonitorPageModule),
    canActivate: [AuthGuard]
  },
  // 巡检统计
  {
    path: 'app/statistics',
    loadChildren: () => import('./statistics/statistics.module').then(m => m.StatisticsPageModule),
    canActivate: [AuthGuard]
  },
  // 报警信息
  {
    path: 'app/alarm',
    loadChildren: () => import('./alarm/alarm.module').then(m => m.AlarmPageModule),
    canActivate: [AuthGuard]
  },
  // 关键点管理
  {
    path: 'app/keypoint',
    loadChildren: () => import('./keypoint/keypoint.module').then(m => m.KeypointPageModule),
    canActivate: [AuthGuard]
  },
  // 修改密码
  {
    path: 'editPassword',
    component: ChangPasswordComponent,
  },
];
@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
