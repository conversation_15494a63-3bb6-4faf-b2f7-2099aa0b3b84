@import "../../../../theme/variables.scss";

// 下载弹窗容器
.download-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 70vh;
  min-height: 200px;
  overflow: hidden;
  box-sizing: border-box;
}

.download-header {
  text-align: center;
  min-height: 42px;
  height: 42px;
  line-height: 42px;
  width: 100%;
  color: var(--ion-color-primary-contrast);
  background-color: var(--ion-color-primary);
  font-size: 16px;
  font-weight: 500;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  box-sizing: border-box;
}

.download-context {
  padding: 20px 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 100px;
  background-color: #fff;

  div {
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #333;
    font-weight: 500;
    line-height: 1.4;
  }

  ion-progress-bar {
    width: 100%;
    height: 6px;
    margin-top: 8px;
  }

  // 错误信息样式
  .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .error-text {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      font-weight: 400;
    }
  }
}

.download-footer {
  border-top: 1px solid #f6f6f6;
  min-height: 42px;
  height: 42px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  flex-shrink: 0;
  box-sizing: border-box;

  div {
    width: 100%;
    font-size: 14px;
    color: #666;
    font-weight: 500;
    line-height: 1.4;
    text-align: center;
  }
}

.btn-install {
  width: 100%;
  text-align: center;
  color: var(--ion-color-primary);
  font-size: 14px;
  font-weight: 500;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;

  &:hover {
    background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  }

  &:active {
    background-color: rgba(var(--ion-color-primary-rgb), 0.2);
  }
}

.btn-copy {
  width: 100%;
  text-align: center;
  color: #ff6b6b;
  font-size: 14px;
  font-weight: 500;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  gap: 6px;

  ion-icon {
    font-size: 16px;
  }

  &:hover {
    background-color: rgba(255, 107, 107, 0.1);
  }

  &:active {
    background-color: rgba(255, 107, 107, 0.2);
  }
}

// 响应式适配
@media (max-height: 600px) {
  .download-container {
    max-height: 80vh;
  }

  .download-header {
    min-height: 38px;
    height: 38px;
    line-height: 38px;
    font-size: 15px;
    padding: 0 12px;
  }

  .download-context {
    padding: 16px 12px;
    min-height: 80px;

    div {
      font-size: 13px;
      margin-bottom: 10px;
    }
  }

  .download-footer {
    min-height: 38px;
    height: 38px;
    padding: 0 12px;

    div {
      font-size: 13px;
    }
  }

  .btn-install,
  .btn-copy {
    min-height: 38px;
    padding: 10px 12px;
    font-size: 13px;
  }

  .error-message {
    padding: 12px 0;

    .error-text {
      font-size: 13px;
    }
  }
}

@media (max-height: 500px) {
  .download-container {
    max-height: 90vh;
  }

  .download-header {
    min-height: 36px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    padding: 0 8px;
  }

  .download-context {
    padding: 12px 8px;
    min-height: 60px;

    div {
      font-size: 12px;
      margin-bottom: 8px;
    }
  }

  .download-footer {
    min-height: 36px;
    height: 36px;
    padding: 0 8px;

    div {
      font-size: 12px;
    }
  }

  .btn-install,
  .btn-copy {
    min-height: 36px;
    padding: 8px 10px;
    font-size: 12px;
  }

  .error-message {
    padding: 10px 0;

    .error-text {
      font-size: 12px;
    }
  }
}

// 特殊机型兼容性处理
@media (max-width: 320px) {
  .download-container {
    max-height: 95vh;
  }

  .download-context {
    padding: 8px;

    div {
      font-size: 12px;
    }
  }

  .btn-install,
  .btn-copy {
    font-size: 12px;
    padding: 8px;
  }

  .error-message {
    padding: 8px 0;

    .error-text {
      font-size: 11px;
    }
  }
}

// 确保文字在所有设备上都可见
.download-header,
.download-context div,
.download-footer div,
.btn-install,
.btn-copy,
.error-message {
  // 防止文字被截断
  word-wrap: break-word;
  word-break: break-word;
  // 确保文字颜色对比度
  text-shadow: none;
  // 防止字体渲染问题
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
