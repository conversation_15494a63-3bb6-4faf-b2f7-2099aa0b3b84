/**
 * 实时监控
 */
export class Summary {
    /**
     * 部门id
     */
    depCode:string;
    /**
     * 部门名称
     */
    depName:string;
    /**
     * 巡线工总数
     */
    userCount: number = 0;
    /**
     * 在线人数
     */
    onlineCount: number = 0;
    /**
     * 离线人数
     */
    unOnlineCount: number = 0;
}
/**
 * 巡检任务在线人员
 */
export class TaskOnlineUser {
    /**
     * 巡线工id
     */
    userCode: string;
    /**
     * 巡线工名称
     */
    userName: string;
    /**
     * 选中
     */
    select = false;
}
