.track-control {
  width: 100vw;
  background-color: #fff;
  box-shadow: 0 0 10px #555;
  pointer-events: all;
  .layout-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    ion-range {
      padding: 0 10px;
    }
  }
  .ion-text-center {
    border-top: 1px solid #eee;
    .track-date{
      width: 100%; 
      color: #4d80cf;
    }
    .icon-div {
      padding-right: 15px;
      padding-left: 15px;
      .icon-suspend {
        color: #2dd36f;
        font-size: 24px;
      }
      .icon-cancel-suspend {
        color: #ffc409;
        font-size: 24px;
      }
      .icon-stop {
        color: #eb445a;
        font-size: 24px;
      }
    }
  }

  // 用户选择器样式
  .user-selector {
    position: relative;
    padding: 10px;
    
    .user-selected {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
      cursor: pointer;
      
      span {
        font-size: 14px;
        color: #333;
      }
      
      ion-icon {
        font-size: 16px;
        transition: transform 0.3s ease;
        
        &.rotate-icon {
          transform: rotate(180deg);
        }
      }
    }
    
    .user-list {
      position: absolute;
      bottom: 100%;
      left: 10px;
      right: 10px;
      background-color: white;
      border: 1px solid #eee;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      max-height: 160px;
      overflow-y: auto;
      margin-bottom: 5px;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;

        &:hover {
          background: #a8a8a8;
        }
      }
      opacity: 0;
      transform: translateY(10px);
      transition: all 0.2s ease-in-out;
      pointer-events: none;
      padding: 8px;

      &.show {
        opacity: 1;
        transform: translateY(0);
        pointer-events: all;
      }

      // 使用 CSS Grid 实现多列布局
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 6px;

      .user-item {
        padding: 8px 10px;
        cursor: pointer;
        font-size: 13px;
        border-radius: 4px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;

        &.active {
          background-color: #e6f0ff;
          color: #4d80cf;
          font-weight: 500;
          border-color: #4d80cf;
        }
      }
    }
  }
}

// 响应式多列布局优化
@media (max-width: 320px) {
  .track-control {
    .user-selector {
      .user-list {
        // 超小屏幕：单列布局
        grid-template-columns: 1fr;

        .user-item {
          font-size: 12px;
          padding: 6px 8px;
        }
      }
    }
  }
}

@media (min-width: 321px) and (max-width: 480px) {
  .track-control {
    .user-selector {
      .user-list {
        // 小屏幕：两列布局
        grid-template-columns: repeat(2, 1fr);
        gap: 4px;

        .user-item {
          font-size: 12px;
          padding: 7px 8px;
        }
      }
    }
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .track-control {
    .user-selector {
      .user-list {
        // 中等屏幕：三列布局
        grid-template-columns: repeat(3, 1fr);

        .user-item {
          font-size: 13px;
        }
      }
    }
  }
}

@media (min-width: 769px) {
  .track-control {
    .user-selector {
      .user-list {
        // 大屏幕：四列或更多列布局
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        max-width: 500px;

        .user-item {
          font-size: 14px;
          padding: 10px 12px;
        }
      }
    }
  }
}
