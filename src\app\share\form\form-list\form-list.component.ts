import { Component, Input, OnChanges, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DetailsMode } from 'src/app/@core/base/environment';
import { OstFormService } from '../ost-form.service';

@Component({
  selector: 'ost-form-list',
  templateUrl: './form-list.component.html',
  styleUrls: ['./form-list.component.scss'],
  providers: [OstFormService]
})
export class FormListComponent implements OnInit, OnChanges {
  @Input() formGroup: FormGroup;
  @Input() modelMode: DetailsMode;

  constructor(public ostFormServe: OstFormService) { }

  ngOnInit(): void {
    this.ostFormServe.formGroup = this.formGroup;
    this.ostFormServe.modelMode = this.modelMode;
  }

  ngOnChanges(): void {
    this.ostFormServe.formGroup = this.formGroup;
    this.ostFormServe.modelMode = this.modelMode;
  }

}
