.list-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.keypoint-content {
  flex: 1;
  --padding-top: 0;
  --padding-bottom: 0;
}

.filter-toolbar {
  background: var(--ion-color-light);
  padding: 12px 16px;
  border-bottom: 1px solid var(--ion-color-light-shade);
  
  .search-bar {
    margin-bottom: 12px;
    --background: white;
    --border-radius: 12px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .filter-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .filter-group {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      
      ion-button {
        --border-radius: 16px;
        --padding-start: 10px;
        --padding-end: 10px;
        --padding-top: 4px;
        --padding-bottom: 4px;
        height: 28px;
        font-size: 11px;
        font-weight: 500;
        
        ion-icon {
          font-size: 12px;
          margin-right: 3px;
        }
      }
    }
  }
}

.action-toolbar {
  padding: 8px 16px;
  border-bottom: 1px solid var(--ion-color-light-shade);
  background: var(--ion-color-light);

  ion-button {
    --border-radius: 8px;
    --padding-start: 12px;
    --padding-end: 12px;
    height: 32px;
    font-size: 12px;
    font-weight: 500;

    ion-icon {
      font-size: 14px;
      margin-right: 4px;
    }
  }
}

.selection-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--ion-color-primary);
  color: white;
  border-bottom: 1px solid var(--ion-color-primary-shade);

  .selection-info {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;

    ion-icon {
      margin-right: 6px;
      font-size: 16px;
    }
  }

  .selection-actions {
    display: flex;
    gap: 4px;

    ion-button {
      --color: white;
      --border-color: rgba(255, 255, 255, 0.3);
      --border-radius: 6px;
      height: 28px;
      font-size: 12px;

      ion-icon {
        font-size: 14px;
        margin-right: 3px;
      }

      &[color="danger"] {
        --color: var(--ion-color-danger);
        --background: rgba(255, 255, 255, 0.1);
        --border-color: rgba(var(--ion-color-danger-rgb), 0.5);

        &:hover {
          --background: rgba(var(--ion-color-danger-rgb), 0.1);
        }
      }
    }
  }
}

.keypoint-list {
  padding-bottom: 50px;
  
  ion-list {
    padding: 0 0 20px 0;
  }
}

.inspection-card {
  width: calc(100% - 12px);
  margin: 6px;
  padding: 10px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid rgba(0,0,0,0.08);
  font-family: "Microsoft YaHei", sans-serif;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1.4;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.18);
    border-color: rgba(0,0,0,0.12);
  }
  
  &.inspected {
    background: rgba(76, 175, 80, 0.05);
    border-left: 4px solid var(--ion-color-success);
  }

  &.selected {
    background: rgba(var(--ion-color-primary-rgb), 0.1);
    border: 2px solid var(--ion-color-primary);
    transform: scale(0.98);
  }

  .selection-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
    --size: 20px;
    --checkmark-color: var(--ion-color-primary);
    --border-color: var(--ion-color-medium);
    --border-color-checked: var(--ion-color-primary);
    --background-checked: var(--ion-color-primary);
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.badge {
  display: inline-block;
  width: 22px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  background: #5c6fc7;
  color: #fff;
  border-radius: 50%;
  margin-right: 6px;
  font-size: 11px;
  font-weight: 600;
}

.card-header h2 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.card-body {
  margin-bottom: 8px;
}

.field {
  display: flex;
  align-items: center;
  margin: 4px 0;
  line-height: 1.3;
}

.field label {
  width: 66px;
  color: #666;
  flex-shrink: 0;
  font-size: 13px;
  font-weight: 500;
}

.field span {
  font-size: 13px;
  color: #333;
  line-height: 1.3;
}

.tag {
  padding: 3px 8px;
  background: #b3e5fc;
  color: #263238;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.3;
  
  &.vehicle-tag {
    background: #c8e6c9; /* 浅绿色 - 巡视 */
    color: #2e7d32;
  }
  
  &.person-tag {
    background: #ffe0b2; /* 浅橙色 - 巡查 */
    color: #e65100;
  }
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  margin-top: 0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;
  
  ion-icon {
    font-size: 14px;
  }
  
  span {
    color: #666;
  }
  
  &:hover {
    background: rgba(0, 0, 0, 0.05);
    
    span {
      color: #333;
    }
  }
  
  &.delete-action {
    ion-icon {
      color: #f56565;
    }
    
    &:hover span {
      color: #f56565;
    }
  }
  
  &.edit-action {
    ion-icon {
      color: #4299e1;
    }
    
    &:hover span {
      color: #4299e1;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  
  .empty-icon {
    font-size: 64px;
    color: var(--ion-color-light-shade);
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-medium);
    margin-bottom: 8px;
  }
  
  .empty-subtext {
    font-size: 16px;
    color: var(--ion-color-light-shade);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  .loading-text {
    margin-top: 16px;
    font-size: 16px;
    color: var(--ion-color-medium);
  }
}

.infinite-scroll {
  ion-infinite-scroll-content {
    text-align: center;
    
    .infinite-loading {
      margin: 16px 0;
    }
  }
}

@media (max-width: 768px) {
  .filter-toolbar {
    padding: 8px 12px;
    
    .search-bar {
      margin-bottom: 8px;
    }
    
    .filter-buttons-container {
      gap: 6px;
      
      .filter-group {
        gap: 4px;
        
        ion-button {
          --padding-start: 8px;
          --padding-end: 8px;
          height: 26px;
          font-size: 10px;
          
          ion-icon {
            font-size: 11px;
            margin-right: 2px;
          }
        }
      }
    }
  }

  .action-toolbar {
    padding: 6px 12px;

    ion-button {
      height: 28px;
      font-size: 11px;
      --padding-start: 10px;
      --padding-end: 10px;

      ion-icon {
        font-size: 12px;
      }
    }
  }

  .selection-toolbar {
    padding: 6px 12px;

    .selection-info {
      font-size: 12px;

      ion-icon {
        font-size: 14px;
      }
    }

    .selection-actions {
      gap: 3px;

      ion-button {
        height: 26px;
        font-size: 11px;

        ion-icon {
          font-size: 12px;
        }
      }
    }
  }

  .inspection-card {
    margin: 6px;
    padding: 8px;
    
    .selection-checkbox {
      --size: 18px;
    }

    .card-header {
      margin-bottom: 6px;
      
      .badge {
        width: 20px;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        margin-right: 5px;
      }
      
      h2 {
        font-size: 14px;
      }
    }
    
    .card-body {
      margin-bottom: 6px;
      
      .field {
        margin: 3px 0;
        
        label {
          width: 60px;
          font-size: 12px;
        }
        
        span {
          font-size: 12px;
        }
      }
    }
    
    .card-footer {
      gap: 4px;
      
      .action-item {
        padding: 3px 6px;
        font-size: 11px;
        
        ion-icon {
          font-size: 12px;
        }
      }
    }
  }
}

