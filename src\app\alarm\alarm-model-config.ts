/**
 * 告警信息接口
 */
export interface Alarm {
  /** 告警类型 */
  alarmType: string;
  /** 告警地点 */
  alarmPlace?: string;
  /** 告警时间 */
  alarmTime: string;
  /** 告警级别 */
  alarmLevel: string;
  /** 告警备注 */
  remark: string;
  /** 告警编码 */
  alarmCode: string;
  /** 用户名 */
  userName: string;
  /** 部门名称 */
  depName: string;
  /** 用户编码 */
  userCode: string;
  /** 部门编码 */
  depCode: string;
  /** 告警图片编码数组 */
  picCode?: string[];
  /** 告警图片URL数组（用于显示） */
  picUrls?: string[];
  readStatus: string;
}

/**
 * 告警查询参数接口
 */
export interface AlarmParams {
  /** 页码 */
  pageNum?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 告警类型：当前告警或历史告警 */
  readStatus?: '未读' | '已读';
}