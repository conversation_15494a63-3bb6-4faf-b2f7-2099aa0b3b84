/**
 * 关键点信息
 */
export class KeyPointInfo {
    /**
     * 关键点编码
     */
    pointCode: string;
    /**
     * 缓冲区范围
     */
    bufferRange = 10;
    /**
     * 关键点名称
     */
    pointName: string;
    /**
     * 巡检方式
     */
    inspectionMethod: string;
    /**
     * 关键点坐标
     */
    pointGeom: any = [];
    /**
     * 桩编码
     */
    stakeCode: string;
    stakeName: string;
    /**
     * 部门名称
     */
    depCode: string;
    depName: string;
    /**
     * 雨天不巡查
     */
    isItRaining: string;
    /**
     * 任务编码
     */
    taskCode: string;
}

/**
 * 位置信息
 */
export interface LocationInfo {
    /**
     * 经度
     */
    longitude: number;
    /**
     * 纬度
     */
    latitude: number;
    /**
     * 速度
     */
    speed: number;
    /**
     * 精度
     */
    accuracy: number;
    /**
     * 时间戳
     */
    trajectoryTime: number;
}

export class CurrentLocation {
    longitude = 0;
    latitude = 0;
    speed = 0;
    accuracy = 0;
    trajectoryTime = 0;
}