@import "../../../theme/variables.scss";
@import "../../../theme/dialog.scss";

.work-header {
    background: var(--ion-color-primary);
    padding: 12px 0;
}

::ng-deep.header-background {
    background: var(--ion-color-primary);
}

.search-bar {
    height: 40px;
    width: 100%;
    display: flex;
    align-items: center;
    padding-left: 6px;
    position: relative;

    p {
        height: 40px;
        line-height: 40px;
        position: absolute;
        font-size: 14px;
        right: 16px;
        color: #fff;
    }
}

.search-bar-parent {
    color: black;
    width: 80%;
    height: 40px;
    padding: 0 15px;
    font-size: 14px;
    background-color: #fff;
    border-radius: 3px;
    display: flex;
    align-items: center;

    ::-webkit-input-placeholder {
        color: #666666;
    }

    .login-form-input-icon {
        padding-right: 8px;
        text-align: center;
        height: 50%;
    }

    ion-input {
        padding-left: 6px;
        text-align: left;
    }
}

// Segment 容器样式
.segment-container {
    background: #fff;
    padding: 12px 16px 8px;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 10;

    ion-segment {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 4px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

        ion-segment-button {
            --color: #666;
            --color-checked: #fff;
            --background-checked: var(--ion-color-primary);
            --indicator-color: transparent;
            --border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            margin: 0 2px;
            min-height: 36px;
            transition: all 0.3s ease;

            // &.segment-button-checked {
            //     box-shadow: 0 2px 8px rgba(var(--ion-color-primary-rgb), 0.3);
            //     transform: translateY(-1px);
            // }

            // 不同事件类型的颜色
            &[value="管道占压"].segment-button-checked {
                --background-checked: #FF7557;
            }

            &[value="设备失效"].segment-button-checked {
                --background-checked: #4285F4;
            }

            &[value="第三方施工"].segment-button-checked {
                --background-checked: #ffaf46;
            }

            &[value="隐患上报"].segment-button-checked {
                --background-checked: #800080;
            }
        }
    }
}

ion-content {
    background: #f6f6f6;
    height: calc(100vh - 56px - 48px);
    margin-top: 0;
    border-top: none;

    // 确保内容区域与segment有明显分隔
    &::before {
        content: '';
        display: block;
        height: 8px;
        background: linear-gradient(to bottom, rgba(0,0,0,0.05), transparent);
        position: sticky;
        top: 0;
        z-index: 5;
    }

    .incident-card {
        margin: 12px 16px;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.2s ease, box-shadow 0.2s ease;

        &:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px 8px;
            border-bottom: 1px solid #f0f0f0;

            .event-type-badge {
                padding: 4px 12px;
                border-radius: 16px;
                font-size: 12px;
                font-weight: 500;
                color: #fff;

                &.type-pipeline-pressure {
                    background: linear-gradient(135deg, #FF7557, #FF9A7B);
                }

                &.type-equipment-failure {
                    background: linear-gradient(135deg, #4285F4, #6FA8F5);
                }

                &.type-third-party {
                    background: linear-gradient(135deg, #ffaf46, #FFC670);
                }

                &.type-hidden-danger {
                    background: linear-gradient(135deg, #800080, #A020A0);
                }

                &.type-default {
                    background: linear-gradient(135deg, #7d7d7d, #9d9d9d);
                }
            }

            .event-status {
                padding: 3px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 500;

                &.status-cleared {
                    background: #e8f5e8;
                    color: #2e7d32;
                }

                &.status-uncleared {
                    background: #ffebee;
                    color: #c62828;
                }

                &.status-default {
                    background: #f5f5f5;
                    color: #666;
                }
            }
        }

        .event-description {
            padding: 12px 16px 8px;
            display: flex;
            align-items: flex-start;

            .desc-icon {
                color: #666;
                font-size: 16px;
                margin-right: 8px;
                margin-top: 2px;
                flex-shrink: 0;
            }

            .desc-text {
                font-size: 14px;
                color: #333;
                line-height: 1.4;
                flex: 1;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
        }

        .pipeline-info {
            padding: 0 16px 12px;
            display: flex;
            align-items: center;

            .pipeline-icon {
                color: #4285F4;
                font-size: 16px;
                margin-right: 8px;
            }

            .pipeline-text {
                font-size: 13px;
                color: #4285F4;
                font-weight: 500;
            }
        }

        .detail-info {
            padding: 12px 16px;
            background: #fafafa;

            .info-row {
                display: flex;
                flex-wrap: nowrap;
                margin-bottom: 8px;
                align-items: center;

                &:last-child {
                    margin-bottom: 0;
                }

                .info-item {
                    display: flex;
                    align-items: center;
                    flex: 1;
                    margin-right: 12px;
                    white-space: nowrap;
                    overflow: hidden;

                    &:last-child {
                        margin-right: 0;
                    }

                    .info-icon {
                        color: #666;
                        font-size: 14px;
                        margin-right: 6px;
                        flex-shrink: 0;
                    }

                    .info-label {
                        font-size: 12px;
                        color: #666;
                        margin-right: 4px;
                        flex-shrink: 0;
                    }

                    .info-value {
                        font-size: 12px;
                        color: #333;
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }

            .process-measure {
                display: flex;
                align-items: flex-start;
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid #e0e0e0;

                .info-icon {
                    color: #666;
                    font-size: 14px;
                    margin-right: 6px;
                    margin-top: 2px;
                    flex-shrink: 0;
                }

                .info-label {
                    font-size: 12px;
                    color: #666;
                    margin-right: 6px;
                    flex-shrink: 0;
                }

                .measure-text {
                    font-size: 12px;
                    color: #333;
                    line-height: 1.4;
                    flex: 1;
                }
            }
        }

        .image-indicator {
            padding: 8px 16px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-end;

            ion-icon {
                color: #4285F4;
                font-size: 16px;
                margin-right: 4px;
            }

            span {
                font-size: 12px;
                color: #4285F4;
                font-weight: 500;
            }
        }

        // 移动端优化
        @media (max-width: 768px) {
            margin: 8px 12px;

            .card-header {
                padding: 10px 12px 6px;

                .event-type-badge {
                    font-size: 11px;
                    padding: 3px 10px;
                }

                .event-status {
                    font-size: 10px;
                    padding: 2px 6px;
                }
            }

            .event-description {
                padding: 10px 12px 6px;

                .desc-text {
                    font-size: 13px;
                }
            }

            .pipeline-info {
                padding: 0 12px 10px;

                .pipeline-text {
                    font-size: 12px;
                }
            }

            .detail-info {
                padding: 10px 12px;

                .info-row {
                    .info-item {
                        margin-right: 8px;

                        .info-label,
                        .info-value {
                            font-size: 11px;
                        }
                    }
                }

                .process-measure {

                    .info-label,
                    .measure-text {
                        font-size: 11px;
                    }
                }
            }

            .image-indicator {
                padding: 6px 12px 10px;

                span {
                    font-size: 11px;
                }
            }
        }

        // 小屏幕优化 - 保持一行显示
        @media (max-width: 480px) {
            .detail-info {
                .info-row {
                    .info-item {
                        margin-right: 6px;

                        .info-icon {
                            font-size: 12px;
                            margin-right: 4px;
                        }

                        .info-label,
                        .info-value {
                            font-size: 10px;
                        }
                    }
                }
            }
        }
    }

    // 无数据状态优化
    .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 60vh;
        color: #999;
        padding: 20px;

        img {
            width: 60px;
            height: 60px;
            opacity: 0.6;
            margin-bottom: 20px;
        }

        .no-data-span {
            font-size: 16px;
            color: #666;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .no-data-tip {
            font-size: 14px;
            color: #999;
            text-align: center;
            line-height: 1.4;
            margin: 0;
        }
    }
}

// Segment 移动端优化
@media (max-width: 768px) {
    .segment-container {
        padding: 8px 12px 6px;

        ion-segment {
            ion-segment-button {
                font-size: 13px;
                min-height: 32px;
                margin: 0 1px;
            }
        }
    }
}

@media (max-width: 480px) {
    .segment-container {
        padding: 6px 8px 4px;

        ion-segment {
            ion-segment-button {
                font-size: 12px;
                min-height: 30px;
                padding: 0 8px;
            }
        }
    }
}