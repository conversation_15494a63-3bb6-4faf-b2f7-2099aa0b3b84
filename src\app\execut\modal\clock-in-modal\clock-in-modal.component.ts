import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { DetailsMode } from 'src/app/@core/base/environment';

/**
 * 打卡模态框组件 - 管理打卡和未巡检原因上报的tab切换
 */
@Component({
  selector: 'app-clock-in-modal',
  templateUrl: './clock-in-modal.component.html',
  styleUrls: ['./clock-in-modal.component.scss']
})
export class ClockInModalComponent implements OnInit, OnDestroy {
  // 当前关键点
  @Input() currentKeyPoint: any;
  // 任务Code
  @Input() taskCode: string;
  // 初始tab类型
  @Input() initialTab: 'clockIn' | 'reason' = 'clockIn';
  // 数据模式（新增或查看）
  @Input() modelMode: DetailsMode = DetailsMode.ADD;
  // 是否为详情模式
  @Input() isDetailMode: boolean = false;
  // 当前实时坐标
  @Input() currentCoordinate: number[];

  // 当前激活的tab
  activeTab: 'clockIn' | 'reason' = 'clockIn';
  
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();

  constructor(
    private modalCtrl: ModalController,
  ) { }

  ngOnInit() {
    // 根据传入的初始tab类型设置当前激活的tab
    if (this.initialTab) {
      this.activeTab = this.initialTab;
    }
  }

  /**
   * 切换tab
   */
  switchTab(tab: 'clockIn' | 'reason') {
    this.activeTab = tab;
  }

  /**
   * 关闭模态框
   */
  onClose() {
    this.modalCtrl.dismiss();
  }

  /**
   * 处理子组件提交成功事件
   */
  onSubmitSuccess(eventData: { shouldRefreshKeyPoints: boolean }) {
    this.modalCtrl.dismiss({
      result: 'refresh',
      keyPointId: this.currentKeyPoint?.id,
      shouldRefreshKeyPoints: eventData.shouldRefreshKeyPoints
    });
  }

  /**
   * 获取当前tab的标题
   */
  get currentTabTitle(): string {
    const isViewMode = this.modelMode === DetailsMode.SEE;
    if (this.activeTab === 'clockIn') {
      return isViewMode ? '查看打卡数据' : '关键点打卡';
    } else {
      return isViewMode ? '查看未巡检原因' : '未巡检原因上报';
    }
  }

  /**
   * 获取当前tab的工具栏颜色
   */
  get currentToolbarColor(): string {
    return this.activeTab === 'clockIn' ? 'success' : 'warning';
  }

  ngOnDestroy(): void { 
    this.destroy$.next();
    this.destroy$.complete();
  }
} 