import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, ModalController } from '@ionic/angular';
import { Router, NavigationEnd } from '@angular/router';
import { filter, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ShareMethodService } from '../@core/providers/share-method.service';
import { UserInfoService } from '../@core/providers/user-Info.service';
import { NetworkService } from '../@core/providers/network.service';
import { ChangPasswordComponent } from '../self/chang-password/chang-password.component';
import { PageEventService } from './home.event';
import { DataSyncManagerService } from '../@core/providers/data-sync';
import { ToastService } from '../@core/providers/toast.service';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss']
})

export class HomePage implements OnInit, OnDestroy {
  // 下拉刷新事件
  refreshEvent: any;
  // 定时刷新定时器
  private refreshTimer: any;
  // 路由销毁订阅
  private destroy$ = new Subject<void>();
  // 当前是否在首页
  private isOnHomePage = true;
  
  constructor(
    public userInfo: UserInfoService, private alertCtrl: AlertController,
    private modalCtrl: ModalController, private toastService: ToastService,
    private dataEvent: PageEventService, private shareUtils: ShareMethodService,
    private dataSyncManager: DataSyncManagerService,
    private networkService: NetworkService, private router: Router
  ) {
  }
  
  ngOnInit(): void {
    this.checkPassword();
    this.setupRouteListener();
    this.setupTimerControlListener();
    this.startRefreshTimer();
  }

  ngOnDestroy(): void {
    // 清理所有资源
    this.cleanupResources();
  }

  /**
   * 设置路由监听
   */
  private setupRouteListener(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationEnd) => {
        const isHomePage = event.url.includes('/tabs/home');
        
        if (isHomePage && !this.isOnHomePage) {
          // 返回首页，立即刷新一次数据，然后恢复定时器
          console.log('[HomePage] 返回首页，立即刷新数据并恢复定时刷新');
          this.isOnHomePage = true;
          // 立即执行一次数据刷新
          this.dataEvent.sendData('home', true);
          // 启动定时刷新
          this.startRefreshTimer();
        } else if (!isHomePage && this.isOnHomePage) {
          // 离开首页，暂停定时器
          console.log('[HomePage] 离开首页，暂停定时刷新');
          this.isOnHomePage = false;
          this.stopRefreshTimer();
        }
      });
  }

  /**
   * 启动定时刷新
   */
  private startRefreshTimer(): void {
    if (this.networkService.isOnline() && this.isOnHomePage) {
      // 清除可能存在的旧定时器
      this.stopRefreshTimer();
      // 每分钟定时刷新
      this.refreshTimer = setInterval(() => {
        if (this.isOnHomePage) {
          this.dataEvent.sendData('home', true);
        }
      }, 60 * 1000);
      console.log('[HomePage] 定时刷新已启动');
    }
  }

  /**
   * 停止定时刷新
   */
  private stopRefreshTimer(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
      console.log('[HomePage] 定时刷新已暂停');
    }
  }

  /**
   * 清理所有资源
   */
  private cleanupResources(): void {
    // 清理路由监听
    this.destroy$.next();
    this.destroy$.complete();
    
    // 清理定时器
    this.stopRefreshTimer();
  }

  /**
   * 下拉刷新
   */
  doRefresh(event): void {
    this.dataEvent.sendData('home', true);
    this.refreshEvent = event;
    setTimeout(() => {
      this.closeRefresh();
    }, 1000);
  }

  // 关闭刷新
  closeRefresh(): void {
    if (this.refreshEvent) {
      this.refreshEvent.target.complete();
    }
  }

  /**
   * 密码到期提醒
   */
  checkPassword() {
    if (this.userInfo.expireTime) {
      const date = this.shareUtils.daysUntilDeadline(this.userInfo.expireTime);
      if (date <= 5) {
        this.openDialog(date);
      }
    }
  }

  async openDialog(date: number): Promise<void> {
    const msg = date <= 0 ? `您的密码已到期，请及时修改密码` : `您的密码还有${date}天到期，请及时修改密码`;
    const results = await this.alertCtrl.create({
      header: '密码到期提醒!',
      message: msg,
      buttons: [
        {
          text: '取消',
          role: 'cancel',
          cssClass: 'secondary',
        }, {
          text: '修改密码',
          cssClass: 'Okay',
          handler: () => {
            this.changePassword();
          }
        }
      ]
    });
    await results.present();
  }

  /**
   * 修改密码
   */
  async changePassword(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ChangPasswordComponent,
      backdropDismiss: false
    });
    return await modal.present();
  }

  /**
   * 手动上传缓存数据
   */
  async onManualUpload(): Promise<void> {
    try {
      await this.dataSyncManager.uploadAllCachedData();
      this.toastService.presentToast('手动上传已触发，请查看日志', 'success');
    } catch (err) {
      this.toastService.presentToast('手动上传失败', 'danger');
    }
  }

  /**
   * 设置定时器控制监听
   */
  private setupTimerControlListener(): void {
    // 监听定时器暂停消息
    this.dataEvent.receiveDataForPageType('pauseTimer')
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        console.log('[HomePage] 收到暂停定时器消息');
        this.stopRefreshTimer();
      });

    // 监听定时器恢复消息
    this.dataEvent.receiveDataForPageType('resumeTimer')
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        console.log('[HomePage] 收到恢复定时器消息');
        if (this.isOnHomePage) {
          // 立即执行一次数据刷新
          this.dataEvent.sendData('home', true);
          // 启动定时刷新
          this.startRefreshTimer();
        }
      });
  }

}
