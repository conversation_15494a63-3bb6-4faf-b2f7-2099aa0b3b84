# CoreInterceptorService 加密功能说明

## 功能概述

`CoreInterceptorService` 现已实现**参数加密处理统一化**，对所有 HTTP 请求参数进行 AES 加密，确保接口安全。

## 架构设计

### 核心服务
- **CoreInterceptorService**: HTTP 拦截器，负责请求加密和响应解密
- **AesEncryptionService**: AES 加密算法实现
- **EncryptionDebugLoggerService**: 专门的调试日志服务，提供结构化的加密过程日志

## 加密规则（2024年新版）

### 1. 加密算法
- **算法**: AES 加密
- **模式**: ECB (Electronic Codebook)
- **填充**: Pkcs7
- **密钥**: `G7f$2kL9@q3EwP8*`

### 2. 参数加密策略
- **非白名单接口**：
  - 所有请求方式（GET/POST/PUT/DELETE等）参数**只保留加密后的 `cip` 字段**，移除所有明文参数。
  - GET 请求：URL 只保留 `?cip=xxx`，不再有其他 query 参数。
  - POST/PUT/DELETE 请求：body 只保留 `{ cip: xxx }`。
- **白名单接口**：参数明文传递，不做加密处理。

### 3. 特殊字符处理
加密后的数据会进行 URL 编码处理以下特殊字符：

| 字符 | URL编码 | 说明 |
|-----|--------|------|
| `+` | `%2B` | 加号 |
| `/` | `%2F` | 斜杠 |
| `=` | `%3D` | 等号 |
| ` ` | `%20` | 空格 |
| `*` | `%2A` | 星号 |
| `!` | `%21` | 感叹号 |
| `'` | `%27` | 单引号 |
| `(` | `%28` | 左括号 |
| `)` | `%29` | 右括号 |

## 白名单配置

### 添加不需要加密的接口

在 `CoreInterceptorService` 中修改 `encryptionWhitelist` 数组：

```typescript
private encryptionWhitelist = new Set([
  // 添加其他不需要加密的接口...
]);
```

## 加密示例（新版）

### 原始 GET 请求
```
GET /work-goods/api/v2/goods/approve/history/grid?pageNum=1&pageSize=10&depCode=DEP20240416111542HDZUH
```

### 加密后 GET 请求
```
GET /work-goods/api/v2/goods/approve/history/grid?cip=BtStTavQQbG4Xiz3M3vIaiydySyX%2FeJNCBwB4nmvY0fRgMMS%2FQBEfvMzFZ7rajcRMmkm%2BsH0HlcxxBikSmKfcvITNhLw9BbnchRsIVkbqWM%3D
```

### 原始 POST/PUT/DELETE 请求 (Body)
```json
{
  "account": "develop",
  "userPasd": "cpFhWiXjmiG0gQIhwq0GUBVfQOyO6KQ/..."
}
```

### 加密后 POST/PUT/DELETE 请求 (Body)
```json
{
  "cip": "vWnxqxx3%2BbW5dkPXshcTW3wP7eCA2vjSxn1Z2pijIUsXGeVE7txStAD1StHm%2FRWAdMUsLwpmxpOQDNxaf5CqXoHjDqcuRx6F6Znq8iokDWx%2F8PTI3OnXpZWLRv81VlSaNHk6C7ciT7fhbYzuaGWS3LAyRGrAN1c3bdmEaAK16NZBhwrLAV54DwwAoWbgXRbxtmmNm82bHB26YefdqkdUoolIPwLvJn5NQwEGdWj2kw%2Fo3bWnasmhZdsOWazBhxNPEY%2FeZC8%2Bs5eyqdEKqnZG1Q%3D%3D"
}
```

## 工作流程

1. **拦截请求**: HTTP 拦截器捕获所有出站请求
2. **调试日志**: 记录原始请求参数（🚀 [请求原始]）
3. **白名单检查**: 检查请求 URL 是否在白名单中
4. **参数识别**: 识别 URL 参数和 Body 参数
5. **数据加密**: 使用 AES-ECB 算法加密参数数据
6. **特殊字符处理**: 对加密结果进行 URL 编码
7. **请求重构**: 只保留加密后的 `cip` 字段，移除所有明文参数
8. **调试日志**: 记录加密后的请求（🔐 [请求加密]）
9. **发送请求**: 发送处理后的请求
10. **响应处理**: 拦截响应并解密数据
11. **调试日志**: 记录解密后的响应（✅ [响应解密]）

## 兼容性与注意事项

1. **彻底移除明文参数**：拦截器已通过 `params: new HttpParams()` 等方式，确保 GET 请求 URL 只保留 `cip`，不会被 Angular 自动合并原始参数。
2. **业务代码规范**：建议业务代码不要在 URL 后手动拼接参数，所有参数应通过对象传递，由拦截器统一加密。
3. **白名单管理**: 确保将不需要加密的接口添加到白名单中
4. **错误处理**: 加密失败时会返回原始请求，避免阻断业务流程
5. **性能考虑**: 加密操作会增加一定的处理时间
6. **调试日志**:
   - 生产环境请务必关闭调试日志（`debugEnabled: false`）
   - 调试日志会输出敏感数据，仅在开发环境使用
   - 可以通过浏览器开发者工具查看结构化的加密过程日志
7. **服务分离**: 调试日志功能已独立为 `EncryptionDebugLoggerService`，便于维护和测试

## 调试日志功能

### 启用调试日志

在 `environment.dev.ts` 中配置：

```typescript
export const environment = {
  // ... 其他配置
  encryption: {
    debugEnabled: true    // 启用调试日志
  }
};
```

### 日志格式

调试日志采用 JSON 对象格式，包含以下信息：
- **stage**: 处理阶段标识
- **timestamp**: 本地化时间戳（精确到毫秒）
- **url**: 请求路径（不含参数）
- **data**: 格式化的数据内容

### 日志阶段标识

| 阶段 | 图标标识 | 说明 | 数据内容 |
|-----|---------|------|---------|
| REQ-原始 | 🚀 [请求原始] | 加密前的原始请求数据 | URL参数、Body参数、参数数量 |
| REQ-加密 | 🔐 [请求加密] | 加密后的请求数据 | 加密后的URL、Body、加密状态信息 |
| RES-解密 | ✅ [响应解密] | 解密后的响应数据 | 完整响应数据 + 数据摘要信息 |

### 日志示例

#### 🚀 请求原始数据
```json
🚀 [请求原始] {
  "stage": "REQ-原始",
  "timestamp": "2025/7/3 18:18:20.130",
  "url": "/work-user/api/v2/userCenter/mail/receive/grid",
  "data": {
    "urlParams": {"pageSize": 10, "pageNum": 1},
    "bodyParams": {"status": "active"},
    "paramCount": 3
  }
}
```

#### 🔐 请求加密后
```json
🔐 [请求加密] {
  "stage": "REQ-加密",
  "timestamp": "2025/7/3 18:18:20.140",
  "url": "/work-user/api/v2/userCenter/mail/receive/grid",
  "data": {
    "url": "/work-user/api/v2/userCenter/mail/receive/grid?cip=encrypted_params",
    "body": {"cip": "encrypted_body_data"},
    "hasEncryptedUrl": true,
    "hasEncryptedBody": true,
    "encryptedBodyLength": 156
  }
}
```

#### ✅ 响应解密后
```json
✅ [响应解密] {
  "stage": "RES-解密",
  "timestamp": "2025/7/3 18:18:20.161",
  "url": "/work-user/api/v2/userCenter/mail/receive/grid",
  "data": {
    "code": 0,
    "msg": "success",
    "data": {
      "records": [...],
      "total": 1,
      "size": 10
    },
    "dataSummary": {
      "type": "object",
      "keys": ["records", "total", "size", "current", "pages"]
    }
  }
}
```

### EncryptionDebugLoggerService API

```typescript
// 记录原始请求
logOriginalRequest(url: string, urlParams?: any, bodyParams?: any): void

// 记录加密请求
logEncryptedRequest(url: string, encryptedUrl?: string, encryptedBody?: any): void

// 记录解密响应
logDecryptedResponse(url: string, responseData: any): void

// 通用调试日志
logEncryptionDebug(stage: string, url: string, data?: any): void
```

## ECB 模式特点

- **无需 IV**: ECB 模式不需要初始化向量，加密结果更简洁
- **确定性**: 相同的明文总是产生相同的密文
- **独立性**: 每个数据块独立加密，不依赖其他块
- **适用场景**: 适合加密短数据或不需要高安全性的场景

