import { Injectable } from '@angular/core';
import { Storage } from '@ionic/storage-angular';
import { from, Observable } from 'rxjs';
import { flatMap } from 'rxjs/operators';
@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private selfStorage: Storage | null = null;

  constructor(private storage: Storage) {
  }
  private async proxyStorage(): Promise<Storage> {
    if (this.selfStorage) {
      return Promise.resolve(this.selfStorage);
    } else {
      this.selfStorage = await this.storage.create();
      return Promise.resolve(this.selfStorage);
    }
  }

  public set(key: string, value: any): Observable<any> {
    return from(this.proxyStorage()).pipe(flatMap((s) => from(s.set(key, value))));
  }

  public get(key: string): Observable<any> {
    return from(this.proxyStorage()).pipe(flatMap((s) => from(s.get(key))));
  }

  public remove(key: string): Observable<any> {
    return from(this.selfStorage?.remove(key));
  }

  public clear(): Observable<any> {
    return from(this.selfStorage?.clear());
  }

  public length(): Observable<any> {
    return from(this.selfStorage?.length());
  }
}
