:host {
  display: block; // 确保宿主元素是块级元素，以便应用宽度和高度
  width: 100%;
  height: 100%;
}

.camera-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  
  // 全屏模式下的样式，作用于组件的宿主元素
  &.fullscreen {
    width: 100vw;
    height: 100vh;
    // 确保内容填充整个屏幕
    .video-section {
      width: 100vw;
      height: 100vh;
    }
  }

  .controls-top-right {
    position: absolute;
    top: 4px;
    right: 6px;
    z-index: 20; // 确保位于所有内容之上，包括错误容器和加载指示器
    display: flex;
    gap: 5px; // 按钮之间的间距

    ion-button {
      --padding-start: 8px;
      --padding-end: 8px;
      --min-width: 36px;
      --min-height: 36px;
      border-radius: 50%; // 圆形按钮
      background-color: rgba(0, 0, 0, 0.4); // 半透明背景
      ion-icon {
        font-size: 20px;
      }
    }
  }

  .video-section {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .video-player {
      width: 100%;
      height: 100%;
      object-fit: contain;
      &::-webkit-media-controls {
        display: none !important;
      }
      &::-webkit-media-controls-enclosure {
        display: none !important;
      }
      &::-webkit-media-controls-panel {
        display: none !important;
      }
    }

    .overlay-canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }

    .overlay-canvas.show-canvas {
      display: block;
    }

    .error-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 10;

      .error-message {
        color: #fff;
        font-size: 16px;
        text-align: center;
        margin-bottom: 10px;
        padding: 0 20px;
        line-height: 1.4;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        ion-icon {
          font-size: 48px;
          color: #ff6b6b;
        }

        span {
          max-width: 300px;
          word-wrap: break-word;
        }
      }

      .error-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;

        .retry-button {
          --color: #fff;
          --background: rgba(255, 255, 255, 0.15);
          --border-radius: 25px;
          --padding-start: 24px;
          --padding-end: 24px;
          --min-height: 44px;
          --border: 1px solid rgba(255, 255, 255, 0.3);
          transition: all 0.3s ease;
          
          &:hover {
            --background: rgba(255, 255, 255, 0.25);
            --border-color: rgba(255, 255, 255, 0.5);
          }
          
          ion-icon {
            margin-right: 8px;
            font-size: 18px;
          }
        }
      }
    }
  }
}

.loading-spinner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7); /* 半透明背景 */
  color: white;
  z-index: 10; /* 确保它在视频上方 */

  ion-spinner {
    width: 50px;
    height: 28px;
    margin-bottom: 10px;
  }
  p {
    margin: 0;
  }
}