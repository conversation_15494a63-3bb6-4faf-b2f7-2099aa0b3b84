.location-provider-modal {
  --background: #f8f9fa;

  .provider-options {
    padding: 16px;
  }

  .option-header {
    text-align: center;

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  ion-list {
    background: transparent;
  }

  ion-item {
    --background: #ffffff;
    --border-radius: 12px;
    --padding-start: 16px;
    --padding-end: 16px;
    --inner-padding-end: 0;
    margin-bottom: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &.selected {
      --background: #f0f7ff;
      border-color: var(--ion-color-primary);
      box-shadow: 0 2px 8px rgba(var(--ion-color-primary-rgb), 0.2);
    }

    ion-icon[slot="start"] {
      font-size: 24px;
      margin-right: 16px;
    }

    ion-label {
      h3 {
        font-weight: 600;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &.text-primary {
          color: var(--ion-color-primary);
        }

        .selected-icon {
          font-size: 20px;
        }
      }

      p {
        font-size: 13px;
        color: var(--ion-color-medium);
        margin: 0;
        line-height: 1.4;
      }
    }
  }

  .recommendation-note {
    ion-card {
      margin: 0;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      ion-card-content {
        padding: 16px;

        .note-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          ion-icon {
            font-size: 20px;
            margin-right: 8px;
          }

          strong {
            font-size: 16px;
          }
        }

        p {
          margin: 4px 0;
          font-size: 13px;
          line-height: 1.5;

          strong {
            color: var(--ion-color-dark);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .location-provider-modal {
    .provider-options {
      padding: 12px;
    }

    ion-item {
      --padding-start: 12px;
      --padding-end: 12px;

      ion-icon[slot="start"] {
        font-size: 22px;
        margin-right: 12px;
      }

      ion-label {
        h3 {
          font-size: 15px;
        }

        p {
          font-size: 12px;
        }
      }
    }
  }
}
