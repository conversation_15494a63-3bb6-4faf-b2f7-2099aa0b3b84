# Quick Option Select 组件

`ost-quick-option-select` 是一个智能的选择组件，它会根据选项数量自动切换显示模式。当选项数量小于等于3个时，直接显示单选按钮组；当选项数量大于3个时，使用弹窗列表模式。

## 功能特点

1. 自适应显示模式
   - 选项 ≤ 3：单选按钮组（支持横向和竖向两种布局）
   - 选项 > 3：使用 `ost-input-search` 组件的弹窗列表模式

2. 布局模式
   - 横向布局（默认）：适合选项文本较短，需要节省垂直空间的场景
   - 竖向布局：适合选项文本较长或需要清晰层级展示的场景

3. 数据加载与处理
   - 通过 `interfaceUrl` 动态加载数据
   - 优化的数据处理策略：
     - 选项 ≤ 3：在组件内部进行数据转换，以支持快速选择模式
     - 选项 > 3：不进行转换，直接将原始数据传递给 `ost-option-source` 组件处理
   - 支持数据格式转换（通过 `labelName` 和 `labelValue` 配置）
   - 支持联动加载（通过 `previousKey` 配置）

4. 表单集成
   - 实现 `ControlValueAccessor` 接口
   - 支持 `formControlName` 绑定
   - 支持禁用、只读、必填等状态

## 使用方式

### 1. 基本使用（推荐）

```html
<ost-quick-option-select
  formControlName="fieldName"
  [labelValue]="'value'"
  [labelName]="'name'"
  interfaceUrl="/api/options"
></ost-quick-option-select>
```

### 2. 完整配置

```html
<ost-quick-option-select
  formControlName="fieldName"
  [labelValue]="'value'"
  [labelName]="'name'"
  [previousKey]="{name: 'prevField', value: prevValue}"
  interfaceUrl="/api/options"
  [placeholder]="'请选择'"
  [disabled]="false"
  [readonly]="false"
  [required]="true"
  [icon]="'search-outline'"
  (selectedItemChange)="onSelectedItemChange($event)"
></ost-quick-option-select>
```

```typescript
onSelectedItemChange(item: any) {
  console.log('Selected item:', item);
  // item 格式为: {name: '选项名称', value: '选项值', ...其他属性}
}
```

### 3. 动态表单配置

在动态表单中使用时，可以通过以下配置对象来定义：

```typescript
{
  type: 'quick-option-select',
  name: 'fieldName',           // 表单控件名称
  label: '选项名称',           // 显示标签
  placeholder: '请选择',       // 占位文本
  interfaceUrl: '/api/url',    // 数据接口地址
  labelName: 'name',           // 选项显示字段
  labelValue: 'value',         // 选项值字段
  previousKey: 'parentField',  // 联动字段名（可选）
  disabled: false,             // 是否禁用（可选）
  readonly: false,             // 是否只读（可选）
  required: true,              // 是否必填（可选）
  icon: 'search-outline'       // 图标（可选）
}
```

### 4. 非表单场景使用（可选）

```html
<ost-quick-option-select
  [radioValue]="currentValue"
  [labelValue]="'value'"
  [labelName]="'name'"
  interfaceUrl="/api/options"
  (ngModelChange)="onValueChange($event)"
></ost-quick-option-select>
```

## 输入属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| labelName | string | - | 选项显示字段名 |
| labelValue | string | - | 选项值字段名 |
| interfaceUrl | string | - | 数据接口地址 |
| previousKey | object | - | 联动字段配置 |
| placeholder | string | '' | 输入框占位文本 |
| disabled | boolean | false | 是否禁用 |
| readonly | boolean | false | 是否只读 |
| required | boolean | false | 是否必填 |
| icon | string | 'search-outline' | 图标名称 |
| direction | 'horizontal' \| 'vertical' | 'horizontal' | 快速选择模式的排列方向 |
| radioValue | string | - | 可选属性，仅用于非表单控件场景下的值绑定 |

## 输出事件

| 事件名 | 参数类型 | 说明 |
|--------|----------|------|
| ngModelChange | any | 值变化时触发（非表单场景） |
| selectedItemChange | any | 选中项变化时触发，返回当前项的所有属性对象 |

## 值绑定与回显

组件支持两种方式的值绑定：

### 1. 表单控件绑定（推荐）
```html
<form [formGroup]="form">
  <ost-quick-option-select formControlName="field"></ost-quick-option-select>
</form>
```

在表单控件场景下，组件会自动处理值的绑定和回显，不需要使用 `radioValue` 属性。

### 2. 非表单控件绑定（可选）
```html
<ost-quick-option-select 
  [radioValue]="value"
  (ngModelChange)="value = $event">
</ost-quick-option-select>
```

`radioValue` 属性仅用于非表单控件场景，当你不使用 `formControlName` 或 `ngModel` 时，可以通过这个属性来实现值的绑定和回显。

### 值回显机制

1. 表单控件场景（推荐）
   - 通过 `formControlName` 自动处理值的绑定和回显
   - 组件内部实现了 `ControlValueAccessor` 接口
   - 使用 `writeValue` 方法接收表单值
   - 通过 `onChange` 方法通知值变化

2. 非表单控件场景（可选）
   - 通过 `radioValue` 输入属性设置值
   - 通过 `ngModelChange` 事件获取值变化
   - 适用于简单的值绑定场景

### 数据联动

通过 `previousKey` 配置实现联动：

```html
<ost-quick-option-select
  [previousKey]="{
    name: 'parentField',
    value: parentForm.get('parentField').value
  }"
></ost-quick-option-select>
```

当父级字段值变化时，组件会重新加载数据。

## 性能优化

为了提高组件性能，特别是在处理大量数据时，组件实现了智能的数据处理策略：

1. 当选项数量小于等于3个时：
   - 在组件内部通过 `transformation` 方法进行数据转换
   - 转换后的数据格式为统一的 `{value, name, ...rest}` 结构
   - 这种格式更适合快速选择模式下的直接展示

2. 当选项数量大于3个时：
   - 不在组件内部进行数据转换，避免重复处理
   - 直接将原始数据传递给 `ost-option-source` 组件
   - 由 `ost-option-source` 组件负责数据的转换和展示
   - 这种方式减少了数据处理的冗余，提高了性能

## 注意事项

1. 组件需要在 `ReactiveFormsModule` 环境中使用
2. 在表单控件场景下，只使用 `formControlName`，不要同时使用 `radioValue`
3. `radioValue` 属性仅用于非表单控件场景
4. 联动功能需要正确配置 `previousKey`
5. 数据接口需要返回正确的格式：
   ```typescript
   {
     code: 0,
     data: {
       records?: any[],  // 或直接返回数组
       dictValueList?: any[]  // 字典数据
     }
   }
   ``` 