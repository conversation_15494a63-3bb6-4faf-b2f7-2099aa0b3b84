// 横向排列样式
.quick-radio-group {
  display: flex;
  flex-direction: row;
  gap: 12px;
  padding: 0px;
  padding-left: 6px;

  ion-item-divider {
    --padding-start: 0px;
    margin: 0;
    min-height: 36px;
    border: 0px;
    width: auto;

    ion-label {
      margin: auto;
    }
    
    ion-radio {
      margin-right: 4px;
      width: 16px;
      height: 16px;
    }
  }
}

// 竖向排列样式
.quick-radio-group-vertical {
  display: flex;
  flex-direction: column;
  gap: 0px;
  padding: 0;
  margin-left: 12px;

  ion-item-divider {
    --padding-start: 0px;
    margin: 0;
    min-height: 36px;
    border: 0px;
    width: 100%;

    ion-label {
      margin: 0;
      padding: 8px 0;
    }
    
    ion-radio {
      margin-right: 8px;
      width: 16px;
      height: 16px;
    }
  }
}

.no-pointer-events {
  pointer-events: none;
}