# 巡检任务分类展示UI设计方案

## 1. 设计目标

### 核心价值
- **待办提醒**：一目了然地展示用户需要完成的巡检任务
- **分类展示**：同时显示巡视（每日）和巡查（每月）任务
- **状态感知**：直观显示任务的紧急程度和完成状态

### 业务特点
- **巡视任务**：每天更新，周期性重复，时效性强
- **巡查任务**：每月执行，持续性任务，长期跟踪

## 2. UI布局设计

### 2.1 整体结构（分区块展示）
```
┌─────────────────────────────────────┐
│           巡检任务待办               │
├─────────────────────────────────────┤
│ 🚗 今日巡视任务 (3/5)    [查看全部] │
├─────────────────────────────────────┤
│  ┌─ 任务卡片1 ─┐ ┌─ 任务卡片2 ─┐   │
│  │   紧急任务   │ │   重要任务   │   │
│  └─────────────┘ └─────────────┘   │
├─────────────────────────────────────┤
│ 👤 本月巡查任务 (8/12)   [查看全部] │
├─────────────────────────────────────┤
│  ┌─ 任务卡片1 ─┐ ┌─ 任务卡片2 ─┐   │
│  │   进行中     │ │   待开始     │   │
│  └─────────────┘ └─────────────┘   │
└─────────────────────────────────────┘
```

### 2.2 分区块设计

#### 巡视任务区块
- **标题**：🚗 今日巡视任务 (完成数/总数)
- **位置**：页面上半部分
- **颜色主题**：绿色系
- **显示数量**：最多显示2-3个最紧急的任务
- **操作**：右上角"查看全部"链接

#### 巡查任务区块
- **标题**：👤 本月巡查任务 (完成数/总数)
- **位置**：页面下半部分
- **颜色主题**：橙色系
- **显示数量**：最多显示2-3个进行中的任务
- **操作**：右上角"查看全部"链接

## 3. 任务卡片设计

### 3.1 巡视任务卡片（紧凑型）
```
┌─────────────────────────────────────┐
│ ⚡ 设备巡检A线                      │
│ 📍 主变压器区域  ⏰ 08:00-10:00     │
│ 📊 2/5 (40%)           [开始任务]   │
└─────────────────────────────────────┘
```

### 3.2 巡查任务卡片（紧凑型）
```
┌─────────────────────────────────────┐
│ 📅 月度安全检查                     │
│ 📍 全厂区域    ⏳ 本月1-15日       │
│ 📊 8/12 (67%)          [继续任务]   │
└─────────────────────────────────────┘
```

### 3.3 卡片状态标识

#### 优先级标识
- **紧急**：红色 ⚡ 图标
- **重要**：橙色 ⚠️ 图标
- **普通**：无特殊标识

#### 时间状态
- **今日**：绿色背景
- **逾期**：红色背景
- **本月**：蓝色背景
- **即将到期**：黄色背景

## 4. 交互设计

### 4.1 页面交互
- **同时展示**：巡视和巡查任务同时显示在一个页面
- **区块滚动**：页面支持整体滚动查看所有任务
- **数据加载**：页面加载时显示骨架屏

### 4.2 任务操作
- **开始任务**：点击按钮跳转到具体执行页面
- **继续任务**：恢复之前的执行进度
- **查看详情**：点击卡片查看任务详细信息
- **查看全部**：点击区块标题右侧链接查看该类型所有任务

### 4.3 刷新机制
- **下拉刷新**：支持下拉刷新获取最新任务状态
- **自动刷新**：定时更新任务进度和状态
- **实时更新**：任务状态变化时实时反映在界面上

## 5. 数据展示策略

### 5.1 巡视任务区块
- **数量限制**：最多显示2-3个最紧急的任务
- **排序规则**：
  1. 逾期任务优先
  2. 按计划开始时间排序
  3. 按优先级排序
- **空状态**：无任务时显示"今日无巡视任务"提示

### 5.2 巡查任务区块
- **数量限制**：最多显示2-3个进行中的任务
- **排序规则**：
  1. 即将到期的任务优先
  2. 按完成进度排序（低完成度优先）
- **空状态**：无任务时显示"本月无巡查任务"提示

### 5.3 任务优先级策略
- **巡视任务**：优先显示当日必须完成的任务
- **巡查任务**：优先显示进度落后或即将到期的任务
- **整体平衡**：确保两个区块都有内容展示，避免某个区块过空

## 6. 响应式设计

### 6.1 移动端适配
- **区块布局**：上下垂直排列，巡视任务在上，巡查任务在下
- **任务卡片**：单列布局，每行一个卡片
- **紧凑显示**：减少卡片间距，优化空间利用

### 6.2 平板端适配
- **任务卡片**：每个区块内可采用双列布局
- **区块间距**：增加区块间的视觉分隔
- **横屏模式**：考虑左右分栏显示两种任务类型

## 7. 状态管理

### 7.1 数据状态
- **加载中**：显示骨架屏或loading动画
- **空状态**：显示友好的空状态提示
- **错误状态**：显示重试按钮

### 7.2 实时更新
- 定时刷新任务状态（每5分钟）
- 任务完成后实时更新进度
- 新任务分配时推送通知

## 8. 用户体验优化

### 8.1 视觉层次
- 使用不同的卡片高度区分重要性
- 紧急任务使用更醒目的颜色和动画
- 完成的任务使用灰色调降低视觉权重

### 8.2 操作便捷性
- 一键开始最紧急的任务
- 快速查看任务详情
- 支持任务状态快速切换

### 8.3 信息密度
- 在有限空间内展示关键信息
- 使用图标减少文字占用空间
- 支持展开/收起查看更多详情

## 9. 技术实现要点

### 9.1 组件结构
```typescript
// 主要组件
- BacklogComponent (主容器)
  - TaskSectionComponent (任务区块)
    - SectionHeaderComponent (区块标题)
    - TaskCardComponent (任务卡片)
  - EmptyStateComponent (空状态)
  - LoadingStateComponent (加载状态)
```

### 9.2 数据模型
```typescript
interface Task {
  id: string;
  type: 'patrol' | 'inspection'; // 巡视 | 巡查
  name: string;
  priority: 'urgent' | 'important' | 'normal';
  status: 'pending' | 'in-progress' | 'completed';
  progress: number; // 完成百分比
  scheduledTime?: string; // 计划时间
  deadline?: string; // 截止时间
  area: string; // 区域
}
```

### 9.3 状态管理
```typescript
// 状态接口
interface BacklogState {
  patrolTasks: Task[];      // 巡视任务
  inspectionTasks: Task[];  // 巡查任务
  loading: boolean;
  error: string | null;
  lastUpdated: Date;
}

// 服务管理
- 使用RxJS管理任务数据流
- 实现任务状态的响应式更新
- 分别缓存两种类型任务数据
- 定时刷新机制
```

## 10. 后续优化方向

1. **智能推荐**：根据历史数据推荐最优执行顺序
2. **语音提醒**：集成语音播报功能
3. **地图集成**：显示任务位置和最优路径
4. **团队协作**：支持任务分配和协作功能
5. **数据分析**：提供任务完成情况的统计分析

---

*本设计方案基于用户需求和业务特点制定，可根据实际使用反馈进行迭代优化。*