<ion-content>
  <ion-header
    class="sticky"
    [translucent]="true"
  >
    <ion-toolbar color="primary">
      <ion-buttons
        slot="start"
        class="title-start-back"
      >
        <ion-button (click)="onCloseDialo()">
          <ion-icon name="chevron-back-outline"></ion-icon>
        </ion-button>
      </ion-buttons>
      <ion-title>GIS配置</ion-title>
    </ion-toolbar>
  </ion-header>
  <form
    [formGroup]="infoFormGroup"
    (ngSubmit)="onChangeConfig()"
  >
    <ost-form-list [formGroup]="infoFormGroup">
      <!-- 期望准确度 -->
      <ost-form-item>
        <ion-label>期望精度:</ion-label>
        <ion-input formControlName="desiredAccuracy"></ion-input>
      </ost-form-item>
      <!-- 静止半径 -->
      <ost-form-item>
        <ion-label>静止半径:</ion-label>
        <ion-input formControlName="stationaryRadius"></ion-input>
      </ost-form-item>
      <!-- 最小间距 -->
      <ost-form-item>
        <ion-label>最小间距:</ion-label>
        <ion-input formControlName="distanceFilter"></ion-input>
      </ost-form-item>
      <!-- 坐标同步数 -->
      <ost-form-item>
        <ion-label>坐标同步数:</ion-label>
        <ion-input
          readonly
          formControlName="syncThreshold"
        ></ion-input>
      </ost-form-item>
      <!-- 位置更新之间的最小时间间隔（毫秒） -->
      <ost-form-item>
        <ion-label>更新间隔:</ion-label>
        <ion-input
          readonly
          formControlName="interval"
        ></ion-input>
      </ost-form-item>
      <!-- 限制存储到db中的最大位置数 -->
      <ost-form-item>
        <ion-label>存储点数:</ion-label>
        <ion-input
          formControlName="maxLocations"
          [type]=""
        ></ion-input>
      </ost-form-item>
    </ost-form-list>
    <ion-button
      type="submit"
      color="primary"
      expand="block"
      [disabled]="!infoFormGroup.valid"
    >
      保存配置
    </ion-button>
  </form>
</ion-content>