<ion-content>
  <!-- 下拉刷新 -->
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ng-container *ngIf="logList.length > 0 ;else noData">
    <div *ngFor="let item of logList" class="task-item">
      <ion-item lines="none">
        <!-- 巡线任务 -->
        <ion-label>
          <p class="task-p"></p>
          事件类型 : {{item.eventType}}
        </ion-label>
        <!-- 删除按钮 -->
        <ion-note *ngIf="modelMode !== DetailsMode.SEE" slot="end" (click)="onDelete(item)">
          <ion-icon style="font-size: 17px;color: red;" name="trash-outline"></ion-icon>
        </ion-note>
      </ion-item>
      <ion-grid (click)="showDetail(item)">
        <ion-row>
          <ion-col size="8">
            <p>是否完成 ：{{item.status}}</p>
            <p>填写人 ：{{item.userName}}</p>
            <p>描述 ：{{item.describe}}</p>
          </ion-col>
          <ion-col size="4" *ngIf="item.fileCodes">
            <img [src]="getFileUrl(item.fileCodes[0].url)" alt="Item Image" 
              style="width: 100%; height: 60px;object-fit: contain;">
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  </ng-container>
  <!-- 添加 -->
  <ion-fab class="ionFab" *ngIf="modelMode !== DetailsMode.SEE"
    horizontal="end" vertical="bottom" slot="fixed">
    <ion-fab-button (click)="onAddLogs()">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>

<!-- 无数据 -->
<ng-template #noData>
  <div class="no-data">
    <img src="assets/menu/box2.png" style="padding-top: 50px;" />
    <!-- 暂无数据 -->
    <span class="no-data-span">暂无数据</span>
  </div>
</ng-template>