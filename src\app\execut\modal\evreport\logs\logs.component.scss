ion-content {
    height: 100%;

    background-color: #f6f6f6;
    --background: #f6f6f6;

    .task-item {
        margin: 0 0 12px 0;
        color: #000;
        font-size: 12px;
        border-top: 1px solid #f6f6f6;
        border-bottom: 1px solid #f6f6f6;

        ion-label {
            font-size: 14px;

            .task-p {
                width: 6px;
                height: 20px;
                background-color: var(--ion-color-secondary);
                float: left;
                border-radius: 16px;
                margin: 0 10px 0 0;
            }
        }

        ion-note {
            margin-left: 0;
            padding-left: 0;
        }

        ion-grid {
            padding: 6px 16px;
            background-color: #fff;
            border-top: 1px solid #f6f6f6;
            ion-col{
                padding: 0;
                p{
                    margin: 6px 0;
                }
            }
        }
    }

}

.ionFab {
    margin-bottom: 60px;
}