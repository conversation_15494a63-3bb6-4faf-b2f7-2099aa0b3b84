import { Component, Input, OnInit } from '@angular/core';
import { PopoverController } from '@ionic/angular';
import { Collection } from 'ol';
import BaseLayer from 'ol/layer/Base';
import LayerGroup from 'ol/layer/Group';
import { CoordinateSystemService } from '../../../service/coordinate-system.service';
@Component({
  selector: 'ost-map-switch',
  templateUrl: './ost-map-switch.component.html',
  styleUrls: ['./ost-map-switch.component.scss']
})
export class MapSwitchComponent implements OnInit {
  // 创建地图
  @Input() baseLayer: LayerGroup;
  // 基础图层集合
  @Input() baseLayerList: Collection<LayerGroup> = new Collection();
  // 创建业务图层
  @Input() businessLayer!: LayerGroup;
  // 业务图层集合
  @Input() businessLayerList: Collection<BaseLayer> = new Collection();

  constructor(
    public popoverController: PopoverController,
    private coordinateSystemService: CoordinateSystemService
  ) { }

  ngOnInit(): void {
    // 初始化透明度
    this.businessLayerList.forEach(lyaer => {
      if (!lyaer.get('opacity')) {
        lyaer.set('opacity', 1);
      }
    });
  }


  /**
   * 图层切换功能（增强版，支持坐标系切换）
   */
  onMapServe(item: LayerGroup): void {
    const newBaseMapId = item.get('id');
    const previousBaseMapId = this.getCurrentSelectedBaseMapId();

    // 1. 切换底图
    this.baseLayerList.forEach(layer => { layer.set('selected', false); });
    item.set('selected', true);
    this.baseLayer.setLayers(item.getLayers());

    // 2. 检查是否需要切换坐标系
    if (newBaseMapId !== previousBaseMapId) {
      console.log(`🗺️ 底图切换: ${previousBaseMapId} → ${newBaseMapId}`);

      // 3. 通知坐标系服务切换坐标系
      this.coordinateSystemService.switchBaseMap(newBaseMapId);
    }

    this.popoverController.dismiss();
  }

  /**
   * 获取当前选中的底图ID
   */
  private getCurrentSelectedBaseMapId(): string {
    const selectedLayer = this.baseLayerList.getArray().find(layer => layer.get('selected'));
    return selectedLayer?.get('id') || '';
  }

  /**
   * 业务图层选择功能
   * @param value 复选框值 true/false
   * @param item 当前操作图层
   */
  onChangeBusiness(value: boolean, item: BaseLayer): void {
    const newLayer = new Collection<BaseLayer>();
    this.businessLayerList.getArray().map((layer) => {
      if (item.get('id') === layer.get('id')) {
        layer.set('selected', value);
      }
      if (layer.get('selected')) {
        newLayer.push(layer);
      }
    });
    this.businessLayer.setLayers(newLayer);
  }
  /**
   * 管线透明度
   */
  opacityChange($event: number, item: BaseLayer): void {

    this.businessLayerList.getArray().map((layer) => {
      if (item.get('id') === layer.get('id')) {
        layer.set('opacity', $event / 100);
      }
    });
    this.businessLayer.setLayers(this.businessLayerList);
  }
  /**
   * 关闭窗口
   */
  onClose(): void {
    this.popoverController.dismiss();
  }

}
