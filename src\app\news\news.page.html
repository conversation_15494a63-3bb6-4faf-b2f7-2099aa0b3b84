<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start"></ion-buttons>
    <ion-title> 消息中心 </ion-title>
    <ion-note slot="end"></ion-note>
  </ion-toolbar>
</ion-header>
<ion-content class="body">
  <!-- 下拉刷新 -->
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <!-- 列表 -->
  <ng-container *ngIf="newsGridData.length > 0 || isLoading;else noData">

    <div class="notification-card" *ngFor="let item of newsGridData; let i= index"
      [ngClass]="{'read': item.readStatus === '已读'}">
      <div class="notification-header">
        <ion-icon name="notifications-outline" class="notification-icon"></ion-icon>
        <div class="notification-title">{{item.msgTitle}}</div>
      </div>
      <ion-item lines="none" detail (click)="onNewsDetail(item)">
        <ion-label>查看详情</ion-label>
        <div class="notification-time">{{item.sendTime}}</div>
      </ion-item>
    </div>

  </ng-container>

  <!-- 上拉加载更多 -->
  <ion-infinite-scroll
    threshold="40px" class="infinite-scroll"
    *ngIf="newsGridData.length > 0;"
    (ionInfinite)="loadMoreData($event)"
  >
    <ion-infinite-scroll-content
      style="margin-top: 16px;"
      [loadingSpinner]="spinnerType"
      [loadingText]="isShowNoMoreStr"
    >
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>


<!-- 暂无数据 -->
<ng-template #noData>
  <div class="no-data">
    <img src="assets/menu/box2.png" style="padding-top: 50px" />
    <span class="no-data-span">暂无数据</span>
  </div>
</ng-template>