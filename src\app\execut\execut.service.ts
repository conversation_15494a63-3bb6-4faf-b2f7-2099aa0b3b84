import { Injectable } from "@angular/core";
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams, ResourceRequestMethod, ResourceResponseBodyType } from "@ngx-resource/core";
import { RequestResult } from "../@core/base/request-result";
import { EvReportInfo, LogInfo } from "./class/evreport";
import { TaskInfo } from "./class/task.bean";
import { KeyPointInfo } from "./class/key-point";


@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root',
})
export class ExecutService extends Resource {

  constructor() { super(); }

  /**
   * 轨迹上传
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/app/trajectory/upload',
  })
  uploadTrack!: IResourceMethodObservable<TaskInfo[], RequestResult<any>>;

  /**
   * 开始任务
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/app/start/task',
  })
  startTasck!: IResourceMethodObservable<TaskInfo, RequestResult<any>>;

  /**
   * 继续任务
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/app/continue/task',
  })
  continueTasck!: IResourceMethodObservable<{ taskCode: string }, RequestResult<any>>;

  /**
   * 巡检任务结束
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/app/stop/task',
  })
  endTasck!: IResourceMethodObservable<{ taskCode: string }, RequestResult<any>>;

  /**
   * 新增关键点
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/app/point/msg',
  })
  addKeyPoint!: IResourceMethodObservable<KeyPointInfo, RequestResult<any>>;

  /**
   * 关键点修改
   */
  @ResourceAction({
    method: ResourceRequestMethod.Put,
    path: '/work-inspect/api/v2/inspect/app/point/msg',
  })
  updateKeyPoint!: IResourceMethodObservable<KeyPointInfo, RequestResult<any>>;

  /****************************************************事件上报********************************************************/

  /**
   * 事件上报添加
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/batch/event/msg',
  })
  saveEReport!: IResourceMethodObservable<any, RequestResult<any>>;

  /**
   * 事件上报查询
   */
  @ResourceAction({
    method: ResourceRequestMethod.Get,
    path: '/work-inspect/api/v2/inspect/event/msg/byCode',
  })
  getEReportByCode!: IResourceMethodObservable<{ eventCode: string }, RequestResult<EvReportInfo>>;

  /**
   * 事件上报修改
   */
  @ResourceAction({
    method: ResourceRequestMethod.Put,
    path: '/work-inspect/api/v2/inspect/app/event/msg',
  })
  updateEReport!: IResourceMethodObservable<EvReportInfo, RequestResult<any>>;

  /**
   * 事件上报删除
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/event/msg',
    method: ResourceRequestMethod.Delete,
    responseBodyType: ResourceResponseBodyType.Text,
  })
  deleteEReport!: IResourceMethodObservable<string[], RequestResult<boolean>>;

  /*******************************************************节点信息*****************************************************/

  /**
   * 根据eventCode查询节点列表
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/schedule/msg/list',
  })
  getLogList!: IResourceMethodObservable<{ eventCode: string }, RequestResult<LogInfo[]>>;

  /**
   * 根据scheduleCode查询节点详情
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/schedule/msg/byCode',
  })
  getLogDetail!: IResourceMethodObservable<{ scheduleCode: string }, RequestResult<LogInfo>>;

  /**
   * 节点添加
   */
  @ResourceAction({
    method: ResourceRequestMethod.Post,
    path: '/work-inspect/api/v2/inspect/app/schedule/msg',
  })
  saveLogs!: IResourceMethodObservable<LogInfo, RequestResult<boolean>>;

  /**
   * 节点修改
   */
  @ResourceAction({
    method: ResourceRequestMethod.Put,
    path: '/work-inspect/api/v2/inspect/app/schedule/msg',
  })
  updateLogs!: IResourceMethodObservable<LogInfo, RequestResult<boolean>>;

  /**
   * 删除
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/schedule/msg',
    method: ResourceRequestMethod.Delete,
    responseBodyType: ResourceResponseBodyType.Text,
  })
  deleteLog!: IResourceMethodObservable<string[], RequestResult<boolean>>;

}