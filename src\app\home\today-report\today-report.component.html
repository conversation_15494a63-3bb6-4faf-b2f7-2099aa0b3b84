<ion-card-content class="reportBody">
  <div class="title">
    <span>今日事件上报统计</span>
  </div>

  <div class="tabs">
    <ion-grid>
      <ion-row class="ion-align-items-center">
        <ion-col size="4" *ngFor="let item of problemList">
          <div class="div-box" (click)="onProblemClick(item.name)">
            <div class="p-img" [style.color]="'#'+item.color">
              {{item.value}}
            </div>
            <div class="name">{{item.name}}</div>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>

</ion-card-content>