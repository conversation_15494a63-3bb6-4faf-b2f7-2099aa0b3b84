# 数据同步服务完整指南

## 📋 目录

1. [概述](#概述)
2. [架构设计](#架构设计)
3. [文件结构](#文件结构)
4. [服务详解](#服务详解)
5. [使用指南](#使用指南)
6. [API 参考](#api-参考)
7. [最佳实践](#最佳实践)
8. [故障排除](#故障排除)

## 概述

数据同步服务是一个统一的数据同步解决方案，主要用于在弱网或离线场景下，将业务数据本地缓存，并在网络恢复后自动上传，确保数据可靠性和用户体验。该服务适用于移动端、PWA、IoT等需要离线数据保障的业务场景。

### 主要功能

- **本地数据缓存**：支持多业务类型的数据本地持久化存储
- **网络状态监听**：自动感知网络变化，网络恢复后自动触发数据上传
- **自动上传与失败重试**：支持自动批量上传，失败后自动重试，最大重试次数可配置
- **统一异常处理**：对上传失败、超最大重试的数据进行统一处理和日志记录
- **上传格式灵活配置**：支持通过配置决定每种业务类型的上传数据格式（数组/对象）

### 架构重构说明

为了提高代码的可维护性和可测试性，数据同步服务已经重构为模块化架构：

- **DataSyncManagerService**: 主协调器，提供统一的对外接口，保持向后兼容
- **SyncCacheManagerService**: 缓存管理服务，负责本地数据的增删改查
- **SyncUploadManagerService**: 上传管理服务，负责数据上传和网络请求处理
- **SyncRetryManagerService**: 重试管理服务，负责失败重试和数据导出功能

这种架构设计遵循单一职责原则，每个服务专注于特定的功能领域。

## 架构设计

### 架构图

```
DataSyncManagerService (主协调器)
├── SyncCacheManagerService (缓存管理)
├── SyncUploadManagerService (上传管理)
└── SyncRetryManagerService (重试管理)
```

### 设计原则

1. **单一职责原则**: 每个服务专注于特定的功能领域
2. **依赖注入**: 通过构造函数注入依赖，便于测试和扩展
3. **向后兼容**: 保持原有API不变，确保现有代码无需修改
4. **可扩展性**: 新功能可以通过添加新服务或扩展现有服务实现

## 文件结构

```
src/app/@core/providers/data-sync/
├── index.ts                           # 统一导出入口
├── data-sync-manager.service.ts       # 主协调器服务
├── sync-cache-manager.service.ts      # 缓存管理服务
├── sync-upload-manager.service.ts     # 上传管理服务
├── sync-retry-manager.service.ts      # 重试管理服务
└── types/
    └── sync-data.types.ts             # 类型定义

# 向后兼容文件（已废弃，仅为兼容保留）
src/app/@core/providers/data-sync-manager.service.ts
```

### 导入方式

```typescript
// 推荐方式：从统一入口导入
import { DataSyncManagerService, SyncDataType } from 'src/app/@core/providers/data-sync';

// 高级使用：直接导入子服务
import { 
  SyncCacheManagerService, 
  SyncUploadManagerService 
} from 'src/app/@core/providers/data-sync';

// 类型导入
import { SyncCacheItem, FailedDataGroup } from 'src/app/@core/providers/data-sync';
```

## 服务详解

### 1. DataSyncManagerService (主协调器)

**职责**: 提供统一的对外接口，协调各个子服务

**主要方法**:
- `init()`: 初始化网络监听
- `addToCache()`: 添加数据到缓存并尝试上传
- `getCache()`: 获取缓存数据
- `uploadAllCachedData()`: 批量上传所有缓存数据
- `getFailedData()`: 获取失败数据
- `manualRetryFailedData()`: 手动重试失败数据
- `exportFailedDataToFile()`: 导出失败数据

### 2. SyncCacheManagerService (缓存管理)

**职责**: 负责本地缓存的增删改查操作

**主要方法**:
- `addToCache()`: 添加数据到缓存
- `getCache()`: 获取指定类型的缓存
- `getAllCache()`: 获取所有类型的缓存
- `removeFromCache()`: 移除单个缓存项
- `removeBatchFromCache()`: 批量移除缓存项
- `updateCacheItems()`: 更新缓存项状态
- `clearCache()`: 清空指定类型缓存
- `getCacheStats()`: 获取缓存统计信息

### 3. SyncUploadManagerService (上传管理)

**职责**: 负责数据上传逻辑，包括批量上传、单个上传、网络请求处理

**主要方法**:
- `tryImmediateUpload()`: 尝试立即上传单个数据
- `uploadAllCachedData()`: 批量上传所有缓存数据
- `isCurrentlyUploading()`: 检查是否正在上传
- `sendRequestByMethod()`: 发送网络请求

### 4. SyncRetryManagerService (重试管理)

**职责**: 负责重试机制和失败数据处理

**主要方法**:
- `getFailedData()`: 获取失败数据
- `getFailedDataCount()`: 获取失败数据数量
- `manualRetryFailedData()`: 手动重试失败数据
- `clearFailedData()`: 清理失败数据
- `exportFailedDataToText()`: 导出失败数据
- `getRetryStats()`: 获取重试统计信息
- `filterValidItems()`: 过滤有效数据项
- `incrementRetryCount()`: 增加重试次数

## 使用指南

### 基本使用

```typescript
import { DataSyncManagerService, SyncDataType } from 'src/app/@core/providers/data-sync';

// 注入服务
constructor(private dataSyncManager: DataSyncManagerService) {}

// 初始化（在 deviceready 后调用）
this.dataSyncManager.init();

// 添加数据到缓存
const result = await this.dataSyncManager.addToCache(
  SyncDataType.MANUAL_CLOCK_IN,
  data,
  uploadUrl
);

if (result.status === 'uploaded') {
  console.log('数据已成功上传');
} else {
  console.log('数据已缓存，将在网络恢复后自动上传');
}
```

### 高级使用

```typescript
// 直接使用子服务获取更细粒度的控制
import { 
  SyncCacheManagerService, 
  SyncRetryManagerService 
} from 'src/app/@core/providers/data-sync';

// 获取缓存统计
const stats = await this.cacheManager.getCacheStats();

// 获取重试统计
const retryStats = await this.retryManager.getRetryStats();
```

## API 参考

### DataSyncManagerService

#### addToCache(type, data, uploadUrl, method?)

添加业务数据到本地缓存，并尝试立即上传。

**参数**:
- `type: SyncDataType` - 业务类型
- `data: any` - 业务数据对象
- `uploadUrl: string` - 完整的上传接口地址
- `method?: string` - HTTP 请求方式，默认 'POST'

**返回值**:
```typescript
Promise<{
  status: 'uploaded', code: number, msg: string
} | {
  status: 'cached'
}>
```

**示例**:
```typescript
const result = await dataSyncManager.addToCache(
  SyncDataType.MANUAL_CLOCK_IN,
  { taskCode: 'T001', userCode: 'U001' },
  'https://api.example.com/upload'
);
```

#### getCache(type)

获取指定类型的本地缓存数据。

**参数**:
- `type: SyncDataType` - 业务类型

**返回值**: `Promise<SyncCacheItem[]>`

#### uploadAllCachedData()

批量上传所有业务类型的本地缓存数据。

**返回值**: `Promise<void>`

#### getFailedData()

获取所有达到最大重试次数的失败数据。

**返回值**: `Promise<FailedDataGroup[]>`

#### manualRetryFailedData()

手动重试失败的数据（重置重试次数）。

**返回值**: `Promise<void>`

#### exportFailedDataToFile()

导出失败数据到文本格式。

**返回值**: `Promise<string>`

### 业务数据类型

```typescript
enum SyncDataType {
  MANUAL_CLOCK_IN = 'manualClockIn',           // 用户手动打卡
  AUTO_KEY_POINT_CLOCK_IN = 'autoKeyPointClockIn', // 系统自动关键点打卡
  INSPECTION_REPORT = 'inspectionReport'        // 巡检事件上报
}
```

### 缓存数据结构

```typescript
interface SyncCacheItem {
  id: string;           // 唯一标识
  type: SyncDataType;   // 业务类型
  data: any;           // 业务原始数据
  timestamp: number;    // 缓存时间（毫秒时间戳）
  retryCount: number;   // 当前重试次数
  uploadUrl: string;    // 上传接口地址
  method?: string;      // 请求方式
}
```

## 最佳实践

### 1. 初始化时机

```typescript
// 在 Platform ready 后初始化
this.platform.ready().then(() => {
  this.dataSyncManager.init();
});
```

### 2. 错误处理

```typescript
try {
  const result = await this.dataSyncManager.addToCache(type, data, url);
  if (result.status === 'uploaded' && result.code === 0) {
    this.showSuccessMessage('数据上传成功');
  } else if (result.status === 'cached') {
    this.showInfoMessage('数据已缓存，网络恢复后自动上传');
  } else {
    this.showWarningMessage('上传失败，数据已缓存');
  }
} catch (error) {
  this.showErrorMessage('操作失败');
}
```

### 3. 网络状态处理

服务会自动监听网络状态，无需手动处理。但可以通过以下方式手动触发上传：

```typescript
// 手动触发上传
await this.dataSyncManager.uploadAllCachedData();
```

### 4. 失败数据管理

```typescript
// 获取失败数据数量
const failedCount = await this.dataSyncManager.getFailedDataCount();

if (failedCount > 0) {
  // 提供用户选择：重试或导出
  const choice = await this.showActionSheet(['重试上传', '导出数据']);

  if (choice === 'retry') {
    await this.dataSyncManager.manualRetryFailedData();
  } else if (choice === 'export') {
    const exportData = await this.dataSyncManager.exportFailedDataToFile();
    this.saveToFile(exportData);
  }
}
```

### 5. 性能优化

- 避免频繁调用 `uploadAllCachedData()`，服务会自动处理
- 大量数据时考虑分批处理
- 定期清理失败数据以避免存储空间占用

## 故障排除

### 常见问题

#### 1. 数据未自动上传

**可能原因**:
- 网络状态监听未初始化
- 网络连接不稳定
- 服务器接口异常

**解决方案**:
```typescript
// 检查初始化状态
this.dataSyncManager.init();

// 手动触发上传
await this.dataSyncManager.uploadAllCachedData();

// 检查网络状态
console.log('正在上传:', this.dataSyncManager.isCurrentlyUploading());
```

#### 2. 重试次数过多

**可能原因**:
- 接口地址错误
- 数据格式不匹配
- 服务器持续异常

**解决方案**:
```typescript
// 检查失败数据
const failedData = await this.dataSyncManager.getFailedData();
console.log('失败数据:', failedData);

// 清理失败数据
await this.dataSyncManager.clearFailedData();
```

#### 3. 缓存数据过多

**解决方案**:
```typescript
// 获取缓存统计
const stats = await this.dataSyncManager.getCacheStats();
console.log('缓存统计:', stats);

// 清理特定类型缓存
await this.dataSyncManager.clearCache(SyncDataType.MANUAL_CLOCK_IN);

// 清理所有缓存
await this.dataSyncManager.clearAllCache();
```

### 调试技巧

1. **启用详细日志**: 服务会自动输出详细的调试信息到控制台
2. **监控网络请求**: 检查浏览器开发者工具的网络面板
3. **检查本地存储**: 使用浏览器开发者工具查看 IndexedDB 中的缓存数据

### 联系支持

如果遇到无法解决的问题，请提供以下信息：
- 错误日志
- 网络环境
- 设备信息
- 复现步骤

---

## 更新日志

### v2.0.0 (当前版本)
- 重构为模块化架构
- 提高代码可维护性和可测试性
- 保持完全向后兼容
- 新增详细的统计和监控功能

### v1.0.0
- 初始版本
- 基本的缓存和上传功能
- 简单的重试机制
