import { Injectable } from '@angular/core';
import { GestureConfig, BoundaryResult, ImageDimensions } from './image-gesture.model';

/**
 * 拖拽手势处理器
 * 负责处理图片的拖拽手势
 */
@Injectable({
  providedIn: 'root'
})
export class PanGestureHandlerService {
  private isDragging = false;
  private lastTranslateX = 0;
  private lastTranslateY = 0;

  /**
   * 处理拖拽开始
   */
  handlePanStart(
    onStopInertia: () => void,
    onToggleClass: (className: string, add: boolean) => void,
    onClearCache: () => void,
    onClearVelocityTracker: () => void
  ): void {
    // 停止任何正在进行的惯性滑动
    onStopInertia();

    this.isDragging = true;
    onToggleClass('dragging', true);

    // 性能优化：拖拽开始时清除缓存，确保获取最新的尺寸信息
    onClearCache();

    // 清空速度追踪器
    onClearVelocityTracker();
  }

  /**
   * 处理拖拽
   */
  handlePan(
    ev: any,
    config: GestureConfig,
    dimensions: ImageDimensions | null,
    bounds: BoundaryResult | null,
    onAddVelocitySample: (deltaX: number, deltaY: number) => void,
    onUpdateTranslation: (x: number, y: number) => void,
    constrainToBounds: (x: number, y: number, bounds: BoundaryResult) => { x: number; y: number },
    currentScale: number = 1
  ): void {
    if (!dimensions) {
      this.handleSimplePan(ev, config, onUpdateTranslation);
      return;
    }

    // 根据缩放级别动态调整阻尼系数，放大时降低敏感度
    const scaleAdjustedDamping = config.dampingFactor! * Math.max(0.3, 1 / Math.max(1, currentScale * 0.8));

    // 计算拖拽增量
    let deltaX = ev.deltaX * scaleAdjustedDamping;
    let deltaY = ev.deltaY * scaleAdjustedDamping;

    // 记录实际的移动增量用于惯性滑动（而不是累积位置）
    onAddVelocitySample(deltaX, deltaY);

    let nextX = this.lastTranslateX + deltaX;
    let nextY = this.lastTranslateY + deltaY;

    // 限制拖拽范围，确保图片不会完全移出容器
    if (bounds) {
      const constrained = constrainToBounds(nextX, nextY, bounds);
      onUpdateTranslation(constrained.x, constrained.y);
    } else {
      onUpdateTranslation(nextX, nextY);
    }
  }

  /**
   * 简单拖拽处理（降级方案）
   */
  private handleSimplePan(
    ev: any,
    config: GestureConfig,
    onUpdateTranslation: (x: number, y: number) => void
  ): void {
    // 即使是简单拖拽也应该有基本的边界限制
    let nextX = this.lastTranslateX + (ev.deltaX * config.dampingFactor!);
    let nextY = this.lastTranslateY + (ev.deltaY * config.dampingFactor!);

    // 设置一个基本的边界限制，防止图片移动过远
    const maxOffset = 200; // 最大偏移像素
    const constrainedX = Math.max(-maxOffset, Math.min(maxOffset, nextX));
    const constrainedY = Math.max(-maxOffset, Math.min(maxOffset, nextY));
    onUpdateTranslation(constrainedX, constrainedY);
  }

  /**
   * 处理拖拽结束
   */
  handlePanEnd(
    currentX: number,
    currentY: number,
    onToggleClass: (className: string, add: boolean) => void,
    onStartInertiaScroll: () => void,
    onSmartBoundaryAdjustment: () => void,
    onUpdateTranslation: (x: number, y: number, updateLast: boolean) => void,
    isInertiaScrolling: () => boolean
  ): void {
    this.isDragging = false;
    onToggleClass('dragging', false);

    // 启动惯性滑动
    onStartInertiaScroll();

    // 如果没有惯性滑动，进行智能边界调整
    if (!isInertiaScrolling()) {
      onSmartBoundaryAdjustment();
    }

    onUpdateTranslation(currentX, currentY, true);
  }

  /**
   * 更新位移状态
   */
  updateTranslation(x: number, y: number, updateLast: boolean = false): void {
    if (updateLast) {
      this.lastTranslateX = x;
      this.lastTranslateY = y;
    }
  }

  /**
   * 获取当前位移
   */
  getCurrentTranslation(): { x: number; y: number } {
    return {
      x: this.lastTranslateX,
      y: this.lastTranslateY
    };
  }

  /**
   * 设置位移
   */
  setTranslation(x: number, y: number): void {
    this.lastTranslateX = x;
    this.lastTranslateY = y;
  }

  /**
   * 重置位移
   */
  resetTranslation(): void {
    this.lastTranslateX = 0;
    this.lastTranslateY = 0;
    this.isDragging = false;
  }

  /**
   * 检查是否正在拖拽
   */
  isDraggingActive(): boolean {
    return this.isDragging;
  }

  /**
   * 平滑移动到指定位置
   */
  smoothMoveTo(
    targetX: number,
    targetY: number,
    onUpdate: (x: number, y: number) => void,
    duration: number = 300
  ): void {
    const startX = this.lastTranslateX;
    const startY = this.lastTranslateY;
    const deltaX = targetX - startX;
    const deltaY = targetY - startY;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // 使用缓动函数
      const easeProgress = this.easeOutCubic(progress);
      const newX = startX + deltaX * easeProgress;
      const newY = startY + deltaY * easeProgress;
      
      onUpdate(newX, newY);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.setTranslation(targetX, targetY);
      }
    };

    animate();
  }

  /**
   * 缓动函数：缓出三次方
   */
  private easeOutCubic(t: number): number {
    return 1 - Math.pow(1 - t, 3);
  }

  /**
   * 获取拖拽状态信息
   */
  getDragInfo(): {
    isDragging: boolean;
    lastTranslateX: number;
    lastTranslateY: number;
  } {
    return {
      isDragging: this.isDragging,
      lastTranslateX: this.lastTranslateX,
      lastTranslateY: this.lastTranslateY
    };
  }
}
