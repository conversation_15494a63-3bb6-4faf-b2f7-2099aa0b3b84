import { Injectable } from '@angular/core';
import { IonContent } from '@ionic/angular';

export interface ScrollState {
  scrollState: 'top' | 'bottom' | 'middle';
  scrollButtonIcon: string;
}

@Injectable({
  providedIn: 'root'
})
export class ScrollControlService {
  private scrollState: 'top' | 'bottom' | 'middle' = 'top';
  private scrollButtonIcon = 'chevron-down-outline';

  /**
   * 设置滚动控制
   * @param content IonContent实例
   * @param onStateChange 状态变化回调函数
   */
  setupScrollControl(content: IonContent, onStateChange?: (state: ScrollState) => void): void {
    if (content) {
      content.getScrollElement().then(() => {
        this.updateScrollButtonState(content, onStateChange);
      });
      content.ionScroll.subscribe(() => {
        this.updateScrollButtonState(content, onStateChange);
      });
    }
  }

  /**
   * 刷新滚动按钮状态
   */
  private updateScrollButtonState(content: IonContent, onStateChange?: (state: ScrollState) => void): void {
    if (content) {
      content.getScrollElement().then(scrollElement => {
        const scrollTop = scrollElement.scrollTop;
        const scrollHeight = scrollElement.scrollHeight;
        const clientHeight = scrollElement.clientHeight;
        
        if (scrollTop === 0) {
          this.scrollState = 'top';
          this.scrollButtonIcon = 'chevron-down-outline';
        } else if (scrollTop + clientHeight >= scrollHeight - 2) {
          this.scrollState = 'bottom';
          this.scrollButtonIcon = 'chevron-up-outline';
        } else {
          this.scrollState = 'middle';
          this.scrollButtonIcon = 'chevron-up-outline';
        }

        // 触发状态变化回调
        if (onStateChange) {
          onStateChange({
            scrollState: this.scrollState,
            scrollButtonIcon: this.scrollButtonIcon
          });
        }
      });
    }
  }

  /**
   * 悬浮按钮点击事件，根据状态滚动
   */
  onScrollButtonClick(content: IonContent, onStateChange?: (state: ScrollState) => void): void {
    if (this.scrollState === 'top') {
      this.scrollToBottom(content).then(() => this.updateScrollButtonState(content, onStateChange));
    } else {
      this.scrollToTop(content).then(() => this.updateScrollButtonState(content, onStateChange));
    }
  }

  /**
   * 滚动到顶部
   */
  scrollToTop(content: IonContent): Promise<void> {
    if (content) {
      return content.scrollToTop(300);
    }
    return Promise.resolve();
  }

  /**
   * 滚动到底部
   */
  scrollToBottom(content: IonContent): Promise<void> {
    if (content) {
      return content.scrollToBottom(300);
    }
    return Promise.resolve();
  }

  /**
   * 获取当前滚动状态
   */
  getCurrentState(): ScrollState {
    return {
      scrollState: this.scrollState,
      scrollButtonIcon: this.scrollButtonIcon
    };
  }
} 