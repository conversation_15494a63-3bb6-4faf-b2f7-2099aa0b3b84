import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

import { NetworkService } from '../network.service';
import { SyncCacheManagerService } from './sync-cache-manager.service';
import { SyncUploadManagerService } from './sync-upload-manager.service';
import { SyncRetryManagerService } from './sync-retry-manager.service';
import { 
  SyncDataType, 
  SyncCacheItem, 
  AddToCacheResult, 
  FailedDataGroup 
} from './types/sync-data.types';

// 重新导出类型，保持向后兼容
export { SyncDataType, SyncCacheItem } from './types/sync-data.types';

/**
 * 统一数据同步服务（主协调器）
 * 负责协调各个子服务，提供统一的对外接口
 * 注：数据加密由CoreInterceptorService统一处理
 */
@Injectable({
  providedIn: 'root'
})
export class DataSyncManagerService {
  /**
   * 网络状态（true为在线，false为离线）
   */
  private networkStatus$ = new BehaviorSubject<boolean>(false);

  constructor(
    private networkService: NetworkService,
    private cacheManager: SyncCacheManagerService,
    private uploadManager: SyncUploadManagerService,
    private retryManager: SyncRetryManagerService
  ) { }

  /**
   * 初始化网络状态监听和自动上传逻辑，需在 deviceready 后调用
   */
  public init() {
    this.networkService.getNetworkStatus().subscribe(isOnline => {
      console.log(`[DataSyncManagerService] Network status changed: ${isOnline ? 'Online' : 'Offline'}`);
      this.networkStatus$.next(isOnline);
      if (isOnline && !this.uploadManager.isCurrentlyUploading()) {
        this.uploadAllCachedData();
      }
    });
  }

  /**
   * 添加业务数据到本地缓存
   * 返回本次数据的实际处理结果
   */
  async addToCache(type: SyncDataType, data: any, uploadUrl: string, method: string = 'POST'): Promise<AddToCacheResult> {
    // 先添加到缓存
    const cacheItem = await this.cacheManager.addToCache(type, data, uploadUrl, method);

    // 尝试立即上传
    try {
      const uploadResult = await this.uploadManager.tryImmediateUpload(type, data, uploadUrl, method);
      
      if (uploadResult.success) {
        // 上传成功，从缓存中移除
        await this.cacheManager.removeFromCache(type, cacheItem.id);
        return { 
          status: 'uploaded', 
          code: uploadResult.code || 0, 
          msg: uploadResult.msg || '上传成功' 
        };
      } else {
        return { status: 'cached' };
      }
    } catch (err) {
      console.error('[DataSyncManagerService] 立即上传失败，数据已缓存:', err);
      return { status: 'cached' };
    }
  }

  /**
   * 获取指定类型的本地缓存数据
   */
  async getCache(type: SyncDataType): Promise<SyncCacheItem[]> {
    return this.cacheManager.getCache(type);
  }

  /**
   * 移除指定类型的缓存项
   */
  async removeFromCache(type: SyncDataType, id: string): Promise<void> {
    return this.cacheManager.removeFromCache(type, id);
  }

  /**
   * 批量上传所有业务类型的本地缓存数据
   */
  async uploadAllCachedData(): Promise<void> {
    return this.uploadManager.uploadAllCachedData();
  }

  /**
   * 获取所有达到最大重试次数的失败数据
   */
  async getFailedData(): Promise<FailedDataGroup[]> {
    return this.retryManager.getFailedData();
  }

  /**
   * 手动重试失败的数据（重置重试次数）
   */
  async manualRetryFailedData(): Promise<void> {
    await this.retryManager.manualRetryFailedData();

    // 触发重新上传
    if (this.networkStatus$.value) {
      this.uploadAllCachedData();
    }
  }

  /**
   * 检查指定任务的关键点是否有待上传的打卡缓存数据
   * @param taskCode 任务编码
   * @param pointCode 关键点编码（可选，如果不提供则检查整个任务）
   * @returns 缓存的打卡数据项
   */
  async getTaskClockInCache(taskCode: string, pointCode?: string): Promise<SyncCacheItem[]> {
    // 获取所有打卡相关的缓存数据
    const manualClockInCache = await this.getCache(SyncDataType.MANUAL_CLOCK_IN);
    const autoClockInCache = await this.getCache(SyncDataType.AUTO_KEY_POINT_CLOCK_IN);
    const notInspectedCache = await this.getCache(SyncDataType.NOT_INSPECTED_REASON);

    // 合并所有缓存数据
    const allCache = [...manualClockInCache, ...autoClockInCache, ...notInspectedCache];

    // 过滤出指定任务的缓存数据
    let taskRelatedCache = allCache.filter(cache => cache.data.taskCode === taskCode);

    // 如果指定了关键点编码，进一步过滤
    if (pointCode) {
      taskRelatedCache = taskRelatedCache.filter(cache => cache.data.pointCode === pointCode);
    }

    return taskRelatedCache;
  }

  /**
   * 检查指定关键点是否有待上传的打卡缓存数据
   * @param taskCode 任务编码
   * @param pointCode 关键点编码
   * @returns 是否有缓存数据
   */
  async hasKeyPointClockInCache(taskCode: string, pointCode: string): Promise<boolean> {
    const cache = await this.getTaskClockInCache(taskCode, pointCode);
    return cache.length > 0;
  }

  /**
   * 获取任务所有关键点的缓存状态映射
   * @param taskCode 任务编码
   * @returns 关键点编码到缓存状态的映射
   */
  async getTaskKeyPointsCacheMap(taskCode: string): Promise<Map<string, boolean>> {
    console.log(`🔍 [缓存检查] 开始检查任务 ${taskCode} 的缓存数据`);

    const taskCache = await this.getTaskClockInCache(taskCode);
    const cacheMap = new Map<string, boolean>();

    console.log(`🔍 [缓存检查] 找到 ${taskCache.length} 条缓存记录`);

    // 为每个有缓存数据的关键点设置标记
    taskCache.forEach(cache => {
      if (cache.data.pointCode) {
        cacheMap.set(cache.data.pointCode, true);
        console.log(`🔍 [缓存检查] 关键点 ${cache.data.pointCode} 有缓存数据 (类型: ${cache.type}, 时间: ${new Date(cache.timestamp).toLocaleString()})`);
      }
    });

    console.log(`🔍 [缓存检查] 缓存映射构建完成，共 ${cacheMap.size} 个关键点有缓存数据`);
    return cacheMap;
  }

  /**
   * 导出失败数据到文本文件
   */
  async exportFailedDataToFile(): Promise<string> {
    return this.retryManager.exportFailedDataToText();
  }

  // ========== 扩展方法（可选使用） ==========

  /**
   * 获取缓存统计信息
   */
  async getCacheStats() {
    return this.cacheManager.getCacheStats();
  }

  /**
   * 获取重试统计信息
   */
  async getRetryStats() {
    return this.retryManager.getRetryStats();
  }

  /**
   * 获取失败数据数量
   */
  async getFailedDataCount(): Promise<number> {
    return this.retryManager.getFailedDataCount();
  }

  /**
   * 清理失败数据
   */
  async clearFailedData(): Promise<number> {
    return this.retryManager.clearFailedData();
  }

  /**
   * 检查是否正在上传
   */
  isCurrentlyUploading(): boolean {
    return this.uploadManager.isCurrentlyUploading();
  }

  /**
   * 获取所有缓存数据
   */
  async getAllCache() {
    return this.cacheManager.getAllCache();
  }

  /**
   * 清空指定类型的缓存
   */
  async clearCache(type: SyncDataType): Promise<void> {
    return this.cacheManager.clearCache(type);
  }

  /**
   * 清空所有缓存
   */
  async clearAllCache(): Promise<void> {
    return this.cacheManager.clearAllCache();
  }
}
