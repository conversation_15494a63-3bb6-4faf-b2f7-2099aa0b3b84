import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams } from '@ngx-resource/core';
import { PageGridResult, RequestResult } from '../@core/base/request-result';
import { TrackUserData } from './class/playback.interface';
import { Task, TaskParams } from './class/work';

/**
 * 工作服务
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root'
})
export class WorkService extends Resource {

  constructor() {
    super();
  }
  /**
   * 任务列表
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/all/task',
  })
  taskGrid!: IResourceMethodObservable<TaskParams, PageGridResult<Task[]>>;
  /**
   * 任务详情
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/task/msg/byCode',
  })
  taskByCode!: IResourceMethodObservable<{ taskCode: string }, RequestResult<Task>>;

  /**
   * 关键点查询
   */
  @ResourceAction({
    path: '/cruise/linecruising/keyPoints/getKeypointByTask',
  })
  getKeypointByTask!: IResourceMethodObservable<{ taskId: string }, RequestResult<any>>;

  /**
   * 刷新巡检合格率
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/refresh/completion/rate',
  })
  refreshRate!: IResourceMethodObservable<{ taskCode: string }, RequestResult<Task>>;

  /**
   * 轨迹回放
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/trajectory/byTask',
  })
  getTrailPointsByTask!: IResourceMethodObservable<{ taskCode: string }, RequestResult<TrackUserData[]>>;
}
