<!-- 报警信息 -->
<ost-map #ostMap
  [zoom]="7"
  [layerIds]="['p_pipe_joint_info','inspect_alarm',]"
  (mapLoaded)="onMapLoaded()"
>
  <ost-layout-item align="top-left">
    <div class="map-close-btn" 
      [class.disabled]="!closeButtonEnabled"
      [class.hidden]="!mapLoaded">
      <ion-icon name="close" (click)="onClose()"></ion-icon>
    </div>
  </ost-layout-item>

  <ost-layout-item align="top-right">
    <div class="alarm-btn-wrapper">
      <img class="alarm-btn"
        [class.disabled]="modalStates.alarm"
        [class.hidden]="!mapLoaded"
        src="/assets/menu/alarm-map.png"
        (click)="onAlarmClick()">
      <span class="alarm-badge" *ngIf="+unreadCount > 0">{{ unreadCount }}</span>
    </div>
  </ost-layout-item>
</ost-map>

