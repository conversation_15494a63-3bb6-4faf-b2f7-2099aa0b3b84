# 后台定位和锁屏状态下关键点提醒问题解决方案

## 问题描述

在 Ionic5 + Angular12 + Cordova 项目中，当手机锁屏且断网时，轨迹功能正常运行，但关键点提醒（震动、TTS）和关键点变色功能失效。

## 问题原因分析

### 1. 后台定位配置不完整
原始配置缺少关键的后台运行参数：
- `stopOnTerminate: false` - 应用终止时不停止定位
- `startForeground: true` - 启用前台服务
- `pauseLocationUpdates: false` - 应用暂停时不暂停位置更新

### 2. Angular Zone 问题
在后台状态下，Angular 的变更检测机制可能不会正常工作，导致某些服务调用失效。

### 3. 应用生命周期管理缺失
缺少对应用进入后台、锁屏等状态的专门处理。

## 解决方案

### 1. 更新后台定位配置

已更新 `src/app/app.boot.ts` 中的 `BackgroundGeolocationDefaultConfig`：

```typescript
const BackgroundGeolocationDefaultConfig: BackgroundGeolocationConfig = {
  desiredAccuracy: 10,
  stationaryRadius: 0,
  distanceFilter: 2,
  fastestInterval: 200,
  interval: 200,
  notificationTitle: '巡检app',
  notificationText: '正在定位当中...',
  syncThreshold: 100,
  maxLocations: 50000,
  locationProvider: 3,
  // 关键：后台运行配置
  stopOnTerminate: false, // 应用终止时不停止定位服务
  startOnBoot: false, // 设备启动时不自动启动
  startForeground: true, // 启用前台服务，确保后台运行
  pauseLocationUpdates: false, // 应用暂停时不暂停位置更新
  saveBatteryOnBackground: false, // 后台不节省电池
  enableHighAccuracy: true // 启用高精度定位
};
```

### 2. 优化关键点提醒服务

已更新 `src/app/@core/services/key-point-alert.service.ts`：

- 使用 `NgZone.runOutsideAngular()` 确保后台状态下正常执行
- 添加平台检测，确保在真机环境下执行
- 增强错误处理和日志记录
- 分离震动和TTS功能，提高可靠性

### 3. 优化位置变化回调

已更新 `src/app/execut/execut.component.ts`：

- 在位置变化回调中使用 `NgZone.run()` 确保 Angular 变更检测正常工作
- 添加应用生命周期监听

### 4. 新增应用生命周期管理服务

创建了 `src/app/@core/services/app-lifecycle.service.ts`：

- 监听应用暂停/恢复事件
- 提供应用状态和锁屏状态的观察者
- 支持手动状态设置（用于测试）

## 额外建议

### 1. Android 权限配置

确保在 `config.xml` 中添加必要的权限：

```xml
<!-- 后台定位权限 -->
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />

<!-- 震动权限 -->
<uses-permission android:name="android.permission.VIBRATE" />

<!-- 音频权限 -->
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
```

### 2. 电池优化白名单

建议引导用户将应用添加到电池优化白名单：

```typescript
// 检查电池优化状态
if (this.platform.is('android')) {
  // 可以使用相关插件检查和请求电池优化白名单
}
```

### 3. 测试建议

1. **真机测试**：必须在真机上测试，模拟器无法准确模拟后台行为
2. **网络状态测试**：分别测试有网络和无网络状态
3. **锁屏测试**：测试锁屏后的功能是否正常
4. **长时间测试**：测试长时间后台运行的稳定性

### 4. 调试方法

1. **查看日志**：通过 `adb logcat` 查看应用日志
2. **通知栏检查**：确认前台服务通知是否显示
3. **位置数据检查**：确认位置数据是否持续上传

## 注意事项

1. **电池消耗**：后台持续定位会增加电池消耗
2. **用户体验**：需要向用户说明为什么需要后台定位权限
3. **系统限制**：不同 Android 版本对后台应用的限制不同
4. **厂商定制**：某些厂商的系统可能有额外的后台限制

## 验证步骤

1. 安装更新后的应用到真机
2. 开始轨迹任务
3. 锁屏并断开网络连接
4. 移动到关键点附近
5. 验证是否收到震动和语音提醒
6. 解锁查看关键点是否变色

如果问题仍然存在，请检查：
- 应用是否有后台定位权限
- 应用是否在电池优化白名单中
- 系统是否有特殊的后台限制设置
