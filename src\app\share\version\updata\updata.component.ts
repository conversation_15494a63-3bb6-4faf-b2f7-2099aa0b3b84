import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { VersionInfo } from '../class/version-info';
import { DownloadComponent } from '../download/download.component';
import { environment } from 'src/environments/environment';

declare let InstallApk: any;
@Component({
  selector: 'app-updata',
  templateUrl: './updata.component.html',
  styleUrls: ['./updata.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UpdataComponent implements OnInit, OnDestroy {
  @Input() versionInfo: VersionInfo;
  hasPermission: boolean;
  constructor(private modalController: ModalController, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    /**
     * 检测是否有安装权限
     */
    InstallApk.checkInstallPermission(hasPermission => {
      this.hasPermission = hasPermission;
      this.cd.detectChanges();
    }, error => undefined);
  }
  /**
   * 取消
   */
  onCancal(): void {
    this.modalController.dismiss();
  }

  /**
   * 去容许安装应用
   */
  onToAllow(): void {
    InstallApk.switchToAppInstallSettings();
    document.addEventListener('resume', this.onWinResume, false);
  }

  /**
   * 在被挂起的应用转到前台时触发
   */
  onWinResume = () => {
    InstallApk.checkInstallPermission(hasPermission => {
      this.hasPermission = hasPermission;
      this.cd.detectChanges();
      if (this.hasPermission) {
        document.removeEventListener('resume', this.onWinResume);
      }
    }, error => undefined);
  }
  /**
   * 立即更新
   */
  async onUpdataApp(url: string): Promise<void> {
    this.modalController.dismiss();
    const modal = await this.modalController.create({
      component: DownloadComponent,
      componentProps: { url: `${environment.production ? 'https' : 'http'}://${environment.api.ip}:${environment.api.port}${url}` },
      cssClass: 'dialog-box-xs',
      backdropDismiss: false
    });
    await modal.present();
  }

  ngOnDestroy(): void {
    document.removeEventListener('resume', this.onWinResume);
  }
}
