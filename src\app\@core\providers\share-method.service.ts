import { DatePipe } from "@angular/common";
import { Injectable } from "@angular/core";
import * as turf from '@turf/turf';

@Injectable({
  providedIn: 'root'
})

export class ShareMethodService {

  constructor(
    private datePipe: DatePipe,
  ) { }

  /**
   * 日期转换
   * @param date 日期
   * @param format 格式
   */
  public dateTransform(date: any, format = 'yyyy-MM-dd'): any {
    return this.datePipe.transform(date ? new Date(date) : new Date(), format);
  }

  /**
   * 计算截止日期与当前日期之间的天数差
   * @param deadline 截止日期
   * @returns 天数差
   */
  public daysUntilDeadline(deadline: string): number {
    const currentDate = new Date();
    const deadlineDate = new Date(deadline);

    // 确保日期格式正确并且可以被解析
    if (isNaN(deadlineDate.getTime())) {
      throw new Error('Invalid date format for deadline');
    }

    // 计算截止日期与当前日期之间的差值（以毫秒为单位）
    const diffMs = deadlineDate.getTime() - currentDate.getTime();

    // 将差值转换为天数
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));

    // 返回天数差
    return diffDays;
  }

  /**
   * 判断当前位置是否在指定范围内，或者在范围周边一定距离内
   * @param polygonCoordinates - 多边形范围的坐标数组
   * @param currentLat - 当前纬度
   * @param currentLng - 当前经度
   * @param bufferDistance - 缓冲区距离（米）
   * @returns 是否在范围内或者范围周边
   */
  public isWithinPolygonOrBuffer(
    polygonCoordinates: number[][],
    currentLat: number,
    currentLng: number,
    bufferDistance: number = 20000
  ): boolean {
    // 创建多边形
    const polygon = turf.polygon([polygonCoordinates]);

    // 创建当前位置信息点
    const currentPosition = turf.point([currentLng, currentLat]);

    // 判断是否在多边形范围内
    const isInPolygon = turf.booleanPointInPolygon(currentPosition, polygon);
    if (isInPolygon) {
      return true;
    }

    // 创建缓冲区（单位为米）
    const bufferedPolygon = turf.buffer(polygon, bufferDistance, { units: 'meters' });

    // 判断是否在缓冲区内
    const isInBuffer = turf.booleanPointInPolygon(currentPosition, bufferedPolygon);
    return isInBuffer;
  }

  /**
   * 为base64图片添加时间水印
   * @param base64 纯base64字符串（不带data:image/jpeg;base64,）
   * @param timestamp 时间字符串
   */
  public async addTimestampWatermarkToBase64(base64: string, timestamp: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = 'data:image/jpeg;base64,' + base64;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        // 设置水印样式
        const fontSize = Math.floor(canvas.width / 20);
        ctx.font = `bold ${fontSize}px Arial`;
        ctx.fillStyle = 'rgba(255,255,255,0.7)';
        ctx.textBaseline = 'bottom';
        ctx.textAlign = 'right';
        ctx.shadowColor = 'rgba(0,0,0,0.5)';
        ctx.shadowBlur = 4;
        ctx.fillText(timestamp, canvas.width - 10, canvas.height - 10);
        const watermarkedBase64 = canvas.toDataURL('image/jpeg', 0.9);
        resolve(watermarkedBase64.replace(/^data:image\/jpeg;base64,/, ''));
      };
      img.onerror = reject;
    });
  }

  /**
   * 格式化时间为YYYY-MM-DD HH:mm:ss
   * @param date Date对象
   */
  public formatDateTime(date: Date): string {
    const pad = (n: number) => n < 10 ? '0' + n : n;
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
  }

  /**
   * 格式化坐标字符串，去除中括号并保留每个数字小数点后8位（仅当超过8位时截断，不做四舍五入，整数和不足8位小数保持原样）
   */
  public formatPoint(point: string | number[]): string {
    if (!point) return '';
    // 新增：处理 number[]
    if (Array.isArray(point)) {
      return point
        .map(num => {
          if (typeof num !== 'number' || isNaN(num)) return '';
          const str = num.toString();
          const dotIdx = str.indexOf('.');
          if (dotIdx === -1) return str; // 整数，直接返回
          const [intPart, decPart] = str.split('.');
          if (decPart.length > 8) {
            return intPart + '.' + decPart.slice(0, 8);
          } else {
            return str;
          }
        })
        .join(', ');
    }
    // 去除中括号
    const cleaned = point.replace('[', '').replace(']', '');
    // 按逗号分割
    const parts = cleaned.split(',');
    // 对每个数字处理
    const formatted = parts.map(p => {
      const str = p.trim();
      // 判断是否为小数
      const dotIdx = str.indexOf('.');
      if (dotIdx === -1) return str; // 整数，直接返回
      const [intPart, decPart] = str.split('.');
      if (decPart.length > 8) {
        return intPart + '.' + decPart.slice(0, 8);
      } else {
        return str;
      }
    });
    return formatted.join(', ');
  }
}
