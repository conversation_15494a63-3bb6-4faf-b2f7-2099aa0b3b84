# 摄像头组件服务 API 文档

## 概述

本文档详细说明了摄像头组件中各个服务的API接口、参数、返回值和使用示例。

## CameraInfoService

摄像头信息服务，负责获取摄像头信息和轮询管理。

### 类定义

```typescript
@Injectable({
  providedIn: 'root'
})
export class CameraInfoService
```

### 构造函数

```typescript
constructor(private netSer: ShareModuleService)
```

**参数：**
- `netSer: ShareModuleService` - 网络服务实例，用于HTTP请求

### 方法

#### getCameraInfo(id: string): Promise<RequestResult<any>>

获取指定摄像头的详细信息。

**参数：**
- `id: string` - 摄像头ID

**返回值：**
- `Promise<RequestResult<any>>` - 包含摄像头信息的Promise

**示例：**
```typescript
const cameraInfo = await this.cameraInfoService.getCameraInfo('camera-001');
if (cameraInfo.code === 0) {
  console.log('摄像头信息:', cameraInfo.data);
}
```

#### startPolling(id: string, callback: Function, errorCallback: Function, interval?: number): void

开始轮询获取摄像头信息。

**参数：**
- `id: string` - 摄像头ID
- `callback: (data: any) => void` - 成功回调函数
- `errorCallback: (error: any) => void` - 错误回调函数
- `interval: number` - 轮询间隔（毫秒），默认10000ms

**示例：**
```typescript
this.cameraInfoService.startPolling(
  'camera-001',
  (data) => {
    console.log('获取到摄像头数据:', data);
    this.handleCameraData(data);
  },
  (error) => {
    console.error('轮询错误:', error);
    this.handleError(error);
  },
  5000 // 5秒轮询一次
);
```

#### stopPolling(): void

停止轮询。

**示例：**
```typescript
this.cameraInfoService.stopPolling();
```

#### checkPresetBitChange(presetBitId: string): boolean

检查预置位是否发生变化。

**参数：**
- `presetBitId: string` - 预置位ID

**返回值：**
- `boolean` - 是否发生变化

**示例：**
```typescript
if (this.cameraInfoService.checkPresetBitChange(data.presetBitId)) {
  console.log('预置位发生变化');
  this.drawAreas();
}
```

#### destroy(): void

销毁服务，清理资源。

**示例：**
```typescript
ngOnDestroy() {
  this.cameraInfoService.destroy();
}
```

## VideoPlayerService

视频播放器服务，负责FLV.js播放器的管理和错误处理。

### 类定义

```typescript
export class VideoPlayerService
```

### 属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `flvPlayer` | `any` | FLV.js播放器实例 |
| `currentUrl` | `string \| null` | 当前播放的URL |
| `videoElement` | `HTMLVideoElement \| null` | 视频元素 |
| `_onErrorCallback` | `Function \| null` | 错误回调函数 |
| `isDestroying` | `boolean` | 是否正在销毁 |

### 方法

#### initFlvPlayer(videoElement: HTMLVideoElement, videoUrl: string): void

初始化FLV播放器。

**参数：**
- `videoElement: HTMLVideoElement` - 视频元素
- `videoUrl: string` - 视频流URL

**示例：**
```typescript
this.videoPlayerService.initFlvPlayer(
  this.videoPlayer.nativeElement,
  'https://example.com/stream.flv'
);
```

#### destroyPlayer(): void

销毁播放器实例。

**示例：**
```typescript
this.videoPlayerService.destroyPlayer();
```

#### isPlayerInitialized(): boolean

检查播放器是否已初始化。

**返回值：**
- `boolean` - 是否已初始化

**示例：**
```typescript
if (this.videoPlayerService.isPlayerInitialized()) {
  console.log('播放器已初始化');
}
```

#### setOnErrorCallback(callback: (message: string) => void): void

设置错误回调函数。

**参数：**
- `callback: (message: string) => void` - 错误回调函数

**示例：**
```typescript
this.videoPlayerService.setOnErrorCallback((errorMessage) => {
  console.error('播放器错误:', errorMessage);
  this.handleError(errorMessage);
});
```

#### getCurrentUrl(): string | null

获取当前播放的URL。

**返回值：**
- `string | null` - 当前URL

**示例：**
```typescript
const currentUrl = this.videoPlayerService.getCurrentUrl();
console.log('当前播放URL:', currentUrl);
```

#### resetPlayerState(): void

重置播放器状态（私有方法）。

## CanvasDrawingService

Canvas绘制服务，负责Canvas覆盖层的绘制和同步。

### 类定义

```typescript
export class CanvasDrawingService
```

### 构造函数

```typescript
constructor(originalWidth: number, originalHeight: number)
```

**参数：**
- `originalWidth: number` - 摄像头原始宽度
- `originalHeight: number` - 摄像头原始高度

### 属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `canvas` | `HTMLCanvasElement` | Canvas元素 |
| `ctx` | `CanvasRenderingContext2D` | Canvas 2D上下文 |
| `scaleX` | `number` | X轴缩放比例 |
| `scaleY` | `number` | Y轴缩放比例 |
| `CAMERA_ORIGINAL_WIDTH` | `number` | 摄像头原始宽度 |
| `CAMERA_ORIGINAL_HEIGHT` | `number` | 摄像头原始高度 |

### 方法

#### initCanvas(canvasElement: HTMLCanvasElement): void

初始化Canvas上下文。

**参数：**
- `canvasElement: HTMLCanvasElement` - Canvas元素

**示例：**
```typescript
this.canvasDrawingService.initCanvas(this.overlayCanvas.nativeElement);
```

#### syncCanvasWithVideo(videoElement: HTMLVideoElement): object

同步Canvas尺寸和位置与视频内容。

**参数：**
- `videoElement: HTMLVideoElement` - 视频元素

**返回值：**
- `{ renderedWidth: number, renderedHeight: number, offsetX: number, offsetY: number }` - 渲染信息

**示例：**
```typescript
const renderInfo = this.canvasDrawingService.syncCanvasWithVideo(
  this.videoPlayer.nativeElement
);
console.log('渲染信息:', renderInfo);
```

#### drawAreas(scopePoints: Point[], pipeScopePoints: Point[]): void

绘制警戒区域和管道区域。

**参数：**
- `scopePoints: Point[]` - 警戒区域点数组
- `pipeScopePoints: Point[]` - 管道区域点数组

**示例：**
```typescript
const scopePoints = [
  { x: 100, y: 100 },
  { x: 200, y: 100 },
  { x: 200, y: 200 },
  { x: 100, y: 200 }
];

const pipeScopePoints = [
  { x: 150, y: 150, name: '管道1' },
  { x: 250, y: 150, name: '管道2' }
];

this.canvasDrawingService.drawAreas(scopePoints, pipeScopePoints);
```

#### drawPolygon(points: Point[], fillStyle: string, strokeStyle: string): void

绘制多边形（私有方法）。

**参数：**
- `points: Point[]` - 点数组
- `fillStyle: string` - 填充样式
- `strokeStyle: string` - 边框样式

#### drawPipeScope(points: Point[]): void

绘制管道范围（私有方法）。

**参数：**
- `points: Point[]` - 管道范围点数组

## 数据类型

### Point

点坐标接口。

```typescript
interface Point {
  x: number;      // X坐标
  y: number;      // Y坐标
  name?: string;  // 名称（可选）
}
```

### RequestResult

请求结果接口。

```typescript
interface RequestResult<T> {
  code: number;   // 状态码
  data: T;        // 数据
  msg: string;    // 消息
}
```

## 错误处理

### 错误类型

1. **网络错误**
   - `NETWORK_STATUS_CODE_INVALID` - 无效状态码
   - `NETWORK_EXCEPTION` - 网络异常
   - `NETWORK_TIMEOUT` - 网络超时

2. **媒体错误**
   - `MEDIA_FORMAT_UNSUPPORTED` - 格式不支持
   - `MEDIA_CODEC_UNSUPPORTED` - 编解码器不支持

### 错误处理示例

```typescript
// 设置错误回调
this.videoPlayerService.setOnErrorCallback((errorMessage) => {
  switch (errorMessage) {
    case '视频网络连接异常或超时':
      // 处理网络错误
      this.retryConnection();
      break;
    case '视频格式不支持':
      // 处理格式错误
      this.showFormatError();
      break;
    default:
      // 处理其他错误
      this.showGenericError(errorMessage);
  }
});
```

## 最佳实践

### 1. 服务初始化

```typescript
ngOnInit() {
  // 设置错误回调
  this.videoPlayerService.setOnErrorCallback(this.handleVideoError.bind(this));
  
  // 初始化摄像头
  this.initializeCamera();
}
```

### 2. 资源清理

```typescript
ngOnDestroy() {
  // 停止轮询
  this.cameraInfoService.stopPolling();
  
  // 销毁播放器
  this.videoPlayerService.destroyPlayer();
  
  // 销毁服务
  this.cameraInfoService.destroy();
}
```

### 3. 错误恢复

```typescript
private handleError(error: any) {
  // 停止轮询
  this.cameraInfoService.stopPolling();
  
  // 销毁播放器
  this.videoPlayerService.destroyPlayer();
  
  // 显示错误信息
  this.showError = true;
  this.errorMessage = error;
}
```

### 4. 性能优化

```typescript
// 避免重复初始化
if (!this.videoPlayerService.isPlayerInitialized() || 
    this.videoPlayerService.getCurrentUrl() !== newUrl) {
  this.videoPlayerService.initFlvPlayer(videoElement, newUrl);
}
```

## 注意事项

1. **服务生命周期**：确保在组件销毁时正确清理服务资源
2. **错误处理**：始终设置错误回调函数处理播放器错误
3. **轮询控制**：避免同时启动多个轮询，先停止再启动
4. **内存管理**：长时间使用需要监控内存使用情况
5. **网络优化**：根据网络状况调整轮询间隔和视频质量 