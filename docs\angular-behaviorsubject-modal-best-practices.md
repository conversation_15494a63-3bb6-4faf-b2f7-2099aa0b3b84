# Angular/Ionic 父子组件数据同步之 BehaviorSubject 最佳实践

## 📖 概述

本文档总结了在 Angular/Ionic 项目中，父组件统一管理异步数据并通过 BehaviorSubject 实现弹窗/子组件实时同步的最佳实践。适用于需要全局缓存、弹窗内容实时响应父组件数据变化的场景。

---

## 🎯 典型场景

- **任务执行页**统一请求和缓存关键点数据
- **KeyPointsComponent**弹窗/子页面需实时展示父组件最新数据和加载状态
- 兼容弹窗模式（由父组件传递数据）和独立模式（组件内自行请求数据）

---

## 🔍 问题分析

### 原始问题
- 父组件通过`@Input()`传递普通变量，弹窗打开后，父组件数据变化无法实时同步到弹窗
- 弹窗内容出现“加载中...”不消失、数据不同步等问题

### 根本原因
- `@Input()`传递的是值的快照，非响应式
- 弹窗/子组件实例化后，无法感知父组件后续的数据变化

---

## 💡 解决方案

### 核心思路
- 父组件用`BehaviorSubject`维护数据和loading状态
- 弹窗/子组件通过`@Input()`接收`BehaviorSubject`，在`ngOnInit`中订阅，实时响应父组件变化
- 独立模式下，组件内自行请求数据并维护本地状态

---

## 🏗️ 实现代码

### 1. 父组件（如 ExecutComponent）

```typescript
import { BehaviorSubject } from 'rxjs';

loading$ = new BehaviorSubject<boolean>(false);
keyPoints$ = new BehaviorSubject<KeyPoint[]>([]);

getkeyPoints() {
  this.loading$.next(true);
  this.keyPointService.getKeyPointsByTaskCode(this.task.taskCode)
    .pipe(takeUntil(this.destroy$))
    .subscribe(result => {
      const { code, data, msg } = result;
      if (code === 0) {
        this.keyPoints$.next(data || []);
        this.keyPointManagerService.initKeyPoints(data || []);
      } else {
        this.toastSer.presentToast(msg, 'danger');
      }
      this.loading$.next(false);
    }, () => {
      this.loading$.next(false);
    });
}

async seePoints() {
  await this.modalManagerService.createModalWithGuard('points', {
    component: KeyPointsComponent,
    componentProps: {
      taskCode: this.task.taskCode,
      loading$: this.loading$,
      keyPoints$: this.keyPoints$
    },
    cssClass: 'camera-list-modal'
  });
}
```

---

### 2. 子组件（如 KeyPointsComponent）

```typescript
@Input() loading$: BehaviorSubject<boolean>;
@Input() keyPoints$: BehaviorSubject<KeyPoint[]>;
@Input() showClose = true;
@Input() taskCode: string;

loading = true;
keyPoints: KeyPoint[] = [];
private subscriptions: Subscription[] = [];

ngOnInit() {
  if (this.showClose === false) {
    // 独立模式，组件内自行请求
    this.getkeyPoints();
  } else {
    // 订阅父组件传递的BehaviorSubject
    if (this.loading$) {
      this.subscriptions.push(this.loading$.subscribe(val => this.loading = val));
    }
    if (this.keyPoints$) {
      this.subscriptions.push(this.keyPoints$.subscribe(val => this.keyPoints = val));
    }
  }
}

getkeyPoints() {
  this.loading = true;
  this.keyPointService.getKeyPointsByTaskCode(this.taskCode)
    .pipe(takeUntil(this.destroy$))
    .subscribe(result => {
      const { code, data, msg } = result;
      if (code === 0) {
        this.keyPoints = data || [];
      } else {
        this.toastSer.presentToast(msg, 'danger');
      }
      this.loading = false;
    }, () => {
      this.loading = false;
    });
}

ngOnDestroy(): void {
  this.subscriptions.forEach(sub => sub.unsubscribe());
  this.destroy$.next();
  this.destroy$.complete();
}
```

---

### 3. 模板与样式

```html
<ng-container *ngIf="!loading; else loadingTpl">
  <!-- 关键点内容 -->
</ng-container>
<ng-template #loadingTpl>
  <div class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <div>加载中...</div>
  </div>
</ng-template>
```

```scss
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}
```

---

## 📚 核心知识点

### 1. BehaviorSubject 特性
- 有初始值，始终保存最新值
- 任何时刻订阅都能立刻获得当前值
- 适合做“响应式状态下传”

### 2. 父子组件数据流
- 父组件通过`BehaviorSubject.next()`推送新值
- 子组件通过`subscribe`实时响应

### 3. 独立模式兼容
- 组件可根据业务场景选择“自请求”或“响应式订阅”两种模式

---

## 🔧 实用代码模板

### 父组件
```typescript
loading$ = new BehaviorSubject<boolean>(false);
data$ = new BehaviorSubject<any[]>([]);

getData() {
  this.loading$.next(true);
  // 异步请求...
  this.data$.next(result);
  this.loading$.next(false);
}
```

### 子组件
```typescript
@Input() loading$: BehaviorSubject<boolean>;
@Input() data$: BehaviorSubject<any[]>;

ngOnInit() {
  this.loading$?.subscribe(val => this.loading = val);
  this.data$?.subscribe(val => this.data = val);
}
```

---

## 🚨 常见陷阱与避免方法

### 陷阱1：只传递普通变量，导致数据不同步
- **避免**：始终用BehaviorSubject传递需要实时同步的数据

### 陷阱2：未取消订阅，导致内存泄漏
- **避免**：在`ngOnDestroy`中取消所有订阅

### 陷阱3：独立模式下未维护本地loading
- **避免**：独立模式下组件内自行维护loading和数据

---

## 📋 调试检查清单

- [ ] 父组件数据变化时，弹窗内容能否实时同步？
- [ ] 关闭弹窗时，是否有内存泄漏（订阅未取消）？
- [ ] 独立模式下，组件能否自请求并正确渲染数据？
- [ ] loading状态切换是否流畅？

---

## 🎯 核心记忆要点

1. **“响应式下传”原则**：用BehaviorSubject实现父子组件数据实时同步
2. **“模式兼容”原则**：支持父组件统一管理和组件自请求两种模式
3. **“订阅管理”原则**：所有订阅都要在ngOnDestroy中取消
4. **“UI一致性”原则**：loading、无数据等状态友好提示

---

## 📝 实际应用总结

- 适用于弹窗、抽屉、侧边栏等需要实时响应父组件数据变化的场景
- 便于全局缓存、数据复用和业务扩展
- 是Angular/Ionic企业级项目的推荐实践

---

## 🔗 相关资源

- [RxJS BehaviorSubject 文档](https://rxjs.dev/api/index/class/BehaviorSubject)
- [Angular 官方文档 - 组件交互](https://angular.cn/guide/component-interaction)
- [Ionic Modal 官方文档](https://ionicframework.com/docs/api/modal)

---

**最后更新时间**: 2025-01-09  
**适用版本**: Angular 12+, Ionic 6+ 