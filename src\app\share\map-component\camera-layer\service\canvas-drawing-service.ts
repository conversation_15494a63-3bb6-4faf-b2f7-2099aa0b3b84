export class CanvasDrawingService {
  // Canvas元素
  private canvas: HTMLCanvasElement;
  // Canvas 2D渲染上下文
  private ctx: CanvasRenderingContext2D;
  // X轴缩放比例，用于将原始坐标映射到Canvas
  private scaleX = 1;
  // Y轴缩放比例，用于将原始坐标映射到Canvas
  private scaleY = 1;
  // 摄像头原始视频流的宽高比，用于计算实际渲染区域
  private readonly CAMERA_ORIGINAL_WIDTH: number;
  private readonly CAMERA_ORIGINAL_HEIGHT: number;

  constructor(originalWidth: number, originalHeight: number) {
    this.CAMERA_ORIGINAL_WIDTH = originalWidth;
    this.CAMERA_ORIGINAL_HEIGHT = originalHeight;
  }

  // 初始化Canvas上下文
  initCanvas(canvasElement: HTMLCanvasElement): void {
    this.canvas = canvasElement;
    this.ctx = this.canvas.getContext('2d');
  }

  /**
   * 同步Canvas的尺寸和位置与视频内容的实际渲染区域
   */
  syncCanvasWithVideo(videoElement: HTMLVideoElement): { renderedWidth: number, renderedHeight: number, offsetX: number, offsetY: number } {
    if (!this.canvas || !this.ctx) {
      console.warn('Canvas or context not initialized for syncCanvasWithVideo.');
      return { renderedWidth: 0, renderedHeight: 0, offsetX: 0, offsetY: 0 };
    }

    // 获取视频元素的原始宽度和高度
    const videoOriginalWidth = videoElement.videoWidth;
    const videoOriginalHeight = videoElement.videoHeight;
    // 获取视频元素在布局中的实际可用宽度和高度（受CSS width/height影响）
    const containerWidth = videoElement.clientWidth;
    const containerHeight = videoElement.clientHeight;

    let renderedWidth: number;  // 实际渲染的宽度
    let renderedHeight: number; // 实际渲染的高度
    let offsetX: number;        // 视频内容相对于video元素左上角的X轴偏移
    let offsetY: number;        // 视频内容相对于video元素左上角的Y轴偏移

    // 计算视频内容和容器的宽高比
    const videoAspectRatio = videoOriginalWidth / videoOriginalHeight;
    const containerAspectRatio = containerWidth / containerHeight;

    // 根据object-fit: contain的规则计算视频内容的实际渲染尺寸和偏移
    if (videoAspectRatio > containerAspectRatio) {
      // 视频比容器宽（即视频内容左右两侧会有黑边）
      renderedWidth = containerWidth;
      renderedHeight = containerWidth / videoAspectRatio;
      offsetX = 0; // X轴无偏移
      offsetY = (containerHeight - renderedHeight) / 2; // Y轴居中对齐
    } else {
      // 容器比视频宽（即视频内容上下两侧会有黑边）
      renderedHeight = containerHeight;
      renderedWidth = containerHeight * videoAspectRatio;
      offsetX = (containerWidth - renderedWidth) / 2; // X轴居中对齐
      offsetY = 0; // Y轴无偏移
    }

    // 设置Canvas的内部绘图尺寸为视频内容的实际渲染尺寸
    this.canvas.width = renderedWidth;
    this.canvas.height = renderedHeight;
    // 调整Canvas的CSS样式，使其精确覆盖视频内容的渲染区域
    this.canvas.style.position = 'absolute';   // 确保Canvas可以进行绝对定位
    this.canvas.style.left = `${offsetX}px`;   // 设置Canvas的X轴位置
    this.canvas.style.top = `${offsetY}px`;    // 设置Canvas的Y轴位置
    this.canvas.style.width = `${renderedWidth}px`; // 设置Canvas的宽度
    this.canvas.style.height = `${renderedHeight}px`; // 设置Canvas的高度

    // 计算缩放比例，用于将原始坐标（如1920x1080）映射到当前Canvas尺寸
    this.scaleX = this.canvas.width / this.CAMERA_ORIGINAL_WIDTH;
    this.scaleY = this.canvas.height / this.CAMERA_ORIGINAL_HEIGHT;

    // 返回实际渲染的宽度和高度，以及偏移量
    return { renderedWidth, renderedHeight, offsetX, offsetY };
  }

  /**
   * 绘制Canvas上的区域
   */
  drawAreas(scopePoints: Point[] | null, pipeScopePoints: Point[] | null): void {
    // 如果Canvas上下文未初始化，则不进行绘制
    if (!this.ctx) return;

    // 清除Canvas上的所有绘图
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // 绘制scope区域（警戒区域）
    if (scopePoints) {
      this.drawPolygon(scopePoints, 'rgba(255, 0, 0, 0.3)', 'red');
    }
    // 绘制pipeScope区域（管道检测区域）
    if (pipeScopePoints) {
      this.drawPipeScope(pipeScopePoints);
    }
  }

  /**
   * 绘制多边形
   * @param points 多边形的顶点数组，每个点包含x, y坐标
   * @param fillStyle 填充样式
   * @param strokeStyle 边框样式
   */
  private drawPolygon(points: Point[], fillStyle: string, strokeStyle: string): void {
    if (!points.length) return;
    this.ctx.beginPath();
    this.ctx.moveTo(points[0].x * this.scaleX, points[0].y * this.scaleY);
    for (let i = 1; i < points.length; i++) {
      this.ctx.lineTo(points[i].x * this.scaleX, points[i].y * this.scaleY);
    }
    this.ctx.closePath();
    this.ctx.fillStyle = fillStyle;
    this.ctx.fill();
    this.ctx.strokeStyle = strokeStyle;
    this.ctx.stroke();
  }

  /**
   * 绘制管道范围（可能包含文本标签）
   * @param points 管道范围的顶点数组
   */
  private drawPipeScope(points: Point[]): void {
    if (!points.length) return;
    // 开始绘制路径
    this.ctx.beginPath();
    // 设置管道范围的边框颜色为蓝色
    this.ctx.strokeStyle = 'blue';
    // 设置管道范围的边框宽度为2像素
    this.ctx.lineWidth = 2;
    let currentX = 0;
    let currentY = 0;
    // 遍历管道范围的每个点
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      // 如果点包含名称，则绘制文本标签
      if (point.name) {
        // 设置文本颜色为蓝色
        this.ctx.fillStyle = 'blue';
        // 设置文本字体为Arial，大小为20像素
        this.ctx.font = '20px Arial';
        // 在当前点上方绘制文本标签
        this.ctx.fillText(point.name, currentX, currentY - 10);
      } else {
        // 绘制线段，按比例缩放坐标
        const sx = point.x * this.scaleX;
        const sy = point.y * this.scaleY;
        if (i === 0) {
          this.ctx.moveTo(sx, sy); // 移动到起点
        } else {
          this.ctx.lineTo(sx, sy); // 连接到当前点
        }
        currentX = sx;  // 更新当前点的X坐标
        currentY = sy;  // 更新当前点的Y坐标
      }
    }
    this.ctx.stroke(); // 完成路径绘制
  }
}

interface Point {
  x: number;
  y: number;
  name?: string; // 管道范围可能包含名称
}