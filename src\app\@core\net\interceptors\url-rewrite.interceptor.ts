import { Injectable } from '@angular/core';
import { <PERSON>ttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable()
export class UrlRewriteInterceptor implements HttpInterceptor {
  // 需要过滤的 URL（不参与URL重写）
  private readonly filterUrls: string[] = [
    '/work-user/api/v2/userCenter/checkToken',
    '/file/fileDes',
  ];

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // 如果URL在过滤列表中，则不进行重写
    if (this.filterUrls.some(url => req.urlWithParams.includes(url))) {
      return next.handle(req);
    }

    // 解析URL和查询参数
    const url = new URL(req.url, 'http://dummy'); // 使用dummy基础URL来安全解析
    const path = url.pathname;
    const apiBaseUrl = `${environment.production ? 'https' : 'http'}://${environment.api.ip}:${environment.api.port}`;

    let newUrl = '';

    // 合并GET请求中所有重复的query参数为逗号分隔
    if (req.method === 'GET' && url.search) {
      const params = new URLSearchParams(url.search);
      const mergedParams: { [key: string]: string[] } = {};
      params.forEach((value, key) => {
        if (!mergedParams[key]) {
          mergedParams[key] = [];
        }
        mergedParams[key].push(value);
      });
      const newSearch = Object.keys(mergedParams)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(mergedParams[key].join(','))}`)
        .join('&');
      newUrl = `${apiBaseUrl}${path}${newSearch ? '?' + newSearch : ''}`;
    } else {
      // 对于非GET请求，直接拼接
      newUrl = `${apiBaseUrl}${path}${url.search}`;
    }

    const newReq = req.clone({ url: newUrl });
    return next.handle(newReq);
  }

}