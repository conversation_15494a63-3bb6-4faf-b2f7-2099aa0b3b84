# 摄像头组件快速开始指南

## 概述

摄像头组件是一个功能完整的实时视频流播放组件，支持FLV格式、Canvas覆盖层、全屏模式等功能。

## 快速开始

### 1. 基本使用

```typescript
// 在父组件中
import { CameraLayerComponent } from './camera-layer/camera-layer.component';

@Component({
  // ...
})
export class ParentComponent {
  cameraInfo = {
    id: 'camera-001',
    name: '摄像头1'
  };
}
```

```html
<!-- 在模板中 -->
<app-camera-layer [modelInfo]="cameraInfo"></app-camera-layer>
```

### 2. 错误处理

组件现在具有强大的错误处理和自动恢复机制：

#### 自动重试
- MediaSource错误会自动重试最多3次
- 每次重试间隔递增（2秒、4秒、6秒）
- 重试过程中显示友好的提示信息

#### 手动重试
用户可以通过界面按钮进行手动重试：
- **重试视频**：仅重新初始化视频播放器
- **重新加载**：完全重新初始化摄像头和播放器

#### 错误类型识别
组件能够识别并处理多种错误类型：
- 网络连接错误
- 视频格式错误
- 播放器初始化错误
- MediaSource数据错误

### 3. 配置选项

#### 环境配置
```typescript
// environment.ts
export const environment = {
  production: false,
  api: {
    ip: 'localhost',
    port: '8080'
  }
};
```

#### 播放器配置
```typescript
// VideoPlayerService中的默认配置
const flvConfig = {
  type: 'flv',
  url: videoUrl,
  isLive: true,
  hasAudio: true,
  hasVideo: true,
  enableStashBuffer: false,
  stashInitialSize: 128,
  enableWorker: true,
  lazyLoad: true,
  seekType: 'range',
  cors: true,
  withCredentials: false,
  timeout: 10000
};
```

## 功能特性

### 1. 视频播放
- ✅ FLV.js实时流播放
- ✅ 自动播放支持
- ✅ 错误自动恢复
- ✅ 手动重试机制

### 2. Canvas覆盖层
- ✅ 警戒区域绘制
- ✅ 管道检测区域
- ✅ 自动坐标转换
- ✅ 响应式适配

### 3. 全屏模式
- ✅ 横竖屏切换
- ✅ 屏幕方向锁定
- ✅ 响应式布局

### 4. 错误处理
- ✅ 自动重试机制
- ✅ 错误类型识别
- ✅ 用户友好提示
- ✅ 资源自动清理

## 最佳实践

### 1. 错误处理
```typescript
// 在父组件中监听错误
@Component({
  // ...
})
export class ParentComponent {
  onCameraError(error: string) {
    console.log('摄像头错误:', error);
    // 可以在这里添加自定义错误处理逻辑
  }
}
```

### 2. 性能优化
- 避免频繁切换摄像头
- 及时销毁不需要的组件实例
- 监控内存使用情况

### 3. 用户体验
- 提供清晰的错误提示
- 支持多种重试方式
- 保持界面响应性

## 故障排除

如果遇到问题，请参考 [故障排除指南](./TROUBLESHOOTING.md)。

常见问题：
1. **视频无法播放**：检查网络连接和视频地址
2. **Canvas不显示**：检查坐标数据和Canvas同步
3. **全屏模式异常**：检查浏览器兼容性

## 更新日志

### v1.1.0 (最新)
- ✅ 新增MediaSource错误自动重试机制
- ✅ 改进错误处理和用户界面
- ✅ 增加手动重试选项
- ✅ 优化播放器配置

### v1.0.0
- ✅ 基础视频播放功能
- ✅ Canvas覆盖层支持
- ✅ 全屏模式切换
- ✅ 基本错误处理

## 前置要求

### 1. 环境要求

- Angular 12+
- Ionic 6+
- Node.js 14+
- 支持FLV.js的现代浏览器

### 2. 依赖安装

确保项目中已安装以下依赖：

```bash
npm install flv.js
npm install @ionic/angular
```

### 3. 环境配置

在 `src/environments/environment.ts` 中配置API地址：

```typescript
export const environment = {
  production: false,
  api: {
    ip: 'localhost',    // 替换为您的服务器IP
    port: '8080'        // 替换为您的服务器端口
  }
};
```

## 快速集成

### 1. 导入组件

在您的模块中导入摄像头组件：

```typescript
import { CameraLayerComponent } from './share/map-component/camera-layer/camera-layer.component';

@NgModule({
  declarations: [
    CameraLayerComponent
  ],
  imports: [
    // 其他导入
  ]
})
export class YourModule { }
```

### 2. 基本使用

在您的组件模板中使用摄像头组件：

```html
<app-camera-layer [modelInfo]="cameraInfo"></app-camera-layer>
```

在组件类中定义摄像头信息：

```typescript
export class YourComponent {
  cameraInfo = {
    id: 'camera-001',
    name: '摄像头1'
  };
}
```

### 3. 模态框使用

使用Ionic模态框显示摄像头：

```typescript
import { ModalController } from '@ionic/angular';
import { CameraLayerComponent } from './share/map-component/camera-layer/camera-layer.component';

export class YourComponent {
  constructor(private modalCtrl: ModalController) {}

  async openCamera(cameraId: string) {
    const modal = await this.modalCtrl.create({
      component: CameraLayerComponent,
      componentProps: {
        modelInfo: { id: cameraId }
      },
      cssClass: 'camera-modal'
    });
    
    return await modal.present();
  }
}
```

## 配置选项

### 1. 轮询间隔配置

```typescript
// 在CameraInfoService中自定义轮询间隔
this.cameraInfoService.startPolling(
  cameraId,
  this.handleCameraData.bind(this),
  this.handleError.bind(this),
  5000 // 5秒轮询一次
);
```

### 2. 视频播放器配置

```typescript
// 在VideoPlayerService中自定义FLV.js配置
const flvConfig = {
  type: 'flv',
  url: videoUrl,
  isLive: true,
  hasAudio: true,
  hasVideo: true,
  enableStashBuffer: false,
  stashInitialSize: 128,
  enableWorker: true,
  lazyLoad: true,
  seekType: 'range'
};
```

### 3. Canvas绘制配置

```typescript
// 自定义Canvas绘制服务
const canvasService = new CanvasDrawingService(1920, 1080); // 自定义分辨率
```

## 使用示例

### 1. 基本摄像头显示

```typescript
// 组件类
export class CameraExampleComponent {
  cameraInfo = {
    id: 'camera-001',
    name: '前门摄像头'
  };

  onCameraError(error: string) {
    console.error('摄像头错误:', error);
  }
}
```

```html
<!-- 模板 -->
<ion-content>
  <app-camera-layer 
    [modelInfo]="cameraInfo"
    (error)="onCameraError($event)">
  </app-camera-layer>
</ion-content>
```

### 2. 多摄像头切换

```typescript
export class MultiCameraComponent {
  cameras = [
    { id: 'camera-001', name: '摄像头1' },
    { id: 'camera-002', name: '摄像头2' },
    { id: 'camera-003', name: '摄像头3' }
  ];
  
  currentCamera = this.cameras[0];

  switchCamera(camera: any) {
    this.currentCamera = camera;
  }
}
```

```html
<ion-content>
  <!-- 摄像头选择器 -->
  <ion-segment [(ngModel)]="currentCamera" (ionChange)="switchCamera($event.detail.value)">
    <ion-segment-button *ngFor="let camera of cameras" [value]="camera">
      {{ camera.name }}
    </ion-segment-button>
  </ion-segment>

  <!-- 摄像头显示 -->
  <app-camera-layer [modelInfo]="currentCamera"></app-camera-layer>
</ion-content>
```

### 3. 全屏模式

```typescript
export class FullscreenCameraComponent {
  async openFullscreenCamera(cameraId: string) {
    const modal = await this.modalCtrl.create({
      component: CameraLayerComponent,
      componentProps: {
        modelInfo: { id: cameraId }
      },
      cssClass: 'fullscreen-camera-modal',
      backdropDismiss: false
    });
    
    return await modal.present();
  }
}
```

```scss
// 全屏样式
.fullscreen-camera-modal {
  --width: 100vw;
  --height: 100vh;
  --border-radius: 0;
}
```

## 错误处理

### 1. 基本错误处理

```typescript
export class CameraWithErrorHandlingComponent {
  showError = false;
  errorMessage = '';

  handleCameraError(error: any) {
    this.showError = true;
    this.errorMessage = error;
    
    // 自动重试
    setTimeout(() => {
      this.retryCamera();
    }, 5000);
  }

  retryCamera() {
    this.showError = false;
    this.errorMessage = '';
    // 重新初始化摄像头
  }
}
```

### 2. 网络状态监控

```typescript
export class NetworkAwareCameraComponent {
  isOnline = navigator.onLine;

  constructor() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.retryCamera();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.handleOffline();
    });
  }

  handleOffline() {
    this.showError = true;
    this.errorMessage = '网络连接已断开，请检查网络设置';
  }
}
```

## 性能优化

### 1. 懒加载

```typescript
// 使用懒加载减少初始加载时间
const CameraLayerComponent = () => import('./camera-layer.component').then(m => m.CameraLayerComponent);

// 在路由中使用
{
  path: 'camera/:id',
  loadComponent: CameraLayerComponent
}
```

### 2. 预加载

```typescript
// 预加载下一个摄像头
preloadNextCamera(currentIndex: number) {
  const nextIndex = (currentIndex + 1) % this.cameras.length;
  const nextCamera = this.cameras[nextIndex];
  
  // 预加载摄像头信息
  this.cameraInfoService.getCameraInfo(nextCamera.id);
}
```

### 3. 内存管理

```typescript
ngOnDestroy() {
  // 清理资源
  this.videoPlayerService.destroyPlayer();
  this.cameraInfoService.destroy();
  
  // 清除定时器
  if (this.retryTimer) {
    clearTimeout(this.retryTimer);
  }
}
```

## 测试

### 1. 单元测试

```typescript
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CameraLayerComponent } from './camera-layer.component';

describe('CameraLayerComponent', () => {
  let component: CameraLayerComponent;
  let fixture: ComponentFixture<CameraLayerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CameraLayerComponent ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CameraLayerComponent);
    component = fixture.componentInstance;
    component.modelInfo = { id: 'test-camera' };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize camera', () => {
    expect(component.cameraInfo).toBeDefined();
  });
});
```

### 2. 集成测试

```typescript
// 测试摄像头切换
it('should switch camera correctly', () => {
  const newCamera = { id: 'camera-002', name: '摄像头2' };
  component.modelInfo = newCamera;
  fixture.detectChanges();
  
  expect(component.videoUrl).toContain('camera-002');
});
```

## 部署

### 1. 生产环境配置

```typescript
// environment.prod.ts
export const environment = {
  production: true,
  api: {
    ip: 'your-production-server.com',
    port: '443'
  }
};
```

### 2. 构建优化

```bash
# 生产构建
ng build --prod

# 启用AOT编译
ng build --aot

# 启用代码分割
ng build --prod --build-optimizer
```

### 3. 服务器配置

确保服务器支持：
- HTTPS协议（必需）
- CORS配置
- 视频流传输
- WebSocket连接（如需要）

## 常见问题

### Q: 视频无法播放怎么办？

A: 检查以下几点：
1. 确保浏览器支持FLV.js
2. 检查网络连接
3. 验证视频流地址
4. 检查HTTPS协议

### Q: Canvas覆盖层不显示？

A: 检查以下几点：
1. 确保视频已加载完成
2. 检查Canvas同步逻辑
3. 验证坐标数据格式
4. 检查CSS样式设置

### Q: 轮询停止工作？

A: 检查以下几点：
1. 网络连接状态
2. 服务器响应
3. 错误处理逻辑
4. 定时器清理

## 下一步

- 阅读 [README.md](./README.md) 了解详细功能
- 查看 [API.md](./API.md) 了解完整API
- 参考 [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) 解决常见问题
- 查看示例代码了解更多用法

## 支持

如果您在使用过程中遇到问题，请：
1. 查看故障排除指南
2. 检查浏览器控制台错误
3. 联系开发团队获取支持 