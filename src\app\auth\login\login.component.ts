import { AfterContentInit, Component, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LoadingController, Platform } from '@ionic/angular';
import { JSEncrypt } from 'jsencrypt/lib/JSEncrypt';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { RequestResult } from 'src/app/@core/base/request-result';
import { StorageService } from 'src/app/@core/providers/storage.service';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { AuthService } from '../auth.service';
import { LoginInfo, LoginMsg, RSAPUBLICKEY } from '../class/auth';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements AfterContentInit, OnDestroy {
  // 登录表单
  loginForm: FormGroup;
  // 登录信息
  loginInfo: LoginInfo = new LoginInfo();
  // 动画
  loading: any;
  pwShow = 'password';
  isShow = 'eye-off';
  smallScreen = false;
  // 公钥
  publicKey = RSAPUBLICKEY;
  // 登录状态控制
  isLoggingIn = false;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    private formBuilder: FormBuilder, private toastSer: ToastService,
    private loadingCtrl: LoadingController, private router: Router,
    private authSer: AuthService, private userSer: UserInfoService,
    private storage: StorageService, private platform: Platform
  ) {
    this.getUserName();
    const screenHeight = this.platform.height();
    if (screenHeight === 356) {
      this.smallScreen = true;
    }
  }

  async getUserName(): Promise<void> {
    // 使用Promise.all确保两个异步操作都完成
    const [account, password] = await Promise.all([
      this.storage.get('account').toPromise(),
      this.storage.get('password').toPromise()
    ]);

    // 设置表单值
    this.setFormValues('account', account);
    this.setFormValues('userPasd', password);
  }

  private setFormValues(controlName: string, value: string | null): void {
    const control = this.loginForm.get(controlName);
    if (control) {
      control.setValue(value ? atob(value) : '');
    }
  }

  /**
   * 密码可见
   */
  onPasswordShow(): void {
    this.pwShow === 'password' ? this.isShow = 'eye-outline' : this.isShow = 'eye-off';
    this.pwShow === 'password' ? this.pwShow = 'text' : this.pwShow = 'password';
  }

  /*
   * 自动登录
   */
  onAutoLoginChange(val): void {
    if (val === false) {
      this.storage.set('password', '').subscribe();
    }
  }

  async ngAfterContentInit(): Promise<void> {
    // 登录表单
    this.loginForm = this.formBuilder.group({
      account: [this.loginInfo.account, [Validators.required]],
      userPasd: [this.loginInfo.userPasd, [Validators.required, Validators.minLength(4)]],
      autoLogin: [this.loginInfo.autoLogin]
    });
  }
  /**
   * 登录
   */
  async onLogin(val): Promise<any> {
    // 防止重复点击
    if (this.isLoggingIn) {
      return;
    }

    this.isLoggingIn = true;
    this.loginInfo = val;

    try {
      const encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.publicKey);
      const password: any = encrypt.encrypt(this.loginForm.get('userPasd').value);

      // 创建loading
      this.loading = await this.loadingCtrl.create({
        message: '登录中...'
      });
      await this.loading.present();

      this.authSer.loginRequest({ account: val.account, userPasd: password })
        .pipe(takeUntil(this.destroy$))
        .subscribe(this.onLoginSuccess, this.onLoginErro);
    } catch (error) {
      // 异常兜底处理
      this.toastSer.presentToast('登录异常，请重试', 'danger');
      this.dismissLoading();
      this.isLoggingIn = false;
    }
  }

  /**
   * 登录成功
   */
  onLoginSuccess = (result: RequestResult<LoginMsg>) => {
    const { code, data } = result;

    // 重置登录状态，无论成功失败都应重置
    this.isLoggingIn = false;
    // 关闭loading
    this.dismissLoading();

    if (code !== 0) {
      this.onLoginErro(result);
      return;
    }

    // 需要强制修改密码，跳转后不做后续处理
    if (data.isUpdatePassWord) {
      this.router.navigate(['/editPassword'], { queryParams: { isModal: false, account: data.account } })
      return;
    }

    // 存储账号
    this.storage.set('account', btoa(this.loginInfo.account)).subscribe();

    // 存储密码（仅在自动登录时）
    if (this.loginInfo.autoLogin) {
      this.storage.set('password', btoa(this.loginInfo.userPasd)).subscribe();
    }

    // 处理部门信息
    const depCodeList = data.depCodeList || [];
    const userInfo = { ...data };
    if (Array.isArray(depCodeList) && depCodeList.length > 0) {
      userInfo.depCode = depCodeList[0];
    }

    // 用户信息入本地服务
    this.userSer.login(userInfo);

    // 跳转首页
    this.router.navigateByUrl('/tabs/home');
  }


  /**
   * 登录错误
   */
  onLoginErro = (error) => {
    const errorMsg = error?.msg || '网络连接错误';
    this.toastSer.presentToast(errorMsg, 'danger');
    this.loginForm.get('userPasd').setValue('');
    this.dismissLoading();
    // 重置登录状态
    this.isLoggingIn = false;
  }

  /**
   * 安全关闭loading
   */
  private dismissLoading(): void {
    if (this.loading) {
      this.loading.dismiss().catch(() => {
        // 忽略dismiss时的错误，避免影响正常流程
      }).finally(() => {
        this.loading = null; // 清空引用，避免重复关闭
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

}
