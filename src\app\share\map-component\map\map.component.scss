.ost-map {
  position: absolute;
  width: 100%;
  height: 100%;
}
.ost-map-layout {
  pointer-events: none;
  touch-action: none;
  width: 100%;
  height: 100%;
}

ost-tools-bar {
  margin-right: 8px;
  margin-bottom: 60px;
}

// 位置信息小工具样式
.location-info-widget {
  position: absolute;
  top: 16px;
  left: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  z-index: 1000;
  pointer-events: all;
  padding: 8px 12px;
  // 位置信息小工具显示控制
  // display: none !important; // 注释掉强制隐藏

  .location-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 12px;
      color: #666;
      min-width: 35px;
    }

    .value {
      font-size: 12px;
      color: #333;
      font-weight: 500;
      font-family: 'Courier New', monospace;
    }
  }

  .refresh-icon {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    background: #3880ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(56, 128, 255, 0.3);
    transition: all 0.2s ease;

    ion-icon {
      color: white;
      font-size: 14px;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}


