import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatPercentage'
})
export class FormatPercentagePipe implements PipeTransform {
  transform(value: string): string {
    if (!value) {
      return '';
    }

    // 去掉百分号，转换为数字
    const numericValue = parseFloat(value.replace('%', ''));

    // 获取小数点后的长度
    const decimalPart = value.split('.')[1];
    const decimalLength = decimalPart ? decimalPart.length : 0;
    
    // 只有当小数点后的长度大于2时才进行格式化
    if (decimalLength > 2) {
      const formattedValue = numericValue.toFixed(2);
      return `${formattedValue}%`;
    } else {
      // 如果小数点后的长度不大于2，直接返回原字符串
      return value;
    }
  }
}