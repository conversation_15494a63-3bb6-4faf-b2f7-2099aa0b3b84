import { Injectable } from '@angular/core';
import { BoundaryResult, GestureConfig } from './image-gesture.model';

/**
 * 边界计算工具
 * 负责计算图片拖拽和缩放的边界限制
 */
@Injectable({
  providedIn: 'root'
})
export class BoundaryCalculatorService {

  /**
   * 计算拖拽边界（增强版）
   * 优化边界计算逻辑，确保在各种屏幕尺寸和图片比例下都能正确工作
   */
  calculateDragBounds(
    containerRect: DOMRect,
    scaledWidth: number,
    scaledHeight: number,
    config: GestureConfig,
    currentScale: number = 1
  ): BoundaryResult {
    // 如果图片处于初始大小或更小，不允许任何移动
    if (currentScale <= 1) {
      return {
        minTranslateX: 0,
        maxTranslateX: 0,
        minTranslateY: 0,
        maxTranslateY: 0,
        debug: {
          containerSize: { width: containerRect.width, height: containerRect.height },
          scaledSize: { width: scaledWidth, height: scaledHeight },
          minVisibleSize: { width: scaledWidth, height: scaledHeight },
          minVisibleRatio: 1
        }
      };
    }

    // 设置最小可见区域比例（图片必须保持至少指定比例可见）
    const minVisibleRatio = config.minVisibleRatio || 0.25;

    // 确保最小可见比例在合理范围内
    const clampedMinVisibleRatio = Math.max(0.1, Math.min(0.8, minVisibleRatio));

    // 计算最小可见尺寸
    const minVisibleWidth = scaledWidth * clampedMinVisibleRatio;
    const minVisibleHeight = scaledHeight * clampedMinVisibleRatio;

    // 计算水平和垂直方向的边界
    const xBounds = this.calculateAxisBounds(scaledWidth, containerRect.width, minVisibleWidth);
    const yBounds = this.calculateAxisBounds(scaledHeight, containerRect.height, minVisibleHeight);

    return {
      minTranslateX: xBounds.min,
      maxTranslateX: xBounds.max,
      minTranslateY: yBounds.min,
      maxTranslateY: yBounds.max,
      // 返回额外的调试信息
      debug: {
        containerSize: { width: containerRect.width, height: containerRect.height },
        scaledSize: { width: scaledWidth, height: scaledHeight },
        minVisibleSize: { width: minVisibleWidth, height: minVisibleHeight },
        minVisibleRatio: clampedMinVisibleRatio
      }
    };
  }

  /**
   * 计算单轴边界
   */
  private calculateAxisBounds(
    scaledSize: number,
    containerSize: number,
    minVisibleSize: number
  ): { max: number; min: number } {
    if (scaledSize <= containerSize) {
      // 图片比容器小或等大，允许少量移动
      const allowedMovement = Math.min(20, (containerSize - scaledSize) / 2);
      return { max: allowedMovement, min: -allowedMovement };
    } else {
      // 图片比容器大，计算允许的最大移动距离
      const overflowSize = scaledSize - containerSize;
      const baseMaxMove = overflowSize / 2;
      const visibilityConstraintMove = (scaledSize - minVisibleSize) / 2;
      const finalMaxMove = Math.min(baseMaxMove, visibilityConstraintMove);
      return { max: finalMaxMove, min: -finalMaxMove };
    }
  }

  /**
   * 通用边界约束方法
   */
  constrainToBounds(x: number, y: number, bounds: BoundaryResult): { x: number; y: number } {
    return {
      x: Math.max(bounds.minTranslateX, Math.min(bounds.maxTranslateX, x)),
      y: Math.max(bounds.minTranslateY, Math.min(bounds.maxTranslateY, y))
    };
  }

  /**
   * 检查位置是否在边界内
   */
  isWithinBounds(x: number, y: number, bounds: BoundaryResult): boolean {
    return x >= bounds.minTranslateX &&
      x <= bounds.maxTranslateX &&
      y >= bounds.minTranslateY &&
      y <= bounds.maxTranslateY;
  }

  /**
   * 检查位置是否需要边界调整
   */
  needsBoundaryAdjustment(x: number, y: number, bounds: BoundaryResult): boolean {
    return x < bounds.minTranslateX ||
      x > bounds.maxTranslateX ||
      y < bounds.minTranslateY ||
      y > bounds.maxTranslateY;
  }

  /**
   * 计算到边界的距离
   */
  getDistanceToBounds(x: number, y: number, bounds: BoundaryResult): {
    left: number;
    right: number;
    top: number;
    bottom: number;
  } {
    return {
      left: x - bounds.minTranslateX,
      right: bounds.maxTranslateX - x,
      top: y - bounds.minTranslateY,
      bottom: bounds.maxTranslateY - y
    };
  }

  /**
   * 获取最近的有效位置
   */
  getClosestValidPosition(x: number, y: number, bounds: BoundaryResult): { x: number; y: number } {
    return this.constrainToBounds(x, y, bounds);
  }

  /**
   * 计算缩放边界
   */
  calculateScaleBounds(config: GestureConfig): { min: number; max: number } {
    return {
      min: config.minScale || 0.8,
      max: config.maxScale || 5
    };
  }

  /**
   * 约束缩放值
   */
  constrainScale(scale: number, config: GestureConfig): number {
    const bounds = this.calculateScaleBounds(config);
    return Math.max(bounds.min, Math.min(bounds.max, scale));
  }
}
