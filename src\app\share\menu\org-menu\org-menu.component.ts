import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { map } from 'rxjs/operators';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { OstTreeListComponent } from '../../ost-tree-list/ost-tree-list.component';
import { OstTreeListItem } from '../../ost-tree-list/ost-tree-list.service';
import { ShareModuleService } from '../../share.service';
import { Platform } from '@ionic/angular';

@Component({
  selector: 'ost-org-menu',
  templateUrl: './org-menu.component.html',
  styleUrls: ['./org-menu.component.scss'],
})
export class OrgMenuComponent implements OnInit {
  @ViewChild('menu', { static: false }) menu: OstTreeListComponent;
  // 是否默认选中
  @Input() isDefaultSelect: boolean = true;
  @Input() depCode: string;
  @Input() interfaceUrl: string;
  @Input() showSearch: boolean = true;
  @Input() leafOnly: boolean = false;
  @Input() showDirBtn = false;
  @Input() dirBtnText = '';
  @Input() showSubBtn = false;
  @Input() subBtnText = '';
  // 自动展开层级数，0表示不自动展开，-1表示展开所有层级
  @Input() autoExpandLevels: number = 0;

  @Output() toggleSubMenu = new EventEmitter<any>();
  @Output() itemClick = new EventEmitter<any>();
  @Output() loadingDate = new EventEmitter<any>();
  @Output() subItemClick = new EventEmitter<any>();

  loading: boolean;
  items: OstTreeListItem[] = [];
  orgTree: any;
  screenHeight: any;
  // 搜索参数
  params = '';
  constructor(
    public cd: ChangeDetectorRef, public userSer: UserInfoService,
    public shareSer: ShareModuleService, public platform: Platform
  ) { }

  ngOnInit(): void {
    this.screenHeight = this.platform.height() - 56 - (this.showSearch ? 50 : 0);
    if (!!this.interfaceUrl) {
      this.userSer.isLogin() ? this.getRequest(this.interfaceUrl, this.userSer.depCode) : this.getRequest(this.interfaceUrl, '');
    } else {
      this.userSer.isLogin() ? this.loadMenuTree(this.userSer.depCode) : this.loadMenuTree('');
    }
  }

  getRequest(url: string, id: string) {
    if (url == '/deviceType/tree') {
      this.specialTreeData(url, id)
    } else {
      this.shareSer.getRequest({ interfaceUrl: url })
        .pipe(map((data) => this.transformationOrg(data)))
        .subscribe(res => {
          this.orgTree = res;
          this.items = res;
          this.loadingDate.emit(this.items);
          if (this.isDefaultSelect) {
            // 确保数据加载完成后调用
            setTimeout(() => this.setSelectItemById(id), 100);
          }
          this.loading = false;
          this.cd.markForCheck();
        }, error => {
          this.loading = false;
          this.cd.markForCheck();
        });
    }
  }

  /**
   * 特殊处理树形数据
   */
  specialTreeData(url: string, id: string) {
    this.shareSer.getRequest({ interfaceUrl: url })
      .subscribe(res => {
        const data = this.flattenDeviceTypes(res.data);
        const tree = this.transformation(data);
        this.orgTree = tree;
        this.items = tree;
        this.loadingDate.emit(this.items);
        if (this.isDefaultSelect) {
          // 确保数据加载完成后调用
          setTimeout(() => this.setSelectItemById(id), 100);
        }
        this.loading = false;
        this.cd.markForCheck();
      }, error => {
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  /**
   * 重置
   */
  onReset(): void {
    this.params = '';
    this.items = this.orgTree;
  }

  /**
   * 搜索
   */
  onSearch() {
    // 过滤并显示匹配结果的树形结构
    if (this.params !== '') {
      const searchData = this.filterTree(this.orgTree, this.params);
      this.items = searchData;
    }
  }

  /**
   * 过滤搜索
   * @param query
   * @param nodes
   */
  filterTree(nodes: any[], query: string): any[] {
    let results: any[] = [];
    nodes.forEach(node => {
      // 如果该节点的标题包含查询字符串
      if (node.title.toLowerCase().includes(query.toLowerCase())) {
        // 设置节点为已展开状态
        node.expanded = true;
        // 将节点添加至结果数组
        results.push(node);
      }
      // 如果节点有子节点
      if (node.children) {
        // 对子节点进行递归模糊搜索
        const childResults = this.filterTree(node.children, query);
        // 如果子节点中有匹配且当前节点尚未被添加到结果数组中
        if (childResults.length > 0 && !results.find(res => res.name === node.name)) {
          // 添加该节点至结果数组
          results.push({ ...node, expanded: true, children: childResults });
        }
      }
    });
    /* 返回结果数组 */
    return results;
  }

  setSelectItemById(id: string): void {
    if (!this.menu) {
      // 如果 menu 尚未加载，稍后重试
      setTimeout(() => this.setSelectItemById(id), 100);
      return;
    }
    const selectItem = this.items.find(itme => (itme.data.depCode === id));
    if (selectItem) {
      selectItem.expanded = true;
      this.menu.setSelectItem(selectItem);
      this.menu.onItemClick(selectItem);
      this.cd.markForCheck();
    }
  }

  setSelectItem(item: OstTreeListItem): void {
    this.menu.setSelectItem(item);
  }

  onToggleSubMenu(item: OstTreeListItem): void {
    this.toggleSubMenu.emit(item);
  }

  onItemClick(item: OstTreeListItem): void {
    this.itemClick.emit(item);
  }

  onSubItemClick(item: OstTreeListItem): void {
    this.itemClick.emit(item);
  }
  /**
   * 请求部门树数据
   */
  loadMenuTree(id: string): void {
    this.shareSer.orgList()
      .pipe(map((data) => this.transformationOrg(data)))
      .subscribe(res => {
        this.orgTree = res;
        this.items = res;
        this.loadingDate.emit(this.items);
        if (this.isDefaultSelect) {
          // 确保数据加载完成后调用
          setTimeout(() => this.setSelectItemById(id), 100);
        }
        this.loading = false;
        this.cd.markForCheck();
      }, error => {
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  /**
   * 树形结构 打平
   */
  flattenOrgMenu(orgData: OrgItem[], parentCode: string | null = null): OrgItem[] {
    const flatMenu: OrgItem[] = [];
    for (const item of orgData) {
      const flatItem: OrgItem = {
        depCode: item.depCode,
        depLevel: item.depLevel,
        depLevelLabel: item.depLevelLabel,
        depName: item.depName,
        id: item.id,
        isDelete: item.isDelete,
        parentCode: item.parentCode,
        sortNum: item.sortNum
      };

      flatMenu.push(flatItem);

      if (item.children && item.children.length > 0) {
        flatMenu.push(...this.flattenOrgMenu(item.children, item.depCode));
      }
    }
    return flatMenu;
  }

  /**
   * 数据转换
   */
  transformationOrg(res: any): OstTreeListItem[] {
    // 转平级
    const data = this.flattenOrgMenu(res.data);
    const result: OstTreeListItem[] = [];
    const maps = {};
    data.forEach(i => {
      const menuItem: OstTreeListItem = {
        title: i.depName,
        data: i
      };
      maps[i.depCode] = menuItem;
    });
    data.forEach(i => {
      const parent = maps[i.parentCode] as OstTreeListItem;
      if (parent) {
        if (parent.children) {
          parent.children.push(maps[i.depCode]);
        } else {
          parent.children = [];
          parent.children.push(maps[i.depCode]);
        }
      } else {
        result.push(maps[i.depCode]);
      }
    });

    // 自动展开指定层级的节点
    if (this.autoExpandLevels > 0 || this.autoExpandLevels === -1) {
      this.autoExpandNodes(result, 0);
    }

    return result;
  }

  transformation(data: any) {
    const result: OstTreeListItem[] = [];
    const maps = {};
    data.forEach(i => {
      const menuItem: OstTreeListItem = {
        title: i.depName,
        data: i
      };
      maps[i.depCode] = menuItem;
    });
    data.forEach(i => {
      const parent = maps[i.parentCode] as OstTreeListItem;
      if (parent) {
        if (parent.children) {
          parent.children.push(maps[i.depCode]);
        } else {
          parent.children = [];
          parent.children.push(maps[i.depCode]);
        }
      } else {
        result.push(maps[i.depCode]);
      }
    });

    // 自动展开指定层级的节点
    if (this.autoExpandLevels > 0 || this.autoExpandLevels === -1) {
      this.autoExpandNodes(result, 0);
    }

    return result;
  }

  /**
   * 自动展开节点到指定层级
   * @param nodes 节点数组
   * @param currentLevel 当前层级
   */
  private autoExpandNodes(nodes: OstTreeListItem[], currentLevel: number): void {
    if (!nodes || nodes.length === 0) {
      return;
    }

    for (const node of nodes) {
      // 如果设置为-1，展开所有层级；否则只展开到指定层级
      if (this.autoExpandLevels === -1 || currentLevel < this.autoExpandLevels) {
        if (node.children && node.children.length > 0) {
          node.expanded = true;
          // 递归展开子节点
          this.autoExpandNodes(node.children, currentLevel + 1);
        }
      }
    }
  }

  flattenDeviceTypes(deviceTypes: any): FlattenedDeviceType[] {
    const flattened: FlattenedDeviceType[] = [];
    for (const deviceType of deviceTypes) {
      // 转换字段名并移除其他无关属性
      const flatDeviceType: FlattenedDeviceType = {
        depCode: deviceType.deviceTypeCode,
        id: deviceType.classCode,
        depName: deviceType.deviceTypeName,
        parentCode: deviceType.parentCode,
      };
      flattened.push(flatDeviceType);
      if (deviceType.children) {
        // 递归处理子设备类型
        const childFlattened = this.flattenDeviceTypes(deviceType.children);
        flattened.push(...childFlattened);
      }
    }
    return flattened;
  }

}

interface OrgItem {
  depCode: string;
  depLevel: string;
  depLevelLabel: string;
  children?: OrgItem[];
  depName: string;
  id: string;
  isDelete: number;
  parentCode: string;
  sortNum: string;
}

interface FlattenedDeviceType {
  depCode: string;
  id: string;
  depName: string;
  parentCode?: string;
}