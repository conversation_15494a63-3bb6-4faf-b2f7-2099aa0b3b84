import { Component, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { BackgroundGeolocation, BackgroundGeolocationConfig, BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { AlertController, IonContent, IonFab, ModalController } from '@ionic/angular';
import Geometry from 'ol/geom/Geometry';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { RequestResult } from 'src/app/@core/base/request-result';
import { ToastService } from 'src/app/@core/providers/toast.service';
import { ScrollControlService, ScrollState } from 'src/app/@core/services/scroll-control.service';
import { MapComponent } from 'src/app/share/map-component/map/map.component';
import { Task } from '../class/work';
import { PlaybackPage } from '../playback/playback.page';
import { WorkService } from '../work.service';
import { NotInspectedComponent } from 'src/app/execut/modal/not-inspected/not-inspected.component';

@Component({
  selector: 'task-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss']
})
export class TaskDetailComponent implements OnInit {
  @ViewChild('ostMap', { static: false }) ostMap: MapComponent;
  @ViewChild('content', { static: false }) content: IonContent;
  @ViewChild('myFab') fab: IonFab;
  @Input() modelInfo: any = new Task();
  @Input() centerPoint: number[];
  // 需要操作的业务图层ID
  layerIds = ['inspect_point', 'p_pipe_joint_info'];

  keyPointLayer: VectorLayer<VectorSource<Geometry>> = new VectorLayer({ source: new VectorSource() });
  // 检测到存在未上传数据
  onUploadLength = 0;
  // 检测到未上传原始数据
  locationConfig: BackgroundGeolocationConfig;
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  // 滚动按钮图标
  scrollButtonIcon = 'chevron-down-outline';
  // 当前滚动状态：'top' 表示在顶部显示向下箭头，'bottom' 表示在底部显示向上箭头
  scrollState: 'top' | 'bottom' | 'middle' = 'top';
  constructor(
    private workService: WorkService, private alertCtrl: AlertController,
    private modalCtrl: ModalController, private toastSer: ToastService,
    private backgroundGeolocation: BackgroundGeolocation,
    private scrollControlService: ScrollControlService,
  ) { }

  ngOnInit(): void {
    // 检测未上传数据
    this.checkData();
    // 延迟设置滚动监听，确保content已初始化
    setTimeout(() => {
      this.setupScrollListener();
    }, 100);
  }

  /**
   * 轨迹回放
   */
  async onTrackPlayback(event: any): Promise<void> {
    event.stopPropagation();
    const modal = await this.modalCtrl.create({
      component: PlaybackPage,
      componentProps: { modelInfo: this.modelInfo },
      // cssClass: 'my-custom-class',
      // swipeToClose: true,
    });
    await modal.present();
  }

  /**
   * 刷新合格率
   */
  onRefreshRate(): void {
    this.workService.refreshRate({ taskCode: this.modelInfo.taskCode })
      .pipe(takeUntil(this.destroy$))
      .subscribe((result: RequestResult<Task>) => {
        const { code, data, msg } = result;
        if (code === 0) {
          this.modelInfo.completionRate = data;
          this.toastSer.presentToast('刷新成功', 'success');
        } else {
          this.toastSer.presentToast(msg, 'danger');
        }
      });
  }

  /**
   * 开始执行
   */
  async startExecution(): Promise<void> {
    if (this.onUploadLength !== 0 && this.modelInfo.taskCode !== this.locationConfig.postTemplate.taskId) {
      this.checkAlert();
    } else {
      // 若无历史轨迹，则关闭当前的详情页 modal
      // 并传递一个标识 'start_execution' 给 WorkPage 页面
      await this.modalCtrl.dismiss('start_execution');
    }
  }

  /**
   * 任务异常说明
   */
  async abnormalInfo(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: NotInspectedComponent,
      cssClass: 'camera-list-modal',
      componentProps: {
        taskCode: this.modelInfo.taskCode
      },
      backdropDismiss: false
    });
    await modal.present();

    const { data } = await modal.onDidDismiss();

    if (data === 'refresh') {
      // 使用 setTimeout 确保在下一个事件循环中执行关闭操作，避免模态框状态冲突
      setTimeout(async () => {
        try {
          await this.modalCtrl.dismiss('refresh');
        } catch (error) {
          // 如果直接关闭失败，尝试获取顶层模态框并关闭
          const topModal = await this.modalCtrl.getTop();
          if (topModal) {
            await topModal.dismiss('refresh');
          }
        }
      }, 50);
    }
  }

  /**
   * 检测未上传数据
   */
  async checkData(): Promise<void> {
    try {
      this.onUploadLength = (await this.backgroundGeolocation.getValidLocations() as BackgroundGeolocationResponse[]).length;
      this.locationConfig = await this.backgroundGeolocation.getConfig() as BackgroundGeolocationConfig;
    } catch (error) {
      console.error('检测未上传数据失败:', error);
      this.onUploadLength = 0;
    }
  }
  /**
   * 请先上传XXX任务的巡检轨迹数据，再开始新的任务
   */
  async checkAlert(): Promise<void> {
    // 错误时弹出框提醒确定后关闭执行页面
    const alert = await this.alertCtrl.create({
      header: '系统提示',
      message: '请先上传'
        + ' "' + this.locationConfig.postTemplate.taskName + '" '
        + '任务的巡检轨迹数据，再开始新的任务',
      backdropDismiss: false,
      buttons: [{
        text: '确认',
        handler: () => {
          this.modalCtrl.dismiss();
        }
      }]
    });
    await alert.present();
  }

  get contentHeight(): string {
    const bottomBtnHeight = this.isModelInfoCurrent() ? 60 : 0;
    return `calc(100vh - 56px - ${bottomBtnHeight}px)`;
  }

  /**
   * 判断当前时间是否在任务时间范围内
   * @returns 布尔值 true:在时间范围内;false:不在时间范围内
   */
  isModelInfoCurrent(): boolean {
    const now = new Date();
    const start = new Date(this.modelInfo.startTime);
    const end = new Date(this.modelInfo.endTime);
    const status = this.modelInfo.status;

    return now >= start && now <= end && status !== '超时未完成';
  }

  /**
   * 是否显示刷新按钮
   */
  shouldDisplay(): boolean {
    return this.modelInfo.status !== '已完成' && this.modelInfo.status !== '超时未完成';
  }

  /**
   * 设置滚动监听
   */
  setupScrollListener(): void {
    this.scrollControlService.setupScrollControl(this.content, (state: ScrollState) => {
      this.scrollState = state.scrollState;
      this.scrollButtonIcon = state.scrollButtonIcon;
    });
  }

  /**
   * 悬浮按钮点击事件，根据状态滚动
   */
  onScrollButtonClick(): void {
    this.scrollControlService.onScrollButtonClick(this.content, (state: ScrollState) => {
      this.scrollState = state.scrollState;
      this.scrollButtonIcon = state.scrollButtonIcon;
    });
  }

  goBack(): void { this.modalCtrl.dismiss(); }
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalCtrl.dismiss();
    });
  }
  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
    // 移除地图点图层
    if (this.keyPointLayer) {
      this.keyPointLayer.getSource().clear();
      // 检查地图组件是否存在且已初始化
      if (this.ostMap && this.ostMap.map) {
        this.ostMap.map.removeLayer(this.keyPointLayer);
      }
    }
  }
}