<ion-tabs>
  <ion-tab-bar slot="bottom">

    <ng-container *ngFor="let item of tabList">
      <ion-tab-button [tab]="item.id">
        <ion-badge *ngIf="item.id==='news' && unreadCount !== 0" 
          color="danger" style="font-size: 10px;">
          {{unreadCount}}
        </ion-badge>
        <ion-icon [name]="item.icon"></ion-icon>
        <ion-label>{{item.name}}</ion-label>
      </ion-tab-button>
    </ng-container>

  </ion-tab-bar>
</ion-tabs>