import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DownloadComponent } from './download/download.component';
import { UpdataComponent } from './updata/updata.component';
import { IonicModule } from '@ionic/angular';


@NgModule({
  imports: [
    CommonModule,
    IonicModule,
  ],
  exports: [UpdataComponent, DownloadComponent],
  declarations: [UpdataComponent, DownloadComponent],
  entryComponents: [UpdataComponent, DownloadComponent],
})
export class VersionModule { }
