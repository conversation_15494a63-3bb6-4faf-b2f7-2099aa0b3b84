import { Component, Input, OnInit, ElementRef, ViewChild, AfterViewInit, OnDestroy } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { File as OstFile } from 'ost-utils';
import { environment } from 'src/environments/environment';
import { ImageGestureService } from './services/image-gesture.service';

@Component({
  selector: 'ost-preview',
  templateUrl: './preview.component.html',
  styleUrls: ['./preview.component.scss']
})
export class PreviewComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() file: OstFile;
  @Input() fileUrl: string;
  @Input() title: string;
  @Input() isCardMode: boolean = false;
  @Input() showClearButton: boolean = false;
  @ViewChild('previewImg', { static: false }) previewImg: ElementRef;
  @ViewChild('closeBtn', { static: false }) closeBtn: ElementRef;
  src: string;

  constructor(
    public modalController: ModalController,
    public platform: Platform,
    private imageGestureService: ImageGestureService
  ) { }

  ngOnInit(): void {
    /**
     * 初始化图片预览的 src 路径，优先级如下：
     * 1. 直接传入的 fileUrl
     * 2. file 对象中的 url 字段（拼接后端文件访问地址）
     * 3. file 对象中的 base64 字段
     * 4. 以上都不存在则为空字符串
     */
    this.src = this.getImageSource();
  }

  /**
   * 获取图片源地址
   * @returns 图片源地址
   */
  private getImageSource(): string {
    if (this.fileUrl) {
      return this.fileUrl;
    }

    if (this.file?.url) {
      return this.file.url.startsWith('http')
        ? this.file.url
        : this.buildFileUrl(this.file.url);
    }

    return this.file?.base64 || '';
  }

  /**
   * 构建文件URL
   * @param fileUrl 文件路径
   * @returns 完整的文件访问URL
   */
  private buildFileUrl(fileUrl: string): string {
    const { production, api } = environment;
    const protocol = production ? 'https' : 'http';
    const port = api.port ? `:${api.port}` : '';
    return `${protocol}://${api.ip}${port}/file/getV2/${fileUrl}`;
  }

  ngAfterViewInit(): void {
    if (this.previewImg && this.previewImg.nativeElement) {
      // 使用手势服务初始化手势识别
      this.imageGestureService.initializeGestures(
        this.previewImg,
        this.previewImg.nativeElement.parentElement,
        {
          minScale: 1,
          maxScale: 5,
          dampingFactor: 0.5, // 提高阻尼系数，让移动更顺滑
          resetAnimationDuration: 300,
          minVisibleRatio: 2, // 可见区域比例
          // 优化缩放体验的配置
          inertiaEnabled: true,
          inertiaFriction: 0.93,    // 提高摩擦系数，让惯性滑动更持久
          inertiaMinVelocity: 0.7,  // 降低最小速度阈值，让微小移动也有惯性
          inertiaMaxDuration: 900  // 增加惯性滑动时间，让移动更流畅
        }
      );
    }
  }

  ngOnDestroy(): void {
    // 销毁手势服务
    this.imageGestureService.destroy();
  }

  /**
   * 获取变换样式
   */
  getTransformStyle(): string {
    return this.imageGestureService.getTransformStyle();
  }

  /**
   * 关闭窗口
   */
  closePic(): void {
    this.modalController.dismiss();
  }

  /**
   * 消除按钮点击事件
   */
  onClearClick(): void {
    this.modalController.dismiss({ action: 'clear' });
  }

}
