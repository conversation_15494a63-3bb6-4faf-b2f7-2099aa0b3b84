import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { ShareModule } from '../share/share.module';
import { NewsDetailComponent } from './news-detail/news-detail.component';
import { NewsPage } from './news.page';

const routes: Routes = [
  {
    path: '',
    component: NewsPage,
  }
];

const COMPONENT: any[] = [
  NewsDetailComponent, // 消息详情
];

@NgModule({
  imports: [
    IonicModule,
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    ShareModule,
    RouterModule.forChild(routes)
  ],
  declarations: [NewsPage, ...COMPONENT]
})
export class NewsPageModule { }
