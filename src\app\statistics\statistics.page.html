<ion-header
  class="sticky"
  [translucent]="true"
>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>巡检统计</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onShowSearch()">
        筛选
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="body" [class.search-visible]="isShowSearch" [class.search-hidden]="!isShowSearch">

  <!-- 搜索 -->
  <div [ngClass]="{'form-search-show': isShowSearch,'form-search-hide': !isShowSearch}">
    <form [formGroup]="searchGroup">
      <ost-form-list [formGroup]="searchGroup">
        <ost-form-item [required]="false">
          <ion-label>部门名称&nbsp;:&nbsp;</ion-label>
          <ost-input-search
            placeholder="点击选择部门"
            formControlName="depCode"
            slot="end"
          >
            <search-source-org></search-source-org>
          </ost-input-search>
        </ost-form-item>
        <ost-form-item [required]="false">
          <ion-label>开始时间&nbsp;:&nbsp;</ion-label>
          <ion-datetime
            formControlName="startDate"
            displayFormat="YYYY-MM-DD"
            placeholder="点击选择时间"
            cancelText="取消"
            doneText="确定"
          ></ion-datetime>
        </ost-form-item>
        <ost-form-item [required]="false">
          <ion-label>结束时间&nbsp;:&nbsp;</ion-label>
          <ion-datetime
            formControlName="endDate"
            displayFormat="YYYY-MM-DD"
            placeholder="点击选择时间"
            cancelText="取消"
            doneText="确定"
          ></ion-datetime>
        </ost-form-item>
      </ost-form-list>
    </form>
    <div class="form-button">
      <ion-button
        type="reset" size="small"
        (click)="onReset()"
      >重置</ion-button>
      <ion-button
        type="submit" size="small"
        (click)="onSearch()"
      >搜索</ion-button>
    </div>
  </div>

  <div class="content-wrapper" [ngClass]="{'content-show': isShowSearch}">

    <div class="tabs-container">
      <div class="tabs">
        <button class="tab-button" [ngClass]="{ active: selectedTab === 'inspection' }" (click)="selectTab('inspection')">巡检概况</button>
        <button class="tab-button" [ngClass]="{ active: selectedTab === 'employee' }" (click)="selectTab('employee')">员工数据</button>
      </div>
    </div>
  
    <!-- 巡检概况内容 -->
    <div *ngIf="selectedTab === 'inspection'" class="site-profile">
      <div class="task-item">
        <ion-item lines="none">
          <ion-label>
            <p class="task-p"></p>
            各站点巡检任务完成率
          </ion-label>
        </ion-item>
        <div class="horizontal-bar-chart">
          <ng-container *ngIf="depStatistics.length > 0; else noData">
            <div class="bar-container" *ngFor="let item of depStatistics">
              <span class="label" style="width: 120px;">{{item.depName}}</span>
              <div class="bar">
                  <div class="fill" [style.width]="item.value"></div>
              </div>
              <span class="percentage">{{item.value}}</span>
          </div>
          </ng-container>
        </div>
      </div>
    
      <div class="task-item">
        <ion-item lines="none">
          <ion-label>
            <p class="task-p"></p>
            站点反馈问题统计
          </ion-label>
        </ion-item>
        <ion-grid (click)="showDetail()">
          <ion-row class="ion-align-items-center">
            <ion-col size="4" *ngFor="let item of problemList">
              <div class="div-box">
                <div class="p-img" [style.color]="'#'+item.color">
                  {{item.value}}
                </div>
                <div class="name">{{item.name}}</div>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>

    </div>

    <!-- 员工数据内容 -->
    <div *ngIf="selectedTab === 'employee'" class="site-profile">
      <div class="task-item">
        <ion-item lines="none">
          <ion-label>
            <p class="task-p"></p>
            巡检人员合格率统计
          </ion-label>
        </ion-item>
        <div class="horizontal-bar-chart">
          <ng-container *ngIf="userStatistics.length > 0; else noData">
            <div class="bar-container" *ngFor="let item of userStatistics">
              <span class="label">{{item.userName}}</span>
              <div class="bar">
                  <div class="fill" [style.width]="item.value"></div>
              </div>
              <!-- 保留两位小数 -->
              <span class="percentage">{{ item.value | formatPercentage }}</span>
          </div>
          </ng-container>
        </div>
      </div>
    </div>

  </div>
</ion-content>

<ng-template #noData>
  <div class="no-data">
    <img src="assets/menu/box2.png" style="height: 45px;" />
    <!-- 暂无数据 -->
    <span class="no-data-span" style="font-size: 12px;">暂无数据</span>
  </div>
</ng-template>