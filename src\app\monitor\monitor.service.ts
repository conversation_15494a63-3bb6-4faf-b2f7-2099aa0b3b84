import { Injectable } from '@angular/core';
import { IResourceMethodObservable, Resource, ResourceAction, ResourceParams } from '@ngx-resource/core';
import { RequestResult } from '../@core/base/request-result';
import { Summary } from './class/monitor';

/**
 * 实时监控服务
 */
@ResourceParams({
  pathPrefix: '',
})
@Injectable({
  providedIn: 'root'
})
export class MonitorService extends Resource {

  constructor() {
    super();
  }

  /**
   * 根据部门获取实时监控汇总
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/online/msg/grid',
  })
  getOnlineSummary!: IResourceMethodObservable<{ depName: string }, RequestResult<Summary>>;
  /**
   * 根据部门获取实时监控在线人员列表
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/online/users/geom',
  })
  getOnlineUserList!: IResourceMethodObservable<{ depCode: string }, RequestResult<any>>;
  /**
   * 根据人员Id查询轨迹信息
   */
  @ResourceAction({
    path: '/work-inspect/api/v2/inspect/app/online/user/line',
  })
  getOnlineTrail!: IResourceMethodObservable<{ userCode: string }, RequestResult<any>>;

}
