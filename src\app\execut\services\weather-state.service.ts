import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface WeatherState {
  isSunny: boolean;
  isItRaining: string;
}

@Injectable({
  providedIn: 'root'
})
// 天气状态服务
export class WeatherStateService {
  private weatherState = new BehaviorSubject<WeatherState>({
    isSunny: true,
    isItRaining: '否'
  });

  /**
   * 获取天气状态
   */
  getWeatherState(): Observable<WeatherState> {
    return this.weatherState.asObservable();
  }

  /**
   * 获取当前天气状态值
   */
  getCurrentWeatherState(): WeatherState {
    return this.weatherState.value;
  }

  /**
   * 获取是否晴天状态
   */
  get isSunny(): boolean {
    return this.weatherState.value.isSunny;
  }

  /**
   * 获取是否下雨状态
   */
  get isItRaining(): string {
    return this.weatherState.value.isItRaining;
  }

  /**
   * 切换天气状态
   */
  toggleWeather(): void {
    const currentState = this.weatherState.value;
    const newIsSunny = !currentState.isSunny;
    const newIsItRaining = newIsSunny ? '否' : '是';

    this.weatherState.next({
      isSunny: newIsSunny,
      isItRaining: newIsItRaining
    });

    console.log('当前状态是:', newIsItRaining);
  }

  /**
   * 设置天气状态
   * @param isSunny 是否晴天
   */
  setWeatherState(isSunny: boolean): void {
    const isItRaining = isSunny ? '否' : '是';
    
    this.weatherState.next({
      isSunny,
      isItRaining
    });
  }

  /**
   * 判断是否显示天气开关
   * @param inspectionMethod 巡检方式
   * @param executState 执行状态
   */
  shouldShowWeatherSwitch(inspectionMethod: string, executState: string): boolean {
    return inspectionMethod === '车巡' && executState === 'create';
  }

  /**
   * 重置天气状态
   */
  resetWeatherState(): void {
    this.weatherState.next({
      isSunny: true,
      isItRaining: '否'
    });
  }
} 