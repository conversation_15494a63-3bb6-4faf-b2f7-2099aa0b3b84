import { AbstractControl, ValidatorFn } from '@angular/forms';

export class OSTValidators {

    /**
     * 密码强度校验
     */

    static passwordValid(password: AbstractControl): any {
        const mode = /^.*(?=.{6,28})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*?\(\)]).*$/;
        const valid = password.value === '' ? true : mode.test(password.value);
        return valid ? null : { password: true };
    }

    /**
     * 一致性校验[确认密码]
     */
    static match(targetField: string): ValidatorFn {
        return (self: AbstractControl): { [key: string]: any } => {
            const form = self.parent;
            if (form) {
                const targetControl: AbstractControl = form.controls[targetField];
                if (targetControl.value && self.value !== targetControl.value) {   // 如果两个值不一致
                    return { match: true };
                }
            }
        };
    }

    /**
     * 手机号验证
     */
    static mobilePhoneValid(phone: AbstractControl): any {
        const mode = /^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/;
        const valid = phone.value === '' ? true : mode.test(phone.value);
        return valid ? null : { mobile: true };
    }


    /**
     * 邮箱校验
     */
    static emailValid(email: AbstractControl): any {
        const mode = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
        const valid = email.value === '' ? true : mode.test(email.value);
        return valid ? null : { email: true };
    }


    /**
     * 单选校验
     */
    static answerValid(answerValid: AbstractControl): any {
        return answerValid.value.length !== 0 ? null : { answerValid: true };
    }

    /**
     * 新旧密码不能一致校验
     */
    static notSameAsOldPassword(oldPasswordField: string): ValidatorFn {
        return (control: AbstractControl): { [key: string]: any } | null => {
            const form = control.parent;
            if (form) {
                const oldPassword = form.get(oldPasswordField)?.value;
                const newPassword = control.value;
                if (oldPassword && newPassword && oldPassword === newPassword) {
                    return { notSameAsOldPassword: true };
                }
            }
            return null;
        };
    }

}
