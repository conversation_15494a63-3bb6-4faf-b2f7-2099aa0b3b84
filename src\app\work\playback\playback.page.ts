import { Component, HostListener, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { MapComponent } from 'src/app/share/map-component/map/map.component';
import { Task } from 'src/app/work/class/work';

@Component({
  selector: 'app-playback',
  templateUrl: './playback.page.html',
  styleUrls: ['./playback.page.scss'],
})
export class PlaybackPage implements OnInit, OnDestroy {
  @ViewChild('ostMap', { static: false }) ostMap: MapComponent;
  @Input() modelInfo: Task;
  constructor(
    public modalController: ModalController,
    public platform: Platform,
    ) { }

  ngOnInit(): void {
  }

  /**
   * 回退
   */
  goBack(): void {
    this.modalController.dismiss();
  }
  /**
   * 手机左滑回退
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.modalController.dismiss();
    });
  }
  ngOnDestroy(): void {

  }
}
