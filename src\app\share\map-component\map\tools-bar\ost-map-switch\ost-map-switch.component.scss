// 图层选择器宽度
$switch-layout-width: 100%;
// 图层选择器高度
$switch-layout-height: 180px;
// 图层选择器头高度
$switch-layout-header-height: 35px;
// 图层选择器容器高度
$switch-layout-content-height: 140px;
.switch-layout {
  width: $switch-layout-width;
  background: white;
  color: #000;
  // height: $switch-layout-height;
  .switch-header {
    width: 100%;
    height: $switch-layout-header-height;
    display: flex;
    font-size: 14px;
    flex-direction: row;
    .header-item {
      width: 50%;
      height: $switch-layout-header-height;
      line-height: $switch-layout-header-height;
      padding-left: 15px;
    }
    .end {
      text-align: end;
      padding-right: 16px;
      ion-icon {
        font-size: 16px;
      }
    }
  }
  .switch-content {
    width: 100%;
    // height: $switch-layout-content-height;
    overflow-y: scroll;
    .content-grid {
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      justify-content: space-between;
      .grid-item {
        width: 30%;
        height: 65px;
        background-color: #eee;
        margin-bottom: 15px;
        border: 2px solid #fff;
        font-size: 12px;
        img {
          width: 100%;
          height: 59px;
        }
        span {
          position: relative;
          display: block;
          width: 100%;
          height: 20px;
          top: -20px;
          text-align: center;
          line-height: 20px;
          font-size: 12px;
          background-color: #333854;
          color: #fff;
        }
        &:hover {
          border: 2px solid #7693f5;
          span {
            background-color: #7693f5;
          }
        }
        &.active {
          border: 2px solid #526ecc;
          span {
            background-color: #526ecc;
          }
        }
      }

      .layer-checked {
        width: 100%;
        height: 60px;
        border: 1px solid #eee;
        margin-top: 10px;
        padding: 10px 0 0 0;
        background: white;
        ion-item {
          height: 20px;
          align-items: normal;
          ion-checkbox {
            margin: 0 12px 0 0;
            width: 16px;
            height: 16px;
          }
          ion-label {
            font-size: 12px;
            margin: 0;
          }
        }
        .range {
          margin-top: 4px;
          width: 100%;
          margin-left: 5%;
          div {
            font-size: 12px;
            float: left;
            padding-left: 4px;
          }
          ion-range {
            margin: 0;
            height: 18px;
            width: 70%;
            float: left;
            padding: 0 6px;
          }
        }
      }
      &:after {
        content: "";
        width: 30%;
      }
    }
  }
}
ul {
  list-style: none;
  margin: 0px !important;
  padding-right: 15px !important;
  padding-left: 15px !important;
}
li {
  list-style: none;
}
::ng-deep .sc-ion-popover-ios-h{
  --width:280px;
}