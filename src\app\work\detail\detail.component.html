<ion-header class="sticky" [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start" class="title-start-back">
      <ion-button (click)="goBack()">
        <ion-icon name="chevron-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>任务详情</ion-title>
    <!-- *ngIf="modelInfo.status==='已完成'" -->
    <ion-buttons slot="end" class="title-end-operate" >
      <div class="icon-track" (click)="onTrackPlayback($event)">
        <img src="assets/images/track.png">
      </div>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content #content class="work-content" [style.height]="modelInfo.status === '已上报' ? 'calc(100vh - 56px)' : 'calc(100vh - 56px - 60px)'">
  <!-- 任务名称 -->
  <ion-item lines="none" class="full-border">
    <ion-label><p>计划名称：</p>{{modelInfo.planName}}</ion-label>
  </ion-item>

  <ion-item lines="none" class="full-border">
    <ion-label><p>任务名称：</p>{{modelInfo.taskName}}</ion-label>
  </ion-item>

  <ion-item lines="none" class="full-border">
    <ion-label><p>归属部门：</p>{{modelInfo.depName}}</ion-label>
  </ion-item>

  <ion-item lines="none" class="full-border" class="split-line">
    <ion-label><p>巡检状态：</p>{{modelInfo.status}}</ion-label>
  </ion-item>

  <ion-item lines="none" class="full-border">
    <ion-label><p>巡检方式：</p>{{modelInfo.inspectionMethod}}</ion-label>
  </ion-item>

  <ion-item lines="none" class="full-border">
    <ion-label><p>完成率：</p>{{modelInfo.completionRate}}</ion-label>
    <ion-note *ngIf="shouldDisplay()" slot="end" (click)="onRefreshRate()">
      <ion-icon class="icon-refresh" name="refresh-outline"></ion-icon>
    </ion-note>
  </ion-item>

  <!-- 任务开始时间 -->
  <ion-item lines="none" class="full-border">
    <ion-label><p>任务开始时间：</p>{{modelInfo.startTime}}</ion-label>
  </ion-item>
  <!-- 任务结束时间 -->
  <ion-item lines="none" class="full-border" class="split-line">
    <ion-label><p>任务结束时间：</p>{{modelInfo.endTime}}</ion-label>
  </ion-item>
  <!-- 地图 -->
  <!-- <div class="scope-map split-line">
    <ost-map #ostMap
      [center]="centerPoint"
      [layerIds]="layerIds"
      [taskCode]="modelInfo.taskCode"
      [inspectionMethod]="modelInfo.inspectionMethod"
    >
    </ost-map>
  </div> -->
  <!-- 关键点 -->
  <app-key-points [taskCode]="modelInfo.taskCode" [showClose]="false" [showDataStatus]="true"></app-key-points>

  <ion-fab #myFab vertical="top" horizontal="end" slot="fixed" class="custom-fab-top">
    <ion-fab-button color="primary" size="small" (click)="onScrollButtonClick()">
      <ion-icon [name]="scrollButtonIcon"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>

<ion-footer *ngIf="modelInfo.status !== '已上报'">
  <ion-toolbar>
    <ion-button expand="block" color="primary"
      *ngIf="isModelInfoCurrent()"
      (click)="startExecution()">
      {{modelInfo.status === '未执行' ? '开始巡检' : '继续执行'}}
    </ion-button>
    <ion-button expand="block" color="warning"
      *ngIf="modelInfo.status === '超时未完成'"
      (click)="abnormalInfo()">
      任务异常上报
    </ion-button>
  </ion-toolbar>
</ion-footer>