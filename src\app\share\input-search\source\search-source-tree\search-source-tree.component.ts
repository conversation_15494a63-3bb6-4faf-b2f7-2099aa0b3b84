import { Component, Input, OnInit } from '@angular/core';
import { OstTreeListItem } from 'src/app/share/ost-tree-list/ost-tree-list.service';
import { InputSearchSourceService } from '../../input-search-source.service';

@Component({
  selector: 'ost-search-source-tree',
  templateUrl: './search-source-tree.component.html',
  styleUrls: ['./search-source-tree.component.scss']
})
export class SearchSourceTreeComponent implements OnInit {
  // 请求地址
  @Input() interfaceUrl: string = '';
  // 标签项名称
  @Input() labelName: string = 'stakeName';
  // 标签项值
  @Input() labelValue: string = 'stakeCode';
  constructor(public searchSer: InputSearchSourceService) { }

  ngOnInit(): void { }

  onToggleSubMenu(item: OstTreeListItem): void {
    this.searchSer.change(item.data[this.labelName], item.data[this.labelValue]);
  }
  onItemClick(item: OstTreeListItem): void {
    this.searchSer.change(item.data[this.labelName], item.data[this.labelValue]);
  }

}
