import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController, ModalController, Platform } from '@ionic/angular';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { RequestResult } from './@core/base/request-result';
import { ToastService } from './@core/providers/toast.service';
import { AppBootService } from './app.boot';
import { VersionInfo } from './share/version/class/version-info';
import { UpdataComponent } from './share/version/updata/updata.component';
import { DataSyncManagerService } from './@core/providers/data-sync';
import { NetworkService } from './@core/providers/network.service';


@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnI<PERSON>t, OnD<PERSON>roy {
  backButton$: Subscription;
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  constructor(
    public appBoot: AppBootService, private modalCtrl: ModalController,
    private router: Router, public alertController: AlertController,
    private toastSer: ToastService, private platform: Platform,
    private dataSyncManager: DataSyncManagerService,
    private networkService: NetworkService
  ) {

  }
  async ngOnInit(): Promise<void> {
    this.initializeApp();
    this.checkNetworkAndNavigate();
    // 初始化平台
    await this.appBoot.boot();
    // 注册版本更新监听
    this.appBoot.onVersionChange().pipe(takeUntil(this.destroy$))
      .subscribe((result: RequestResult<VersionInfo>) => {
        const { code, data, msg } = result;
        code !== 0 ? this.toastSer.presentToast(msg, 'danger') : this.handleVersionUpdate(data);
      }, (error) => {
        this.toastSer.presentToast(error.body.message, 'danger');
      });
  }

  initializeApp() {
    this.platform.ready().then(async () => {
      // 既然平台已就绪，现在初始化依赖 Cordova 插件的服务
      this.networkService.init();
      this.dataSyncManager.init();
    });
  }

  checkNetworkAndNavigate() {
    if (this.networkService.isOffline()) {
      // 如果没有网络连接
      this.router.navigate(['/tabs/workbench']);
    }
  }

  /**
   * 返回按钮监听
   */
  @HostListener('document:ionBackButton', ['$event'])
  onBackButtonClick($event: CustomEvent): void {
    $event.detail.register(100, async () => {
      if (this.router.url === '/tabs/home'
        || this.router.url === '/tabs/work'
        || this.router.url === '/tabs/news'
        || this.router.url === '/tabs/self') {
        this.exitAppAlert();
      }
    });
  }


  /**
   * 退出APP
   */
  async exitAppAlert(): Promise<void> {
    const alert = await this.alertController.create({
      header: '退出',
      message: '您要退出您的APP吗？',
      buttons: [
        '取消',
        {
          text: '退出',
          handler: () => {
            // tslint:disable-next-line:no-string-literal
            navigator['app'].exitApp();
          }
        }]
    });
    await alert.present();
  }

  /**
   * 验证版本号格式是否符合规范
   * @param version - 需要验证的版本号字符串，格式应为 v1.0.1
   * @returns boolean - 如果版本号格式正确返回true，否则返回false
   * @example
   * isValidVersionFormat('v1.0.1') // 返回 true
   * isValidVersionFormat('1.0.1') // 返回 false
   * isValidVersionFormat('v1.0') // 返回 false
   */
  private isValidVersionFormat(version: string): boolean {
    const versionRegex = /^v\d+\.\d+\.\d+$/;
    return versionRegex.test(version);
  }

  /**
   * 比较两个版本号的大小
   * @param newVersion - 新版本号，格式为 v1.0.1
   * @param currentVersion - 当前版本号，格式为 v1.0.1
   * @returns number - 如果newVersion > currentVersion返回1，newVersion < currentVersion返回-1，相等返回0
   * @example
   * compareVersions('v1.0.2', 'v1.0.1') // 返回 1
   * compareVersions('v1.0.1', 'v1.0.2') // 返回 -1
   * compareVersions('v1.0.1', 'v1.0.1') // 返回 0
   */
  private compareVersions(newVersion: string, currentVersion: string): number {
    const v1 = newVersion.replace('v', '').split('.').map(Number);
    const v2 = currentVersion.replace('v', '').split('.').map(Number);

    for (let i = 0; i < 3; i++) {
      if (v1[i] > v2[i]) return 1;
      if (v1[i] < v2[i]) return -1;
    }
    return 0;
  }

  /**
   * 处理版本更新检查结果
   * 当检测到新版本时，会弹出更新提示框
   * 当已是最新版本时，会显示提示消息
   * 
   * @param versionInfo - 版本信息数据
   * @property {string} versionNumber - 版本号
   * 
   */
  private handleVersionUpdate = async (versionInfo: VersionInfo) => {
    // 验证版本数据是否存在
    if (!versionInfo || !versionInfo.versionNumber) {
      console.error('Invalid version data');
      return;
    }

    // 验证版本号格式
    if (!this.isValidVersionFormat(versionInfo.versionNumber) || !this.isValidVersionFormat(environment.version)) {
      console.error('Invalid version format');
      return;
    }

    // 比较版本号大小，根据比较结果显示更新提示或最新版本提示
    if (this.compareVersions(versionInfo.versionNumber, environment.version) > 0) {
      const modal = await this.modalCtrl.create({
        component: UpdataComponent,
        componentProps: { versionInfo },
        cssClass: 'dialog-box-xs',
        backdropDismiss: false
      });
      await modal.present();
    } else {
      this.toastSer.presentToast('已是最新版本！', 'primary', 2000, 'bottom');
    }
  }

  /**
   * 销毁
   */
  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }
}

