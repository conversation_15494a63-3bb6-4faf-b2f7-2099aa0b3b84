// tab 样式
.tabs-container {
  display: flex;
  justify-content: center;
  // margin: 6px 0;
  background: #fff;
  border-bottom: 1px solid #eee;

  .tabs {
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
    border-radius: 50px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    width: 90%;

    .tab-button {
      padding: 10px 20px;
      border: none;
      border-radius: 25px;
      font-size: 14px;
      cursor: pointer;
      background-color: transparent;
      color: #4285F4;
      transition: background-color 0.3s, color 0.3s;
      flex: 1;
      margin: 0 2px;

      &.active {
        background-color: #4285F4;
        color: #ffffff;
      }
    }
  }
}

// 内容区域样式
ion-content {
  --background: #f5f5f5;
}

// 确保子组件内容正确显示
app-clock-in,
app-not-inspected {
  display: block;
  height: 100%;
} 