// ========================================
// 模态框样式管理文件
// ========================================

// 允许事件穿透模态框的背景区域，但模态框内容本身不穿透
ion-modal.allow-events-through {
  pointer-events: none; // 允许点击穿透整个 ion-modal 元素
  --backdrop-opacity: 0; // 确保背景是透明的

  .modal-wrapper {
    pointer-events: auto; // 重新启用模态框内容区域的指针事件
  }
}

// 默认小窗口模式下的模态框样式
ion-modal.app-camera-layer {
  .modal-wrapper {
    position: fixed;
    top: 10px;
    right: 10px;
    left: auto; // 取消左侧定位
    transform: none; // 取消 transform
    height: auto;
    max-width: 95vw; // 最大宽度为视口宽度的 95vw
    max-height: 400px; // 最大高度为视口高度的 400px
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    overflow: hidden; // 确保内容不溢出
    animation: slideInFromTopRight 0.3s ease-out; // 进入动画

    // 确保 modal-wrapper 内部的 ion-page 填充其空间
    .ion-page {
      width: 100%;
      height: 100%;
      position: relative;
      display: block;
      contain: content;
    }
  }
}

// 小窗口从右上角滑入的动画
@keyframes slideInFromTopRight {
  from {
    transform: translate(100%, -100%); // 从右上角外开始
    opacity: 0;
  }
  to {
    transform: translate(0, 0); // 滑入到指定位置
    opacity: 1;
  }
}

// 全屏模式下的模态框样式：当 ion-modal 同时拥有 app-camera-layer 和 fullscreen-mode 类时
ion-modal.app-camera-layer.fullscreen-mode {
  .modal-wrapper {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    /* 确保全屏宽度 */
    height: 100vh !important;
    /* 确保全屏高度 */
    max-width: 100vw !important;
    /* 覆盖小窗口的最大宽度 */
    max-height: 100vh !important;
    /* 覆盖小窗口的最大高度 */
    border-radius: 0 !important; // 全屏时无圆角
    transform: none !important; // 全屏时无 transform
    animation: none !important; // 全屏时无进入动画
    box-shadow: none !important; // 全屏时无阴影
  }
}

// 摄像头列表模态框样式
.camera-list-modal {
  position: fixed;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 60vh;
  max-width: 95vw;
  width: 95vw;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  &::part(content) {
    border-radius: 16px;
  }

  ion-content {
    // --background: var(--ion-color-light);
    overflow: auto;
    max-height: 60vh;
  }
}

// 报警列表模态框样式
.alarm-list-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 80vh;
  max-width: 95vw;
  width: 95vw;
  height: 80vh;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  &::part(content) {
    border-radius: 16px;
  }

  ion-content {
    overflow: auto;
    max-height: 80vh;
  }
}

// 事件上报模态框样式
.app-evreport {
  position: fixed;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 80vh;
  max-width: 95vw;
  width: 95vw;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  &::part(content) {
    border-radius: 16px;
  }

  ion-content {
    overflow: auto;
    max-height: 80vh;
  }
}

// 图片预览模态框样式
.ost-preview {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 90vh;
  max-width: 95vw;
  width: auto;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

  &::part(content) {
    border-radius: 8px;
  }

  ion-content {
    overflow: hidden;
  }
}

// 卡片 图片模态框样式
.card-image-modal {
  position: fixed;
  top: 27%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 34vh;
  max-width: 95vw;
  width: 95vw;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

  &::part(content) {
    border-radius: 16px;
  }

  ion-content {
    overflow: auto;
    max-height: 80vh;
  }
}

// 自定义模态框样式
.my-custom-class {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 80vh;
  max-width: 95vw;
  width: 95vw;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

  &::part(content) {
    border-radius: 12px;
  }

  ion-content {
    overflow: auto;
    max-height: 80vh;
  }
} 