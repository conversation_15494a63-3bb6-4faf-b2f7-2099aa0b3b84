{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "defaultProject": "app", "newProjectRoot": "projects", "projects": {"app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["src/theme/variables.scss", "src/global.scss", "node_modules/webuploader/dist/webuploader.css"], "scripts": ["node_modules/echarts/dist/echarts.min.js", "node_modules/jquery/dist/jquery.js", "node_modules/webuploader/dist/webuploader.js"], "aot": false, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": true, "vendorChunk": true, "buildOptimizer": false, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}, "ci": {"progress": false}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}, "dev": {"browserTarget": "app:build:dev"}, "ci": {"progress": false}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "styles": [], "scripts": ["node_modules/echarts/dist/echarts.min.js"], "assets": [{"glob": "favicon.ico", "input": "src/", "output": "/"}, {"glob": "**/*", "input": "src/assets", "output": "/assets"}]}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "app:serve"}, "configurations": {"production": {"devServerTarget": "app:serve:production"}, "ci": {"devServerTarget": "app:serve:ci"}}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}, "dev": {"browserTarget": "app:build:dev"}}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "app:ionic-cordova-build", "devServerTarget": "app:serve"}, "configurations": {"production": {"cordovaBuildTarget": "app:ionic-cordova-build:production", "devServerTarget": "app:serve:production"}, "dev": {"cordovaBuildTarget": "app:ionic-cordova-build:dev", "devServerTarget": "app:serve:dev"}}}}}}, "cli": {"analytics": "78d7cd00-3f27-4d20-a929-ae2e74af8957", "defaultCollection": "@ionic/angular-toolkit"}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}