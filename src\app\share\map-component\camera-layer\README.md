# 摄像头组件 (Camera Layer Component)

## 概述

摄像头组件是一个用于显示实时视频流的Angular组件，支持FLV.js播放器、Canvas覆盖层绘制、全屏模式等功能。该组件主要用于监控系统中显示摄像头实时画面。

## 功能特性

- 🎥 **实时视频流播放**：支持FLV格式的实时视频流
- 🖼️ **Canvas覆盖层**：在视频上绘制警戒区域和管道检测区域
- 📱 **响应式设计**：支持横竖屏切换和全屏模式
- 🔄 **自动轮询**：定期获取摄像头状态信息
- 🛡️ **错误处理**：完善的错误处理和恢复机制
- 🎯 **预置位支持**：支持摄像头预置位变化检测

## 组件架构

```
camera-layer/
├── camera-layer.component.ts          # 主组件
├── camera-layer.component.html        # 组件模板
├── camera-layer.component.scss        # 组件样式
├── service/
│   ├── camera-info.service.ts         # 摄像头信息服务
│   ├── video-player.service.ts        # 视频播放器服务
│   └── canvas-drawing.service.ts      # Canvas绘制服务
└── README.md                          # 说明文档
```

## 核心组件

### CameraLayerComponent

主组件，负责协调各个服务，管理视频播放和用户交互。

#### 主要属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `modelInfo` | `any` | 父组件传入的模型信息，包含摄像头ID |
| `videoPlayer` | `ElementRef<HTMLVideoElement>` | 视频播放器元素引用 |
| `overlayCanvas` | `ElementRef<HTMLCanvasElement>` | 覆盖层Canvas元素引用 |
| `isFullScreen` | `boolean` | 是否处于全屏模式 |
| `videoUrl` | `string` | 视频流URL |
| `cameraInfo` | `any` | 摄像头详细信息 |
| `showError` | `boolean` | 是否显示错误信息 |
| `errorMessage` | `string` | 错误信息内容 |
| `showLoading` | `boolean` | 是否显示加载指示器 |

#### 主要方法

| 方法 | 说明 |
|------|------|
| `initializeCamera()` | 初始化摄像头，获取信息并开始轮询 |
| `handleCameraData()` | 处理摄像头数据，更新视频URL和播放器 |
| `toggleFullScreen()` | 切换全屏模式 |
| `retryInitialization()` | 重试初始化摄像头 |
| `handleError()` | 处理错误，停止轮询并清理资源 |

#### 生命周期

- `ngOnInit()`: 初始化组件，设置全屏模式并初始化摄像头
- `ngAfterViewInit()`: 视图初始化后，绑定视频事件监听器
- `ngOnDestroy()`: 组件销毁时，清理资源和事件监听器

## 服务详解

### CameraInfoService

负责获取摄像头信息和轮询管理。

#### 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getCameraInfo(id)` | `string` | `Promise<RequestResult<any>>` | 获取摄像头信息 |
| `startPolling(id, callback, errorCallback, interval)` | `string, Function, Function, number` | `void` | 开始轮询获取摄像头信息 |
| `stopPolling()` | - | `void` | 停止轮询 |
| `checkPresetBitChange(presetBitId)` | `string` | `boolean` | 检查预置位是否发生变化 |

#### 轮询机制

- 默认轮询间隔：10秒
- 自动错误处理：发生错误时自动停止轮询
- 资源清理：组件销毁时自动清理轮询定时器

### VideoPlayerService

负责FLV.js播放器的管理和错误处理。

#### 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `initFlvPlayer(videoElement, videoUrl)` | `HTMLVideoElement, string` | `void` | 初始化FLV播放器 |
| `destroyPlayer()` | - | `void` | 销毁播放器实例 |
| `isPlayerInitialized()` | - | `boolean` | 检查播放器是否已初始化 |
| `setOnErrorCallback(callback)` | `Function` | `void` | 设置错误回调函数 |
| `getCurrentUrl()` | - | `string \| null` | 获取当前播放的URL |

#### FLV.js配置

```typescript
const flvConfig = {
  type: 'flv',
  url: videoUrl,
  isLive: true,
  hasAudio: true,
  hasVideo: true,
  enableStashBuffer: false,
  stashInitialSize: 128,
  enableWorker: true,
  lazyLoad: true,
  seekType: 'range'
};
```

#### 错误处理

支持多种错误类型的识别和处理：
- 网络错误：连接异常、超时、状态码错误
- 媒体错误：格式不支持、编解码器不支持
- 播放错误：自动播放被阻止

### CanvasDrawingService

负责Canvas覆盖层的绘制和同步。

#### 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `initCanvas(canvasElement)` | `HTMLCanvasElement` | `void` | 初始化Canvas上下文 |
| `syncCanvasWithVideo(videoElement)` | `HTMLVideoElement` | `object` | 同步Canvas尺寸和位置 |
| `drawAreas(scopePoints, pipeScopePoints)` | `Point[], Point[]` | `void` | 绘制警戒区域和管道区域 |

#### 坐标系统

- 支持原始坐标到Canvas坐标的自动缩放
- 处理视频内容与容器的宽高比差异
- 精确覆盖视频内容的实际渲染区域

#### 绘制功能

- **警戒区域**：红色半透明填充，红色边框
- **管道区域**：蓝色边框，支持文本标签

## 使用方法

### 基本使用

```typescript
// 在父组件中
const cameraInfo = {
  id: 'camera-001',
  name: '摄像头1'
};

// 在模板中
<app-camera-layer [modelInfo]="cameraInfo"></app-camera-layer>
```

### 环境配置

确保在`environment.ts`中配置了正确的API地址：

```typescript
export const environment = {
  production: false,
  api: {
    ip: 'localhost',
    port: '8080'
  }
};
```

### 样式定制

可以通过CSS变量自定义组件样式：

```scss
.camera-container {
  --error-bg-color: rgba(0, 0, 0, 0.7);
  --error-text-color: #fff;
  --button-bg-color: rgba(255, 255, 255, 0.2);
}
```

## 错误处理

### 常见错误类型

1. **网络错误**
   - 视频地址无效
   - 网络连接超时
   - HTTP状态码错误

2. **播放器错误**
   - 浏览器不支持FLV播放
   - 视频格式不支持
   - 自动播放被阻止

3. **数据错误**
   - 摄像头信息获取失败
   - 数据格式错误

### 错误恢复

- 自动停止轮询避免持续错误
- 提供重试按钮允许用户手动恢复
- 完善的资源清理防止内存泄漏

## 性能优化

### 已实现的优化

1. **避免重复初始化**：检查URL变化，避免不必要的播放器重建
2. **事件绑定优化**：使用绑定的事件处理程序，避免重复创建函数
3. **资源清理**：组件销毁时正确清理所有资源
4. **轮询控制**：错误时自动停止轮询，避免资源浪费

### 建议的优化

1. **视频质量自适应**：根据网络状况调整视频质量
2. **预加载机制**：预加载下一个预置位的视频流
3. **缓存策略**：缓存摄像头配置信息

## 浏览器兼容性

- **支持FLV.js的浏览器**：Chrome 42+, Firefox 42+, Safari 11+, Edge 79+
- **移动端支持**：iOS Safari 11+, Android Chrome 42+
- **全屏API支持**：现代浏览器都支持

## 注意事项

1. **HTTPS要求**：生产环境必须使用HTTPS协议
2. **用户交互**：某些浏览器需要用户交互才能自动播放视频
3. **内存管理**：长时间使用需要监控内存使用情况
4. **网络带宽**：实时视频流会消耗较多带宽

## 更新日志

### v1.0.0
- 初始版本，支持基本的视频播放功能
- 添加Canvas覆盖层绘制
- 实现全屏模式切换
- 添加错误处理和恢复机制

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License 