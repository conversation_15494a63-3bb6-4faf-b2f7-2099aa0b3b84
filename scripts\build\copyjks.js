
var fs = require('fs');

var form_path=process.cwd()+'/sign/android/geok.jks'
var to_path =process.cwd()+'/platforms/android/geok.jks'
 
copyIt();
function copyIt() {
  console.log('检查签名是否存在');
  fs.exists(to_path, (isexists) => {
    if (isexists) {
      console.log('检查完成依存在签名');
    } else {
      console.log('开始创建签名');
      fs.writeFileSync(to_path, fs.readFileSync(form_path))
      console.log('创建完成');
    }
    console.log('>>>>开始打包');
  })
}


