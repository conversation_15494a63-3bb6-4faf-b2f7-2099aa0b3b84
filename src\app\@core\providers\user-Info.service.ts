import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { StorageService } from 'src/app/@core/providers/storage.service';
import { LoginMsg } from 'src/app/auth/class/auth';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root',
})
export class UserInfoService {
  private userInfo: LoginMsg = new LoginMsg();
  private loginStatus = false;
  private pUserName: string | undefined; // 用户名
  private pUserCode: string | undefined; // 用户id
  private pDepCode: string | undefined; // 部门id
  private pDepName: string | undefined; // 部门名称
  private pPhoneNumber: string | undefined; // 电话
  private pToken: string | undefined; // 口令
  private pAccount: string | undefined; // 账号
  private pAvatar: string | undefined; // 用户头像
  private pRoleType: string | undefined; // 角色类型
  private pExpireTime: string | undefined; // 到期时间

  get userId(): string | undefined {
    return this.pUserCode;
  }
  get depCode(): string | undefined {
    return this.pDepCode;
  }
  get depName(): string | undefined {
    return this.pDepName;
  }
  get account(): string | undefined {
    return this.pAccount;
  }
  get phoneNumber(): string | undefined {
    return this.pPhoneNumber;
  }
  get token(): string | undefined {
    return this.pToken;
  }
  get Avatar(): string | undefined {
    return this.pAvatar;
  }
  get userName(): string | undefined {
    return this.pUserName;
  }
  get roleType(): string | undefined {
    return this.pRoleType;
  }
  get expireTime(): string | undefined {
    return this.pExpireTime;
  }

  constructor(private storage: StorageService, public router: Router,) { }

  /**
   * 从缓存中更新用户信息
   */
  async init(): Promise<void> {
    // 使用Promise.all确保两个异步操作都完成
    const [userInfo, token] = await Promise.all([
      this.storage.get('userInfo').toPromise(),
      this.storage.get('token').toPromise()
    ]);
    if (!!userInfo) {
      // 使用 CryptoJS 将 Base64 字符串解码为 UTF-8 字符串
      const bytes = CryptoJS.enc.Base64.parse(userInfo);
      const jsonString = CryptoJS.enc.Utf8.stringify(bytes).toString();
      this.upDateUserInfo(JSON.parse(jsonString));
    } else {
      this.router.navigateByUrl('/auth');
    }
    if (!!token) {
      this.updateToken(atob(token));
    }
  }

  /**
   * @param userInfo 更新用户信息
   */
  private upDateUserInfo(userInfo: LoginMsg): void {
    if (userInfo) {
      this.userInfo = userInfo;
      // 对象转字符串
      const jsonString = JSON.stringify(userInfo);
      // 使用 CryptoJS 将 JSON 字符串编码为 Base64
      const bUserInfo = CryptoJS.enc.Utf8.parse(jsonString).toString(CryptoJS.enc.Base64);
      this.storage.set('userInfo', bUserInfo).subscribe();
      this.update();
    }
  }


  /**
   * 更新 token
   */
  private updateToken(token: string): void {
    ResourceGlobalConfig.headers = { token };
    this.pToken = token;
    this.storage.set('token', btoa(token)).subscribe();
  }

  /**
   * 清空用户信息
   */
  private clearToken(): void {
    this.storage.remove('token').subscribe();
    this.pToken = undefined;
    ResourceGlobalConfig.headers = {};
  }

  /***
   * 清空用户信息
   */
  private clearUserInfo(): void {
    // 临时方案
    this.storage.remove('loginInfo').subscribe();
    this.storage.remove('userInfo').subscribe();
    this.userInfo = new LoginMsg();
    this.pUserCode = undefined;
    this.pAccount = undefined;
    this.pUserName = undefined;
    this.pPhoneNumber = undefined;
    this.pToken = undefined;
    this.pDepCode = undefined;
    this.pDepName = undefined;
    this.pRoleType = undefined;
    this.pAvatar = undefined;
    this.pExpireTime = undefined;
  }

  /**
   * 清除全部
   */
  private clearAll(): void {
    this.clearToken();
    this.clearUserInfo();
  }

  /**
   * 属性更新
   */
  private update(): void {
    this.pUserCode = this.userInfo.userCode;
    this.pAccount = this.userInfo.account;
    this.pUserName = this.userInfo.userName;
    this.pPhoneNumber = this.userInfo.userPhone;
    this.pToken = this.userInfo.token;
    this.pDepCode = this.userInfo.depCode;
    this.pDepName = this.userInfo.depName;
    this.pRoleType = this.userInfo.roleType;
    this.pAvatar = this.userInfo.headSculpture;
    this.pExpireTime = this.userInfo.dateTime;
  }

  /**
   * 是否登录
   */
  isLogin(): boolean {
    return this.loginStatus;
  }

  /**
   * 登录
   */
  login(userInfo: LoginMsg): void {
    this.loginStatus = true;
    this.upDateUserInfo(userInfo);
    this.updateToken(userInfo.token);
  }

  /**
   * 更新登录状态
   */
  updateLoginStatus(status: boolean): void {
    this.loginStatus = status;
  }

  /**
   * 登出
   */
  logout(): void {
    // 重置
    this.loginStatus = false;
    this.clearAll();
  }


}

