<div #ostMap class="ost-map"></div>

<!-- 位置信息显示小工具 -->
<div class="location-info-widget" *ngIf="showLocationInfo">
  <div class="location-item">
    <span class="label">经度:</span>
    <span class="value">{{ currentLocationInfo.longitude }}°</span>
  </div>
  <div class="location-item">
    <span class="label">纬度:</span>
    <span class="value">{{ currentLocationInfo.latitude }}°</span>
  </div>
  <div class="location-item">
    <span class="label">偏差值:</span>
    <span class="value">{{ currentLocationInfo.accuracy }}m</span>
  </div>
  <div class="refresh-icon" *ngIf="showRefreshIcon" (click)="onRefreshLocation()">
    <ion-icon name="refresh-outline"></ion-icon>
  </div>
</div>

<ost-map-layout class="ost-map-layout">
  <ost-layout-item align="right-center">
    <ost-tools-bar
      #toolsBar
      [map]="map"
      [baseLayer]="baseLayer"
      [baseLayerList]="baseLayerList"
      [businessLayer]="businessLayer"
      [businessLayerList]="businessLayerList"
      [showLocationProviderButton]="showLocationProviderButton"
      (location)="setCurrentLocation([$event.longitude,$event.latitude],$event.accuracy)"
      (refreshLayer)="refreshLayer($event)"></ost-tools-bar>
  </ost-layout-item>

  <ng-content select="ost-layout-item">
  </ng-content>
</ost-map-layout>