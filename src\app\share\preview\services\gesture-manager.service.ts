import { Injectable, ElementRef } from '@angular/core';
import * as Hammer from 'hammerjs';
import { ImageGestureState, GestureConfig } from './image-gesture.model';

/**
 * 核心手势管理器
 * 负责Hammer.js的初始化、配置和销毁
 */
@Injectable({
  providedIn: 'root'
})
export class GestureManagerService {
  private hammer: any;
  private imageElement: ElementRef | null = null;
  private containerElement: HTMLElement | null = null;

  /**
   * 初始化手势识别
   * @param imageElement 图片元素引用
   * @param containerElement 容器元素（可选，默认使用图片的父元素）
   * @param config 手势配置（可选）
   */
  initializeGestures(
    imageElement: ElementRef,
    containerElement?: HTMLElement,
    config?: Partial<GestureConfig>
  ): any {
    this.imageElement = imageElement;
    this.containerElement = containerElement || imageElement.nativeElement?.parentElement;

    if (this.imageElement?.nativeElement) {
      this.setupHammer();
      this.addMobileOptimizations();
      return this.hammer;
    }
    return null;
  }

  /**
   * 设置Hammer.js（针对移动端优化）
   */
  private setupHammer(): void {
    this.hammer = new Hammer(this.imageElement!.nativeElement);

    // 优化缩放手势
    this.hammer.get('pinch').set({
      enable: true,
      threshold: 0.05, // 进一步降低缩放阈值，提高响应性
      pointers: 2 // 明确指定需要两个触点
    });

    // 优化拖拽手势
    this.hammer.get('pan').set({
      direction: Hammer.DIRECTION_ALL,
      threshold: 5, // 设置拖拽阈值，防止误触
      pointers: 1 // 明确指定单指拖拽
    });

    // 优化双击手势
    this.hammer.get('tap').set({
      taps: 2,
      interval: 300, // 双击间隔时间
      threshold: 10, // 双击位置阈值
      posThreshold: 30 // 位置阈值
    });

    // 移动端优化：禁用不需要的手势以提高性能
    this.hammer.get('swipe').set({ enable: false });
    this.hammer.get('rotate').set({ enable: false });

    // 移动端优化：设置触摸动作
    this.hammer.set({
      touchAction: 'none', // 完全禁用浏览器默认行为，提高缩放流畅度
      recognizers: [
        // 确保手势识别的优先级，缩放优先级最高
        [Hammer.Pinch, { enable: true }],
        [Hammer.Pan, { direction: Hammer.DIRECTION_ALL }, ['pinch']],
        [Hammer.Tap, { taps: 2 }]
      ]
    });
  }

  /**
   * 移动端优化：添加触摸优化
   */
  private addMobileOptimizations(): void {
    const element = this.imageElement!.nativeElement;

    // 防止图片被选中
    element.style.userSelect = 'none';
    element.style.webkitUserSelect = 'none';
    element.style.msUserSelect = 'none';

    // 防止图片拖拽
    element.draggable = false;

    // 防止长按菜单
    element.style.webkitTouchCallout = 'none';

    // 优化触摸延迟和缩放体验
    element.style.touchAction = 'none'; // 完全禁用浏览器默认触摸行为，提高响应性
  }

  /**
   * 绑定手势事件
   */
  bindEvents(handlers: {
    onPinch?: (ev: any) => void;
    onPinchStart?: (ev: any) => void;
    onPinchEnd?: () => void;
    onPanStart?: () => void;
    onPan?: (ev: any) => void;
    onPanEnd?: () => void;
    onTap?: () => void;
  }): void {
    if (!this.hammer) return;

    // 缩放手势
    if (handlers.onPinch) this.hammer.on('pinch', handlers.onPinch);
    if (handlers.onPinchStart) this.hammer.on('pinchstart', handlers.onPinchStart);
    if (handlers.onPinchEnd) this.hammer.on('pinchend', handlers.onPinchEnd);

    // 拖拽手势
    if (handlers.onPanStart) this.hammer.on('panstart', handlers.onPanStart);
    if (handlers.onPan) this.hammer.on('pan', handlers.onPan);
    if (handlers.onPanEnd) this.hammer.on('panend', handlers.onPanEnd);

    // 双击重置
    if (handlers.onTap) this.hammer.on('tap', handlers.onTap);
  }

  /**
   * 添加触摸事件监听器
   */
  addTouchListeners(handlers: {
    onTouchStart?: (ev: TouchEvent) => void;
    onTouchMove?: (ev: TouchEvent) => void;
    onTouchEnd?: (ev: TouchEvent) => void;
  }): void {
    if (!this.imageElement?.nativeElement) return;

    const element = this.imageElement.nativeElement;

    if (handlers.onTouchStart) {
      element.addEventListener('touchstart', handlers.onTouchStart, { passive: false });
    }
    if (handlers.onTouchMove) {
      element.addEventListener('touchmove', handlers.onTouchMove, { passive: false });
    }
    if (handlers.onTouchEnd) {
      element.addEventListener('touchend', handlers.onTouchEnd, { passive: true });
    }
  }

  /**
   * 移除触摸事件监听器
   */
  removeTouchListeners(handlers: {
    onTouchStart?: (ev: TouchEvent) => void;
    onTouchMove?: (ev: TouchEvent) => void;
    onTouchEnd?: (ev: TouchEvent) => void;
  }): void {
    if (!this.imageElement?.nativeElement) return;

    const element = this.imageElement.nativeElement;

    if (handlers.onTouchStart) {
      element.removeEventListener('touchstart', handlers.onTouchStart);
    }
    if (handlers.onTouchMove) {
      element.removeEventListener('touchmove', handlers.onTouchMove);
    }
    if (handlers.onTouchEnd) {
      element.removeEventListener('touchend', handlers.onTouchEnd);
    }
  }

  /**
   * 获取图片元素
   */
  getImageElement(): ElementRef | null {
    return this.imageElement;
  }

  /**
   * 获取容器元素
   */
  getContainerElement(): HTMLElement | null {
    return this.containerElement;
  }

  /**
   * 销毁手势识别
   */
  destroy(): void {
    if (this.hammer) {
      this.hammer.destroy();
      this.hammer = null;
    }

    // 清空元素引用
    this.imageElement = null;
    this.containerElement = null;
  }
}
