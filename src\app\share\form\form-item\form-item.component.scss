ion-text {
  color: red;
  padding: 0px;
  font-size: 14px;
  margin-left: 16px;
}

.dividing-line {
  border-bottom: 12px solid #f6f6f6;
}

ion-item-divider {
  --padding-start: 16px;
  --padding-end: 16px;
  font-size: 14px;
  display: flex;
  width: 100%;
  align-items: center;
}

/* textarea 上下布局样式 */
.textarea-vertical-layout {
  flex-direction: column !important;
  align-items: flex-start !important;
  
  ion-label {
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  ion-textarea {
    width: 100%;
    margin-top: 4px;
  }
  
  ion-text {
    position: relative;
    top: -24px;
    margin-left: 4px !important;
  }
}

ost-input-search {
  --margin-inline-start: 0px !important;
  flex: 1; /* 占用剩余所有空间 */
  min-width: 0; /* 防止flex子项溢出 */
  width: 100%;
}

ion-datetime {
  --margin-inline-start: 0px !important;
  flex: 1; /* 占用剩余所有空间 */
  min-width: 0; /* 防止flex子项溢出 */
  width: 100%;
}