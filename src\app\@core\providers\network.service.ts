import { Injectable, NgZone, OnDestroy } from '@angular/core';
import { BehaviorSubject, fromEvent, merge, Observable, Subject } from 'rxjs';
import { map, takeUntil, distinctUntilChanged } from 'rxjs/operators';
import { Platform } from '@ionic/angular';
import { Network } from '@ionic-native/network/ngx';

@Injectable({ providedIn: 'root' })
export class NetworkService implements OnDestroy {

  private networkStatus = new BehaviorSubject<boolean>(navigator.onLine);
  private destroy$ = new Subject<void>();
  private pollingInterval: any; // 用于存储 interval ID
  private isDestroyed = false; // 标记服务是否已销毁

  constructor(
    private network: Network,
    private zone: NgZone,
    private platform: Platform
  ) {}

  public init() {
    this.platform.ready().then(() => {
      console.log('[NetworkService] Platform 已就绪, 正在初始化网络状态检测。');
      this.initializeNetworkEvents();
    });
  }

  private initializeNetworkEvents(): void {
    // 立即设置一次初始状态
    this.updateNetworkStatus();

    // ---- 快速路径: 事件驱动 (实时性高) ----
    let observables: Observable<boolean>[] = [];

    // 在移动设备上使用 Cordova Network 插件
    if (this.platform.is('cordova')) {
      const nativeOnline$ = this.network.onConnect().pipe(map(() => true));
      const nativeOffline$ = this.network.onDisconnect().pipe(map(() => false));
      observables.push(nativeOnline$, nativeOffline$);
    }

    // 在所有平台上都监听 Web API 事件作为备用
    const webOnline$ = fromEvent(window, 'online').pipe(map(() => true));
    const webOffline$ = fromEvent(window, 'offline').pipe(map(() => false));
    observables.push(webOnline$, webOffline$);

    merge(...observables)
      .pipe(takeUntil(this.destroy$))
      .subscribe(isOnline => {
        this.zone.run(() => {
          console.log(`[NetworkService] 事件驱动检测到网络状态变化: ${isOnline ? '在线' : '离线'}`);
          this.networkStatus.next(isOnline);
        });
      });

    // ---- 兜底路径: 定期轮询 (通过network无法实时获得最新得网络状态) ----
    // 每3秒检查一次网络状态
    this.pollingInterval = setInterval(() => {
      // 检查服务是否已销毁，避免在销毁后继续执行
      if (this.isDestroyed) {
        clearInterval(this.pollingInterval);
        return;
      }

      try {
        this.zone.run(() => {
          this.updateNetworkStatus(true); // 传入 true 表示是轮询触发
        });
      } catch (error) {
        console.error('[NetworkService] 轮询检测网络状态时发生错误:', error);
        // 发生错误时停止轮询，避免持续错误
        clearInterval(this.pollingInterval);
      }
    }, 3000);
  }

  /**
   * 检查并更新网络状态的核心方法
   * @param fromPolling 是否由轮询调用
   */
  private updateNetworkStatus(fromPolling = false) {
    // 如果服务已销毁，不执行更新
    if (this.isDestroyed) {
      return;
    }

    try {
      let currentStatus: boolean;
      let networkType: string;

      // 根据平台选择不同的网络检测方式
      if (this.platform.is('cordova')) {
        // 移动设备：使用 Cordova Network 插件
        networkType = this.network.type;
        currentStatus = networkType !== 'none' && networkType !== null;
      } else {
        // 浏览器环境：使用 Web API
        currentStatus = navigator.onLine;
        networkType = currentStatus ? 'unknown' : 'none';
      }

      // 只有当状态真正发生变化时才更新，避免不必要的 next() 调用
      if (this.networkStatus.value !== currentStatus) {
        const source = fromPolling ? '轮询兜底' : '初始检测';
        const platform = this.platform.is('cordova') ? 'Cordova' : '浏览器';
        console.log(`[NetworkService] ${source}检测到网络状态变化: ${currentStatus ? '在线' : '离线'} (平台: ${platform}, 类型: ${networkType})`);
        this.networkStatus.next(currentStatus);
      }
    } catch (error) {
      console.error('[NetworkService] 更新网络状态时发生错误:', error);
      // 发生错误时，回退到使用 navigator.onLine
      const fallbackStatus = navigator.onLine;
      if (this.networkStatus.value !== fallbackStatus) {
        console.log(`[NetworkService] 回退到 navigator.onLine: ${fallbackStatus ? '在线' : '离线'}`);
        this.networkStatus.next(fallbackStatus);
      }
    }
  }

  public getNetworkStatus(): Observable<boolean> {
    // distinctUntilChanged() 可以防止连续发出相同的值
    return this.networkStatus.asObservable().pipe(distinctUntilChanged());
  }

  public isOnline(): boolean {
    return this.networkStatus.value;
  }

  /**
   * 检查当前是否离线状态（同步方法）
   * 提供给其他服务和组件使用，避免重复平台检测逻辑
   */
  public isOffline(): boolean {
    return !this.isOnline();
  }

  /**
   * 获取当前网络类型（同步方法）
   * 在浏览器环境下返回简化的网络类型
   */
  public getNetworkType(): string {
    if (this.platform.is('cordova')) {
      // 移动设备：使用 Cordova Network 插件
      return this.network.type || 'none';
    } else {
      // 浏览器环境：使用 Web API
      return navigator.onLine ? 'unknown' : 'none';
    }
  }

  ngOnDestroy() {
    console.log('[NetworkService] 正在销毁服务，清理资源...');

    // 标记服务已销毁
    this.isDestroyed = true;

    // 组件销毁时，清理订阅和定时器
    this.destroy$.next();
    this.destroy$.complete();

    // 清理定时器
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }

    // 完成网络状态流
    if (this.networkStatus && !this.networkStatus.closed) {
      this.networkStatus.complete();
    }

    console.log('[NetworkService] 服务销毁完成');
  }
}