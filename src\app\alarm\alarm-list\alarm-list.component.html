<ion-header>
  <ion-segment [(ngModel)]="readStatus" (ionChange)="segmentChanged($event)">
    <ion-segment-button value="未读">
      <ion-label>报警列表</ion-label>
    </ion-segment-button>
    <ion-segment-button value="已读">
      <ion-label>历史报警</ion-label>
    </ion-segment-button>
  </ion-segment>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <!-- 列表 -->
  <ng-container *ngIf="alarms.length > 0 || isLoading; else noData">
    <ion-list>
      <ion-item *ngFor="let alarm of alarms">
        <ion-thumbnail slot="start" style="position: relative;">
          <img [src]="getFirstImageUrl(alarm)" alt="报警图片" />
          <div *ngIf="shouldShowImageCount(alarm)"  class="image-count">
            {{ alarm.picUrls.length }}
          </div>
        </ion-thumbnail>
        <ion-label (click)="seeDetails(alarm)">
          <h2>{{ alarm.remark }}</h2>
          <p>类型: {{ alarm.alarmType }}</p>
          <p>时间: {{ alarm.alarmTime | date:'yyyy-MM-dd HH:mm:ss' }}</p>
        </ion-label>
      </ion-item>
    </ion-list>

    <!-- 只在分页数据格式时显示无限滚动 -->
    <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)">
      <ion-infinite-scroll-content
        style="margin-top: 16px;"
        loadingSpinner="bubbles"
        loadingText="正在加载更多...">
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ng-container>
</ion-content>

<!-- 暂无数据 -->
<ng-template #noData>
  <div class="no-data" style="height: calc(80vh - 48px - 38px);">
    <img src="assets/menu/box2.png" style="padding-top: 50px" />
    <span class="no-data-span">暂无数据</span>
  </div>
</ng-template>

<ion-footer>
  <div class="footer-content" (click)="goBack()">关闭</div>
</ion-footer>
