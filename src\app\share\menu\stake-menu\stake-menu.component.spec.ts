import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef } from '@angular/core';
import { Platform } from '@ionic/angular';
import { of } from 'rxjs';

import { StakeMenuComponent } from './stake-menu.component';
import { ShareModuleService } from '../../share.service';
import { OstTreeListItem } from '../../ost-tree-list/ost-tree-list.service';

describe('StakeMenuComponent', () => {
  let component: StakeMenuComponent;
  let fixture: ComponentFixture<StakeMenuComponent>;
  let mockShareService: jasmine.SpyObj<ShareModuleService>;
  let mockPlatform: jasmine.SpyObj<Platform>;

  const mockStakeData = [
    {
      stakeName: '桩号001',
      stakeCode: 'STAKE001',
      id: '1'
    },
    {
      stakeName: '桩号002',
      stakeCode: 'STAKE002',
      id: '2'
    },
    {
      stakeName: '测试桩号003',
      stakeCode: 'STAKE003',
      id: '3'
    }
  ];

  beforeEach(async () => {
    const shareServiceSpy = jasmine.createSpyObj('ShareModuleService', ['getRequest']);
    const platformSpy = jasmine.createSpyObj('Platform', ['height']);

    await TestBed.configureTestingModule({
      declarations: [StakeMenuComponent],
      providers: [
        { provide: ShareModuleService, useValue: shareServiceSpy },
        { provide: Platform, useValue: platformSpy },
        ChangeDetectorRef
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(StakeMenuComponent);
    component = fixture.componentInstance;
    
    mockShareService = TestBed.inject(ShareModuleService) as jasmine.SpyObj<ShareModuleService>;
    mockPlatform = TestBed.inject(Platform) as jasmine.SpyObj<Platform>;

    // 设置默认返回值
    mockPlatform.height.and.returnValue(800);
    mockShareService.getRequest.and.returnValue(of({ data: mockStakeData }));
    
    // 设置必要的输入属性
    component.labelName = 'stakeName';
    component.pipelineId = 'TEST_PIPELINE';
    component.interfaceUrl = '/test/api';
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize screen height on ngOnInit', () => {
    component.ngOnInit();
    expect(component.screenHeight).toBe(694); // 800 - 56 - 50
  });

  it('should load and transform stake data', () => {
    component.loadMenuTree('TEST_PIPELINE');

    expect(mockShareService.getRequest).toHaveBeenCalled();
    
    expect(component.items.length).toBe(3);
    expect(component.items[0].title).toBe('桩号001');
    expect(component.items[0].data.stakeCode).toBe('STAKE001');
    expect(component.stakeTree.length).toBe(3);
  });

  it('should filter stake data correctly', () => {
    // 设置测试数据
    component.stakeTree = component.transformation({ data: mockStakeData });
    
    // 测试搜索功能
    const results = component.filterItems(component.stakeTree, '桩号001');
    
    expect(results.length).toBe(1);
    expect(results[0].title).toBe('桩号001');
  });

  it('should reset search correctly', () => {
    component.stakeTree = component.transformation({ data: mockStakeData });
    component.params = '测试搜索';
    component.items = [];

    component.onReset();

    expect(component.params).toBe('');
    expect(component.items).toEqual(component.stakeTree);
  });

  it('should perform search correctly', () => {
    component.stakeTree = component.transformation({ data: mockStakeData });
    component.params = '测试';

    component.onSearch();

    expect(component.items.length).toBe(1);
    expect(component.items[0].title).toBe('测试桩号003');
  });

  it('should not search when params is empty', () => {
    component.stakeTree = component.transformation({ data: mockStakeData });
    component.items = component.stakeTree;
    component.params = '';

    component.onSearch();

    expect(component.items).toEqual(component.stakeTree);
  });

  it('should handle case-insensitive search', () => {
    component.stakeTree = component.transformation({ data: mockStakeData });
    
    const results = component.filterItems(component.stakeTree, '桩号');
    
    expect(results.length).toBe(3); // 所有桩号都包含"桩号"
  });

  it('should return empty array when filtering with no items', () => {
    const results = component.filterItems([], '测试');
    expect(results).toEqual([]);
  });

  it('should return original items when query is empty', () => {
    const testItems = component.transformation({ data: mockStakeData });
    const results = component.filterItems(testItems, '');
    expect(results).toEqual(testItems);
  });

  it('should transform data correctly for non-paged mode', () => {
    component.isPage = false;
    const result = component.transformation({ data: mockStakeData });
    
    expect(result.length).toBe(3);
    expect(result[0].title).toBe('桩号001');
    expect(result[0].data).toEqual(mockStakeData[0]);
  });

  it('should transform data correctly for paged mode', () => {
    component.isPage = true;
    const result = component.transformation({ data: { records: mockStakeData } });
    
    expect(result.length).toBe(3);
    expect(result[0].title).toBe('桩号001');
    expect(result[0].data).toEqual(mockStakeData[0]);
  });
});
