import { Component, HostListener, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';
import { MapComponent } from '../share/map-component/map/map.component';
import { ModalController, NavController } from '@ionic/angular';
import { AlarmListComponent } from './alarm-list/alarm-list.component';
import { MapService } from '../share/map-component/service';
import { PreviewComponent } from '../share/preview/preview.component';
import { ShareModuleService } from '../share/share.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { RequestResult } from '../@core/base/request-result';
import { Alarm } from './alarm-model-config';
import { ToastService } from '../@core/providers/toast.service';

@Component({
  selector: 'app-alarm',
  templateUrl: './alarm.component.html',
  styleUrls: ['./alarm.component.scss']
})
export class AlarmComponent implements OnInit, OnDestroy {
  // 地图组件
  @ViewChild('ostMap', { static: false }) ostMap: MapComponent;
  unreadCount = '0';
  // 防重复弹窗状态标志
  public modalStates = {
    alarm: false,
    preview: false,
  };
  // 地图加载状态
  public mapLoaded = false;
  // 关闭按钮是否可用
  public closeButtonEnabled = true;
  private destroy$ = new Subject<void>();
  constructor(
    private nav: NavController, private modalCtrl: ModalController,
    private mapService: MapService, private netSer: ShareModuleService,
    private toastSer: ToastService,
  ) { }

  ngOnInit() {
    this.getUnreadCount();
  }

  /**
   * 获取未读数量
   */
  getUnreadCount(): void {
    this.netSer.getRequest({ interfaceUrl: '/work-inspect/api/v2/inspect/alarm/notRead/count', })
      .pipe(takeUntil(this.destroy$))
      .subscribe((result: RequestResult<any>) => {
        const { code, data } = result;
        if (code === 0) {
          this.unreadCount = data;
        }
      });
  }

  /**
   * 地图加载完成后的回调
   */
  onMapLoaded() {
    this.mapLoaded = true;
    // 注册地图组件到地图服务
    this.mapService.registerMapComponent(this.ostMap);
  }

  onAlarmClick() {
    // 打开弹窗时禁用关闭按钮
    this.closeButtonEnabled = false;

    this.createModalWithGuard('alarm', {
      component: AlarmListComponent,
      cssClass: 'alarm-list-modal',
    }, (data: any, role: string) => {
      // 处理从 AlarmListComponent 返回的数据
      if (role === 'detail' && data) {
        const detailInfo = data;
        const geomData = detailInfo.geom;
        if (geomData && geomData.type === 'Point' && geomData.coordinates && geomData.coordinates.length >= 2) {
          const coordinate: [number, number] = geomData.coordinates;
          // 清除之前的标记
          this.mapService.clearMarkers();
          // 在坐标位置添加蓝色点标记
          this.mapService.addImageMarker(coordinate, '/assets/map/point_blue.png', 0.2);
          // 使用地图服务移动地图到摄像头位置，设置缩放级别为18
          this.mapService.moveMapToCoordinate(coordinate, 1000, 18);
        }
        if (detailInfo.fileUrl && detailInfo.fileUrl.length > 0) {
          // 预览图片
          this.previewAvatar(detailInfo.fileUrl[0], detailInfo);
        }
      }
    });
  }

  async previewAvatar(fileUrl: string, alarmInfo: Alarm): Promise<void> {
    // 防止重复打开图片预览
    if (this.modalStates.preview) return;

    this.modalStates.preview = true;
    // 图片预览打开时禁用关闭按钮
    this.closeButtonEnabled = false;

    const modal = await this.modalCtrl.create({
      component: PreviewComponent,
      componentProps: {
        fileUrl, isCardMode: true,
        title: alarmInfo.remark,
        showClearButton: alarmInfo.readStatus === '未读' ? true : false,
      },
      cssClass: 'card-image-modal'
    });
    await modal.present();
    const { data } = await modal.onDidDismiss();

    // 图片预览关闭后重新启用关闭按钮
    this.modalStates.preview = false;
    this.closeButtonEnabled = true;

    if (data?.action === 'clear') {
      this.setReadStatus(alarmInfo.alarmCode);
    }
  }

  /**
   * 设为已读状态
   */
  setReadStatus(alarmCode: string): void {
    this.netSer.putRequest({ alarmCode }, null, { interfaceUrl: '/work-inspect/api/v2/inspect/alarm/msg/status' })
      .pipe(takeUntil(this.destroy$)).subscribe((result: RequestResult<any>) => {
        if (result.code === 0) {
          this.toastSer.presentToast('报警信息已消除', 'success');
          // 刷新未读数量
          this.getUnreadCount();
        }
      });
  }

  onClose() {
    // 如果图片预览模态框正在显示，则不允许关闭页面
    if (this.modalStates.preview) {
      return;
    }

    // 只有在关闭按钮可用时才执行关闭操作
    if (this.closeButtonEnabled) {
      this.nav.back();
    }
  }

  @HostListener('document:ionBackButton', ['$event'])
  onBackButton($event: any): void {
    $event.detail.register(100, async () => {
      $event.stopImmediatePropagation();
      this.onClose();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 通用防重复弹窗方法
   * @param modalType 弹窗类型
   * @param modalConfig 弹窗配置
   * @param callback 弹窗关闭后的回调
   */
  private async createModalWithGuard(
    modalType: 'alarm' | 'preview',
    modalConfig: any,
    callback?: (data: any, role: string) => void
  ): Promise<void> {
    if (this.modalStates[modalType]) return;
    this.modalStates[modalType] = true;

    const modal = await this.modalCtrl.create(modalConfig);
    await modal.present();

    const { data, role } = await modal.onWillDismiss();
    if (callback) {
      callback(data, role);
    }
    this.modalStates[modalType] = false;

    // 弹窗关闭后重新启用关闭按钮
    this.closeButtonEnabled = true;
  }

}
