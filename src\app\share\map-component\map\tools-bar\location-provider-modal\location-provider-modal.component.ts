import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { LocationProviderService, LocationProviderOption } from '../../../service';
import { ToastService } from '../../../../../@core/providers/toast.service';

@Component({
  selector: 'app-location-provider-modal',
  templateUrl: './location-provider-modal.component.html',
  styleUrls: ['./location-provider-modal.component.scss']
})
export class LocationProviderModalComponent implements OnInit {
  locationProviderOptions: LocationProviderOption[] = [];
  currentProvider: number = 3;

  constructor(
    private modalController: ModalController,
    private locationProviderService: LocationProviderService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    this.locationProviderOptions = this.locationProviderService.locationProviderOptions;
    this.currentProvider = this.locationProviderService.getCurrentProvider();
  }

  /**
   * 选择定位模式
   */
  async selectProvider(provider: number): Promise<void> {
    try {
      // 获取切换前的GeolocationConfig状态（用于调试）
      const beforeConfig = await this.locationProviderService.getCurrentGeolocationConfigProvider();
      console.log('切换前GeolocationConfig.locationProvider:', beforeConfig);

      await this.locationProviderService.setProvider(provider);
      this.currentProvider = provider;

      // 获取切换后的GeolocationConfig状态（用于调试）
      const afterConfig = await this.locationProviderService.getCurrentGeolocationConfigProvider();
      console.log('切换后GeolocationConfig.locationProvider:', afterConfig);

      const label = this.locationProviderService.getProviderLabel(provider);
      this.toastService.presentToast(`已切换到${label}，正在应用新的定位模式`, 'success');

      // 关闭弹窗并返回选择的模式
      await this.modalController.dismiss({
        selectedProvider: provider
      }, 'confirm');
    } catch (error) {
      console.error('切换定位模式失败', error);
      this.toastService.presentToast('切换定位模式失败', 'danger');
    }
  }

  /**
   * 关闭弹窗
   */
  async dismiss(): Promise<void> {
    await this.modalController.dismiss(null, 'cancel');
  }
}
