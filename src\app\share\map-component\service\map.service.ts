import { Injectable } from '@angular/core';
import { Coordinate } from 'ol/coordinate';
import { Feature } from 'ol';
import Geometry from 'ol/geom/Geometry';
import Point from 'ol/geom/Point';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Icon, Style } from 'ol/style';
import { MapComponent } from '../map/map.component';

@Injectable({
  providedIn: 'root'
})
export class MapService {
  private mapComponent: MapComponent | null = null;
  private markerLayer: VectorLayer<VectorSource<Geometry>> | null = null;

  /**
   * 注册地图组件实例
   * @param mapComponent 地图组件实例
   */
  registerMapComponent(mapComponent: MapComponent): void {
    this.mapComponent = mapComponent;
    // 创建标记图层
    this.createMarkerLayer();
  }

  /**
   * 注销地图组件实例
   */
  unregisterMapComponent(): void {
    this.mapComponent = null;
    this.markerLayer = null;
  }

  /**
   * 获取地图组件实例
   * @returns 地图组件实例
   */
  getMapComponent(): MapComponent | null {
    return this.mapComponent;
  }

  /**
   * 创建标记图层
   */
  private createMarkerLayer(): void {
    if (this.mapComponent && this.mapComponent.map) {
      this.markerLayer = new VectorLayer({
        source: new VectorSource()
      });
      this.markerLayer.set('serverType', 'markerLayer');
      this.mapComponent.map.addLayer(this.markerLayer);
    }
  }

  /**
   * 在指定坐标添加图片标记
   * @param coordinate 坐标
   * @param imageSrc 图片路径
   * @param scale 缩放比例（可选，默认为1）
   * @returns 创建的标记要素
   */
  addImageMarker(coordinate: Coordinate, imageSrc: string, scale: number = 1): Feature<Point> | null {
    if (!this.markerLayer) {
      console.error('Marker layer not initialized');
      return null;
    }

    // 创建标记样式
    const markerStyle = new Style({
      image: new Icon({
        src: imageSrc,
        scale: scale,
        anchor: [0.5, 1], // 图片底部中心对齐到坐标点
      })
    });

    // 创建标记要素
    const markerFeature = new Feature({
      geometry: new Point(coordinate)
    });
    markerFeature.setStyle(markerStyle);

    // 添加到图层
    this.markerLayer.getSource().addFeature(markerFeature);

    return markerFeature;
  }

  /**
   * 清除所有标记
   */
  clearMarkers(): void {
    if (this.markerLayer) {
      this.markerLayer.getSource().clear();
    }
  }

  /**
   * 设置地图中心位置
   * @param coordinate 坐标
   * @param accuracy 精度
   * @param followView 是否跟随视图
   */
  setMapCenter(coordinate: Coordinate, accuracy: number = 0, followView: boolean = true): void {
    if (this.mapComponent) {
      this.mapComponent.setCurrentLocation(coordinate, accuracy, followView);
    }
  }

  /**
   * 移动地图到指定坐标
   * @param coordinate 坐标
   * @param duration 动画持续时间（毫秒）
   * @param targetZoom 目标缩放级别（可选）
   */
  moveMapToCoordinate(coordinate: Coordinate, duration: number = 500, targetZoom?: number): void {
    if (this.mapComponent && this.mapComponent.view) {
      // 如果没有指定目标缩放级别，则使用当前缩放级别
      const zoom = targetZoom !== undefined ? targetZoom : this.mapComponent.view.getZoom();
      this.mapComponent.view.animate({ 
        center: coordinate, 
        zoom: zoom,
        duration 
      });
    }
  }
} 