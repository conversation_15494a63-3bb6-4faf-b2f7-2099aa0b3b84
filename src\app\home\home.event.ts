import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { filter } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class PageEventService {
  // 使用一个Subject，它接受一个泛型对象，包含页面类型和数据
  private pageDataUpdate$: Subject<{ pageType: string, data: any }> = new Subject();

  /**
   * 发送数据更新事件
   * @param pageType 页面类型，用于区分不同的页面
   * @param data 需要传递的数据
   */
  sendData(pageType: string, data: any): void {
    this.pageDataUpdate$.next({ pageType, data });
  }

  /**
   * 订阅特定页面类型的数据更新事件
   * @param pageType 要监听的页面类型
   * @returns Observable，当有匹配类型的数据更新时发出
   */
  receiveDataForPageType(pageType: string): any {
    return this.pageDataUpdate$.pipe(
      filter(event => event.pageType === pageType), // 过滤出指定页面类型的数据更新
    );
  }
}
