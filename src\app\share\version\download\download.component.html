<div class="download-container">
  <!-- 头部 -->
  <div class="download-header">
    版本更新中
  </div>
  <!-- 内容 -->
  <div class="download-context">
    <!-- 下载中或下载完成时显示进度条 -->
    <div *ngIf="state === 0 || state === 1">
      <div>下载进度</div>
      <ion-progress-bar [value]="progress"></ion-progress-bar>
    </div>
    <!-- 下载失败时显示错误信息 -->
    <div *ngIf="state === 2" class="error-message">
      <div class="error-text">
        下载失败，网络连接可能不稳定。<br>
        请点击下方按钮复制下载地址，<br>
        在浏览器中手动下载安装包。
      </div>
    </div>
  </div>
  <!-- 按钮 -->
  <div class="download-footer">
    <!-- 下载中 -->
    <div *ngIf="state === 0">
      下载中请勿操作手机
    </div>
    <!-- 下载完成 -->
    <button
      class="btn-install"
      *ngIf="state === 1"
      (click)="onInstallApp()"
    >
      立即安装
    </button>
    <!-- 下载失败 -->
    <button
      class="btn-copy"
      *ngIf="state === 2"
      (click)="onCopyDownloadUrl()"
    >
      <ion-icon name="copy-outline" slot="start"></ion-icon>
      复制下载地址
    </button>
  </div>
</div>