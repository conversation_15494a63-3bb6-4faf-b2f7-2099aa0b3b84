import { Component, Input, OnInit } from '@angular/core';
import { OstTreeListItem } from 'src/app/share/ost-tree-list/ost-tree-list.service';
import { InputSearchSourceService } from '../../input-search-source.service';

@Component({
  selector: 'search-source-stake',
  templateUrl: './search-source-stake.component.html',
  styleUrls: ['./search-source-stake.component.scss']
})
export class SearchSourceStakeComponent implements OnInit {
  @Input() showSearch: boolean = true;
  // 管线ID
  @Input() pipelineId: string;
  // 请求地址
  @Input() interfaceUrl: string = '/work-basic/api/v2/basic/stake/list';
  // 是否分页
  @Input() isPage: boolean = false;
  // 标签项名称
  @Input() labelName: string = 'stakeName';
  // 标签项值
  @Input() labelValue: string = 'stakeCode';
  // 搜索框占位符文本
  @Input() searchPlaceholder: string = '请输入桩号名称';
  // 搜索字段名称
  @Input() searchFieldName: string = 'keyword';
  // 加载提示文本
  @Input() loadingText: string = '数据加载中...';
  constructor(public searchSer: InputSearchSourceService) { }

  ngOnInit(): void { }

  onToggleSubMenu(item: OstTreeListItem): void {
    this.searchSer.change(item.data[this.labelName], item.data[this.labelValue]);
  }
  onItemClick(item: OstTreeListItem): void {
    this.searchSer.change(item.data[this.labelName], item.data[this.labelValue]);
  }

}
