import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';

/**
 * AES 加密解密服务
 */
@Injectable({
  providedIn: 'root'
})
export class AesEncryptionService {
  private readonly aesKey = 'G7f$2kL9@q3EwP8*';
  private readonly keyWordArray: CryptoJS.lib.WordArray;
  private readonly encryptionConfig: any;

  // URL编码映射表
  private readonly urlEncodeMap = new Map([
    ['+', '%2B'], ['/', '%2F'], ['=', '%3D'], [' ', '%20'],
    ['*', '%2A'], ['!', '%21'], ["'", '%27'], ['(', '%28'], [')', '%29']
  ]);

  private readonly urlDecodeMap = new Map([
    ['%2B', '+'], ['%2F', '/'], ['%3D', '='], ['%20', ' '],
    ['%2A', '*'], ['%21', '!'], ['%27', "'"], ['%28', '('], ['%29', ')']
  ]);

  constructor() {
    // 预计算密钥和配置，避免重复计算
    this.keyWordArray = CryptoJS.enc.Utf8.parse(this.aesKey);
    this.encryptionConfig = {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
      format: CryptoJS.format.Hex
    };
  }

  /**
   * AES 加密数据（带URL编码）
   * @param data 要加密的数据对象
   * @returns 加密后的 Base64 字符串（已进行 URL 编码）
   */
  encryptData(data: any): string {
    const base64Result = this.performEncryption(data);
    return this.encodeSpecialCharacters(base64Result);
  }

  /**
   * AES 解密数据（带URL解码）
   * @param encryptedData 加密的数据字符串
   * @returns 解密后的原始数据对象
   */
  decryptData(encryptedData: string): any {
    const decodedData = this.decodeSpecialCharacters(encryptedData);
    return this.performDecryption(decodedData);
  }

  /**
   * AES 加密数据（原始格式，不进行URL编码）
   * @param data 要加密的数据对象
   * @returns 原始的 Base64 加密字符串
   */
  encryptDataRaw(data: any): string {
    return this.performEncryption(data);
  }

  /**
   * AES 解密数据（原始格式，不进行URL解码）
   * @param encryptedData 原始的 Base64 加密字符串
   * @returns 解密后的原始数据对象
   */
  decryptDataRaw(encryptedData: string): any {
    return this.performDecryption(encryptedData);
  }

  /**
   * 验证加密解密的完整性
   * @param data 原始数据
   * @returns 验证结果
   */
  validateEncryption(data: any): { success: boolean; encrypted: string; decrypted: any; isValid: boolean } {
    try {
      const encrypted = this.encryptDataRaw(data);
      const decrypted = this.decryptDataRaw(encrypted);
      const isValid = JSON.stringify(data) === JSON.stringify(decrypted);

      return { success: true, encrypted, decrypted, isValid };
    } catch (error) {
      return { success: false, encrypted: '', decrypted: null, isValid: false };
    }
  }

  /**
   * 核心加密方法
   * @param data 要加密的数据对象
   * @returns Base64 加密字符串
   */
  private performEncryption(data: any): string {
    try {
      const jsonString = JSON.stringify(data);
      const encrypted = CryptoJS.AES.encrypt(jsonString, this.keyWordArray, this.encryptionConfig);
      return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
    } catch (error) {
      console.error('AES加密失败:', error);
      throw error;
    }
  }

  /**
   * 核心解密方法
   * @param encryptedData Base64 加密字符串
   * @returns 解密后的原始数据对象
   */
  private performDecryption(encryptedData: string): any {
    try {
      const ciphertext = CryptoJS.enc.Base64.parse(encryptedData);
      const cipherParams = CryptoJS.lib.CipherParams.create({ ciphertext });
      const decrypted = CryptoJS.AES.decrypt(cipherParams, this.keyWordArray, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });

      const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
      if (!jsonString) {
        throw new Error('解密后的数据为空，可能密钥不正确');
      }

      return JSON.parse(jsonString);
    } catch (error) {
      console.error('AES解密失败:', error);
      throw error;
    }
  }



  /**
   * 对特殊字符进行URL编码（优化版本）
   * @param str 要编码的字符串
   * @returns 编码后的字符串
   */
  private encodeSpecialCharacters(str: string): string {
    let result = str;
    for (const [char, encoded] of this.urlEncodeMap) {
      result = result.replace(new RegExp('\\' + char, 'g'), encoded);
    }
    return result;
  }

  /**
   * 对特殊字符进行URL解码（优化版本）
   * @param str 要解码的字符串
   * @returns 解码后的字符串
   */
  private decodeSpecialCharacters(str: string): string {
    let result = str;
    for (const [encoded, char] of this.urlDecodeMap) {
      result = result.replace(new RegExp(encoded, 'g'), char);
    }
    return result;
  }

  /**
   * 获取当前使用的密钥（用于调试）
   * @returns AES密钥
   */
  getAesKey(): string {
    return this.aesKey;
  }
}
