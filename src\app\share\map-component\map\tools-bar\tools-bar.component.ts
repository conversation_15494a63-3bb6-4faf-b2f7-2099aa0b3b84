import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Coordinates, Geolocation } from '@ionic-native/geolocation/ngx';
import { Alert<PERSON>ontroller, ModalController, PopoverController } from '@ionic/angular';
import { Collection, Map } from 'ol';
import BaseLayer from 'ol/layer/Base';
import LayerGroup from 'ol/layer/Group';
import { Subject } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { MapSwitchComponent } from './ost-map-switch/ost-map-switch.component';
import { LocationProviderService } from '../../service';
import { LocationProviderModalComponent } from './location-provider-modal/location-provider-modal.component';

@Component({
  selector: 'ost-tools-bar',
  templateUrl: './tools-bar.component.html',
  styleUrls: ['./tools-bar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolsBarComponent implements OnInit, OnDestroy {
  // 创建地图
  @Input() map: Map;
  // 创建地图
  @Input() baseLayer: LayerGroup;
  // 基础图层集合
  @Input() baseLayerList: Collection<LayerGroup> = new Collection();
  // 创建业务图层
  @Input() businessLayer!: LayerGroup;
  // 业务图层集合
  @Input() businessLayerList: Collection<BaseLayer> = new Collection();
  // 是否显示定位模式选择按钮
  @Input() showLocationProviderButton = false;
  // 定位监听
  @Output() location: EventEmitter<Coordinates> = new EventEmitter();
  // 刷新图层监听
  @Output() refreshLayer: EventEmitter<any> = new EventEmitter();
  zoomChange$ = new Subject<number>();
  // 地图缩放级别
  zoomLevel: number | undefined;
  // spinner
  spinnerState = false;
  // 当前定位模式
  currentLocationProvider = 3;
  private readonly ngUnsubscribe = new Subject<void>(); // 用于统一管理所有可取消的订阅
  constructor(
    private cd: ChangeDetectorRef,
    private geolocation: Geolocation,
    public popoverController: PopoverController,
    public modalController: ModalController,
    private alertController: AlertController,
    private locationProviderService: LocationProviderService,
  ) { }
  ngOnInit(): void {
    this.setupZoomListener();
    this.setupMapMoveEndListener();
    this.initLocationProvider();
  }

  /**
   * 初始化定位模式
   */
  private initLocationProvider(): void {
    try {
      // 监听定位模式变化
      this.locationProviderService.onProviderChange()
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe(provider => {
          this.currentLocationProvider = provider;
          this.cd.detectChanges();
        });

      // 设置初始值
      this.currentLocationProvider = this.locationProviderService.getCurrentProvider();
    } catch (error) {
      console.error('初始化定位模式服务失败:', error);
    }
  }

  private setupZoomListener() {
    this.zoomChange$
      .pipe(
        throttleTime(500),
        // distinctUntilChanged(), // 避免连续相同的zoomLevel触发动画
        takeUntil(this.ngUnsubscribe) // 用于在ngOnDestroy中取消订阅
      )
      .subscribe(zoomDelta => {
        this.map.getView().animate({ zoom: this.map.getView().getZoom() + zoomDelta });
      });

    this.zoomLevel = this.map.getView().getZoom();
  }

  private setupMapMoveEndListener() {
    this.map.on('moveend', () => {
      this.zoomLevel = Math.floor(this.map.getView().getZoom() || 0);
      this.cd.detectChanges();
    });
  }

  /**
   * 获取当前位置 - 使用Geolocation插件避免与巡检轨迹冲突
   */
  async onLocation(): Promise<void> {
    try {
      this.spinnerState = true;
      console.log('🗺️ 开始地图定位（使用Geolocation插件）');

      // 使用Geolocation插件进行高精度定位
      const maxRetries = 2;
      const targetAccuracy = 10;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`🎯 第${attempt}次定位尝试`);

          const options = {
            enableHighAccuracy: true,
            timeout: attempt === 1 ? 10000 : 15000,
            maximumAge: 0
          };

          const position = await this.geolocation.getCurrentPosition(options);
          const coords = position.coords;

          console.log('📍 定位结果:', {
            accuracy: coords.accuracy,
            longitude: coords.longitude,
            latitude: coords.latitude,
            attempt: attempt
          });

          // 检查精度是否满足要求
          if (coords.accuracy <= targetAccuracy) {
            console.log('✅ 地图定位成功 (精度满足要求)');
            this.location.emit(coords);
            return;
          } else if (attempt === maxRetries) {
            // 最后一次尝试，使用当前结果
            console.log('⚠️ 地图定位完成 (精度:', coords.accuracy + 'm)');
            this.location.emit(coords);
            return;
          }

          // 等待后重试
          console.log(`⏳ 精度不够(${coords.accuracy}m)，等待重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (attemptError) {
          console.error(`❌ 第${attempt}次定位失败:`, attemptError);
          if (attempt === maxRetries) {
            throw attemptError;
          }
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }
    } catch (error) {
      console.error('❌ 地图定位失败:', error);
      // 这里不再有降级方案，因为已经直接使用Geolocation
    } finally {
      this.spinnerState = false;
    }
  }

  /**
   * 打开图层检测
   */
  async openSwitch(): Promise<void> {
    const opts = {
      component: MapSwitchComponent,
      componentProps: {
        baseLayer: this.baseLayer,
        baseLayerList: this.baseLayerList,
        businessLayer: this.businessLayer,
        businessLayerList: this.businessLayerList
      },
      translucent: true
    };
    const popover = await this.popoverController.create(opts);
    await popover.present();
  }

  /**
   * 打开定位模式选择弹窗
   */
  async openLocationProviderModal(): Promise<void> {
    try {
      const modal = await this.modalController.create({
        component: LocationProviderModalComponent,
        cssClass: 'camera-list-modal',
        backdropDismiss: true
      });

      await modal.present();

      const { data, role } = await modal.onWillDismiss();
      if (role === 'confirm' && data?.selectedProvider) {
        console.log('定位模式已切换为:', data.selectedProvider);
        // 立即应用新的定位模式到当前的定位服务
        await this.applyNewLocationProvider(data.selectedProvider);
      }
    } catch (error) {
      console.error('打开定位模式选择弹窗失败:', error);

      // 如果模态框创建失败，显示一个简单的alert作为备选方案
      const alert = await this.alertController.create({
        header: '定位模式选择',
        message: '当前定位模式: ' + this.getLocationProviderLabel(),
        buttons: ['确定']
      });
      await alert.present();
    }
  }

  /**
   * 立即应用新的定位模式（仅更新当前组件状态）
   * 注意：ToolsBarComponent现在使用Geolocation插件，不再配置BackgroundGeolocation
   */
  private async applyNewLocationProvider(newProvider: number): Promise<void> {
    try {
      console.log('🗺️ 地图定位模式已更新为:', newProvider);
      // 这里只需要更新当前组件的定位模式状态
      // Geolocation插件不需要像BackgroundGeolocation那样进行配置
    } catch (error) {
      console.warn('应用新定位模式失败:', error);
    }
  }

  /**
   * 获取当前定位模式的图标
   */
  getLocationProviderIcon(): string {
    return this.locationProviderService.getProviderIcon(this.currentLocationProvider);
  }

  /**
   * 获取当前定位模式的标签
   */
  getLocationProviderLabel(): string {
    return this.locationProviderService.getProviderLabel(this.currentLocationProvider);
  }

  /**
   * 调试方法：检查GeolocationConfig同步状态
   */
  async debugGeolocationConfig(): Promise<void> {
    const currentProvider = this.locationProviderService.getCurrentProvider();
    const configProvider = await this.locationProviderService.getCurrentGeolocationConfigProvider();

    console.log('=== 定位配置同步状态检查 ===');
    console.log('当前选择的定位模式:', currentProvider);
    console.log('GeolocationConfig中的定位模式:', configProvider);
    console.log('是否同步:', currentProvider === configProvider ? '✅ 已同步' : '❌ 未同步');
    console.log('================================');
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
    this.zoomChange$.unsubscribe();
  }
}
