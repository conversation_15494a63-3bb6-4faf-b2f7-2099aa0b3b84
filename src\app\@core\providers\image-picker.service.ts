import { Injectable } from '@angular/core';
import { Camera, CameraOptions } from '@ionic-native/camera/ngx';
import { ActionSheetController } from '@ionic/angular';
import { ShareMethodService } from './share-method.service';
import { ToastService } from './toast.service';

@Injectable({ providedIn: 'root' })
export class ImagePickerService {
  constructor(
    private actionSheetCtr: ActionSheetController,
    private camera: Camera,
    private shareMethod: ShareMethodService,
    private toastService: ToastService
  ) {}

  /**
   * 选择图片（相机/相册），并添加水印，返回base64字符串
   * @param options cameraOnly: 是否只允许相机
   */
  async selectImage(options: { cameraOnly?: boolean } = {}): Promise<string | null> {
    const { cameraOnly = false } = options;
    try {
      // 数量限制判断由外部组件控制
      if (cameraOnly) {
        return await this.openCamera();
      }
      // 动态生成按钮
      const buttons = [
        {
          text: '相机',
          icon: 'camera-outline',
          handler: () => {
            (actionSheet as any).dismiss('camera', undefined, undefined);
          }
        },
        {
          text: '相册',
          icon: 'image-outline',
          handler: () => {
            (actionSheet as any).dismiss('gallery', undefined, undefined);
          }
        }
      ];
      const actionSheet = await this.actionSheetCtr.create({
        buttons: cameraOnly ? [buttons[0]] : buttons
      });
      return new Promise<string | null>((resolve) => {
        actionSheet.onDidDismiss().then(async (data) => {
          if (data && data.data === 'camera') {
            resolve(await this.openCamera());
          } else if (data && data.data === 'gallery') {
            resolve(await this.openGallery());
          } else {
            resolve(null);
          }
        });
        actionSheet.present();
      });
    } catch (e) {
      this.toastService.presentToast('图片选择失败', 'danger');
      return null;
    }
  }

  /**
   * 打开相机
   */
  private async openCamera(): Promise<string | null> {
    const options: CameraOptions = {
      quality: 100,
      destinationType: this.camera.DestinationType.DATA_URL,
      encodingType: this.camera.EncodingType.JPEG,
      mediaType: this.camera.MediaType.PICTURE,
      sourceType: this.camera.PictureSourceType.CAMERA,
      allowEdit: false,
      correctOrientation: true,
    };
    try {
      const imageData = await this.camera.getPicture(options);
      const timestamp = this.shareMethod.formatDateTime(new Date());
      return await this.shareMethod.addTimestampWatermarkToBase64(imageData, timestamp);
    } catch (e) {
      this.toastService.presentToast('未选择图像', 'warning');
      return null;
    }
  }

  /**
   * 打开相册
   */
  private async openGallery(): Promise<string | null> {
    const options: CameraOptions = {
      sourceType: this.camera.PictureSourceType.PHOTOLIBRARY,
      destinationType: this.camera.DestinationType.DATA_URL
    };
    try {
      const imageData = await this.camera.getPicture(options);
      return imageData ? 'data:image/jpeg;base64,' + imageData : null;
    } catch (e) {
      this.toastService.presentToast('未选择图像', 'warning');
      return null;
    }
  }
} 