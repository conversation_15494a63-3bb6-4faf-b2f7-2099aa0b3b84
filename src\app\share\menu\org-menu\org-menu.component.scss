.search-bar {
  height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  padding-left: 16px;
  position: relative;
  background-color: #f6f6f6; //3880ff

  p {
    height: 40px;
    line-height: 40px;
    position: absolute;
    font-size: 14px;
    right: 16px;
    color: #fff;
  }

  .search-bar-parent {
    color: black;
    width: 70%;
    height: 40px;
    padding: 0 15px;
    font-size: 14px;
    background-color: #fff;
    border-radius: 3px;
    display: flex;
    align-items: center;

    ::-webkit-input-placeholder {
      color: #666666;
    }

    display: flex;
    align-items: center;

    .login-form-input-icon {
      padding-right: 8px;
      text-align: center;
      height: 50%;
    }

    ion-input {
      padding-left: 6px;
    }
  }

  .title-end-operate {
    color: black;
  }
}

.tip{
  display: flex;
  margin: 6px 12px;
  align-items: center;
  ion-icon{
    font-size: 18px;
    margin-right: 6px;
  }
}