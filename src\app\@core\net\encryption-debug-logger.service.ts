import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

/**
 * 加密调试日志服务
 * 专门处理加密相关的调试日志输出
 */
@Injectable({
  providedIn: 'root'
})
export class EncryptionDebugLoggerService {

  /**
   * 记录加密调试日志 - JSON对象格式
   */
  logEncryptionDebug(stage: string, url: string, data?: any): void {
    if (environment.encryption?.debugEnabled) {
      const now = new Date();
      const timestamp = now.toLocaleString('zh-CN') + '.' + now.getMilliseconds().toString().padStart(3, '0');

      const logData = {
        stage: stage,
        timestamp: timestamp,
        url: url.split('?')[0], // 只显示路径部分
        data: this.formatLogData(stage, data)
      };

      // 根据不同阶段使用不同的标识符和图标
      const stageConfig = this.getStageConfig(stage);
      console.log(stageConfig.prefix, logData);
    }
  }

  /**
   * 获取不同阶段的配置信息
   */
  private getStageConfig(stage: string): { prefix: string } {
    switch (stage) {
      case 'REQ-原始':
        return { prefix: '� [请求原始]' };
      case 'REQ-加密':
        return { prefix: '🔐 [请求加密]' };
      case 'RES-解密':
        return { prefix: '✅ [响应解密]' };
      default:
        return { prefix: '🔧 [调试信息]' };
    }
  }

  /**
   * 格式化日志数据，根据不同阶段优化显示内容
   */
  private formatLogData(stage: string, data: any): any {
    if (!data) return null;
    
    switch (stage) {
      case 'REQ-原始':
        return {
          urlParams: data.urlParams || null,
          bodyParams: data.bodyParams || null,
          paramCount: this.getParamCount(data)
        };
      case 'REQ-加密':
        return {
          url: data.url || null,
          body: data.body || null,
          hasEncryptedUrl: data.url?.includes('cip=') || false,
          hasEncryptedBody: !!data.body?.cip,
          encryptedBodyLength: data.body?.cip?.length || 0
        };
      case 'RES-解密':
        // 保留完整的响应数据结构
        const result: any = {
          code: data.code,
          msg: data.msg,
          data: data.data
        };
        
        // 添加数据摘要信息
        if (data.data) {
          result.dataSummary = {
            type: Array.isArray(data.data) ? 'array' : typeof data.data,
            length: Array.isArray(data.data) ? data.data.length : undefined,
            keys: (typeof data.data === 'object' && !Array.isArray(data.data)) 
              ? Object.keys(data.data) : undefined
          };
        }
        
        return result;
      default:
        return data;
    }
  }

  /**
   * 获取参数数量（用于日志显示）
   */
  private getParamCount(data: any): number {
    if (!data) return 0;
    let count = 0;
    if (data.urlParams && typeof data.urlParams === 'object') {
      count += Object.keys(data.urlParams).length;
    }
    if (data.bodyParams && typeof data.bodyParams === 'object') {
      count += Object.keys(data.bodyParams).length;
    }
    return count;
  }

  /**
   * 记录请求原始数据
   */
  logOriginalRequest(url: string, urlParams?: any, bodyParams?: any): void {
    this.logEncryptionDebug('REQ-原始', url, { urlParams, bodyParams });
  }

  /**
   * 记录加密后的请求数据
   */
  logEncryptedRequest(url: string, encryptedUrl?: string, encryptedBody?: any): void {
    this.logEncryptionDebug('REQ-加密', url, { 
      url: encryptedUrl, 
      body: encryptedBody 
    });
  }

  /**
   * 记录解密后的响应数据
   */
  logDecryptedResponse(url: string, responseData: any): void {
    this.logEncryptionDebug('RES-解密', url, responseData);
  }
}
