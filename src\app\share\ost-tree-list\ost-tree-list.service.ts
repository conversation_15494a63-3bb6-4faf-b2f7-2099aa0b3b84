import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, ReplaySubject, Subject } from 'rxjs';
import { share } from 'rxjs/operators';

export interface OstTreeListBag { tag: string; item: OstTreeListItem; }

const itemClick$ = new Subject<OstTreeListBag>();
const addItems$ = new ReplaySubject<{ tag: string; items: OstTreeListItem[] }>(1);
const getSelectedItem$
  = new ReplaySubject<{ tag: string; listener: BehaviorSubject<OstTreeListBag> }>(1);
const itemSelect$ = new ReplaySubject<OstTreeListBag>(1);
const itemHover$ = new ReplaySubject<OstTreeListBag>(1);
const submenuToggle$ = new ReplaySubject<OstTreeListBag>(1);
const collapseAll$ = new ReplaySubject<{ tag: string }>(1);

export class OstTreeListItem {
  /**
   * 标题
   * @type {string}
   */
  title: string;
  /**
   * 图标
   * @type {string}
   */
  icon?: string;
  /**
   *  展开
   * @type {boolean}
   */
  expanded?: boolean;
  /**
   * 子项
   * @type {List<NbMenuItem>}
   */
  children?: OstTreeListItem[];
  /**
   *  隐藏
   * @type {boolean}
   */
  hidden?: boolean;
  /**
   *
   * @type {boolean}
   */
  group?: boolean;
  parent?: OstTreeListItem;
  selected?: boolean;
  data?: any;
  fragment?: string;

  /**
   * @returns 自上而下的项目
   */
  static getParents(item: OstTreeListItem): OstTreeListItem[] {
    const parents = [];

    let parent = item.parent;
    while (parent) {
      parents.unshift(parent);
      parent = parent.parent;
    }
    return parents;
  }

  static isParent(item: OstTreeListItem, possibleChild: OstTreeListItem): boolean {
    return possibleChild.parent
      ? possibleChild.parent === item || this.isParent(item, possibleChild.parent)
      : false;
  }
}

@Injectable({
  providedIn: 'root'
})
export class OstTreeListService {

  constructor() { }

  /**
   * 添加项
   * @param {List<NbMenuItem>} items
   * @param {string} tag
   */
  addItems(items: OstTreeListItem[], tag?: string): void {
    addItems$.next({ tag, items });
  }

  /**
   * 折叠所以菜单项
   * @param {string} tag
   */
  collapseAll(tag?: string): void {
    collapseAll$.next({ tag });
  }

  /**
   * 返回当前选择的项目。不会订阅未来的活动
   * @param {string} tag
   * @returns {Observable<{tag: string; item: NbMenuItem}>}
   */
  getSelectedItem(tag?: string): Observable<OstTreeListBag> {
    const listener = new BehaviorSubject<OstTreeListBag>(null);

    getSelectedItem$.next({ tag, listener });

    return listener.asObservable();
  }

  onItemClick(): Observable<OstTreeListBag> {
    return itemClick$.pipe(share());
  }

  onItemSelect(): Observable<OstTreeListBag> {
    return itemSelect$.pipe(share());
  }

  onItemHover(): Observable<OstTreeListBag> {
    return itemHover$.pipe(share());
  }

  onSubmenuToggle(): Observable<OstTreeListBag> {
    return submenuToggle$.pipe(share());
  }

}
@Injectable()
export class OstTreeLisstInternalService {

  constructor() { }

  prepareItems(items: OstTreeListItem[]): void {
    const defaultItem = new OstTreeListItem();
    items.forEach(i => {
      this.applyDefaults(i, defaultItem);
      this.setParent(i);
    });
  }

  selectItem(item: OstTreeListItem, items: OstTreeListItem[], collapseOther: boolean = false, tag: string): void {
    const unselectedItems = this.resetSelection(items);
    const collapsedItems = collapseOther ? this.collapseItems(items) : [];
    for (const parent of OstTreeListItem.getParents(item)) {
      parent.selected = true;
      // 仅针对之前未选择的项目发出事件（“unselectedItems”包含已选择的项目）
      if (!unselectedItems.includes(parent)) {
        this.itemSelect(parent, tag);
      }
      // 仅针对之前未扩展的项目发出事件。
      // 'collapsedItems'包含已扩展的项目，因此无需发出事件。
      // 如果'collapseOther'为假，则'collapsedItems'为空，
      // 因此也请检查项目是否尚未扩展
      const wasNotExpanded = !parent.expanded;
      parent.expanded = true;
      const i = collapsedItems.indexOf(parent);

      if (i === -1 && wasNotExpanded) {
        this.submenuToggle(parent, tag);
      } else {
        collapsedItems.splice(i, 1);
      }
    }

    item.selected = true;
    // 仅针对之前未选择的项目发出事件（“ unselectedItems”包含已选择的项目）
    if (!unselectedItems.includes(item)) {
      this.itemSelect(item, tag);
    }

    // 展开所有当前选定的项目后未退回的剩余项目
    for (const collapsedItem of collapsedItems) {
      this.submenuToggle(collapsedItem, tag);
    }
  }

  collapseAll(items: OstTreeListItem[], tag: string, except?: OstTreeListItem): void {
    const collapsedItems = this.collapseItems(items, except);

    for (const item of collapsedItems) {
      this.submenuToggle(item, tag);
    }
  }

  onAddItem(): Observable<{ tag: string; items: OstTreeListItem[] }> {
    return addItems$.pipe(share());
  }

  onCollapseAll(): Observable<{ tag: string }> {
    return collapseAll$.pipe(share());
  }

  onGetSelectedItem(): Observable<{ tag: string; listener: BehaviorSubject<OstTreeListBag> }> {
    return getSelectedItem$.pipe(share());
  }

  itemHover(item: OstTreeListItem, tag?: string): void {
    itemHover$.next({ tag, item });
  }

  submenuToggle(item: OstTreeListItem, tag?: string): void {
    submenuToggle$.next({ tag, item });
  }

  itemSelect(item: OstTreeListItem, tag?: string): void {
    itemSelect$.next({ tag, item });
  }

  itemClick(item: OstTreeListItem, tag?: string): void {
    itemClick$.next({ tag, item });
  }

  /**
   * 重置选择
   * @param items
   * @returns
   */
  private resetSelection(items: OstTreeListItem[]): OstTreeListItem[] {
    const unselectedItems = [];

    for (const item of items) {
      if (item.selected) {
        unselectedItems.push(item);
      }
      item.selected = false;

      if (item.children) {
        unselectedItems.push(...this.resetSelection(item.children));
      }
    }

    return unselectedItems;
  }

  /**
   * 彻底折叠所有给定的项目
   */
  private collapseItems(items: OstTreeListItem[], except?: OstTreeListItem): OstTreeListItem[] {
    const collapsedItems = [];

    for (const item of items) {
      if (except && (item === except || OstTreeListItem.isParent(item, except))) {
        continue;
      }

      if (item.expanded) {
        collapsedItems.push(item);
      }
      item.expanded = false;

      if (item.children) {
        collapsedItems.push(...this.collapseItems(item.children));
      }
    }

    return collapsedItems;
  }

  private applyDefaults(item, defaultItem): void {
    const menuItem = { ...item };
    Object.assign(item, defaultItem, menuItem);
    // tslint:disable-next-line:no-unused-expression
    item.children && item.children.forEach((child: any) => {
      this.applyDefaults(child, defaultItem);
    });
  }

  private setParent(item: OstTreeListItem): void {
    // tslint:disable-next-line:no-unused-expression
    item.children && item.children.forEach(child => {
      child.parent = item;
      this.setParent(child);
    });
  }


}

