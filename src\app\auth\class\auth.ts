
/**
 * 登录后返回的信息
 */
export class LoginMsg {
  userCode: string;
  // 账号
  account: string;
  userName: string;
  userPhone: string;
  // 访问token
  token: string;
  // 部门id
  depCodeList: any;
  depCode: string;
  // 部门名称
  depName: string;
  // 角色
  roleType: string;
  // 用户头像
  headSculpture: string;
  // 到期时间
  dateTime: string;
  // 是否强制修改密码
  isUpdatePassWord?: boolean;
}

/**
 * 登录信息
 */
export class LoginInfo {
  account = '';
  userPasd = '';
  autoLogin = true;
}


// RSA 公钥（加密用）
export const RSAPUBLICKEY = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCP3pgAHG2DH5CbJL22tclN39wzU0BJdUlzU2CGa959CXppQrlfFDpjR5Rh08PVwhOnjCPI+S4AloSDgcTzHSlJlXOQU0m2RhCnimozZTWhbI60iduuhRtGaRvvfEJB1KWy+NgXXOoE0bQAZEjNB137stnlqtFWKtZOII+85c83owIDAQAB';