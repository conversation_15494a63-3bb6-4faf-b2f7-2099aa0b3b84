/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import "~@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";
@import "./theme/dialog.scss";
@import "./theme/modal.scss";

// 悬浮样式
.sticky {
  position: sticky;
  top: 0px;
  z-index: 999;
  text-align: center;
}

// 块分割线
.split-line {
  border-bottom: 12px solid #f6f6f6;
}

// ion-item分割线
.full-border {
  border-bottom: 1px solid #f6f6f6;
}

// 标题-返回按钮[用于ion-buttons标签]
.title-start-back {
  position: absolute;
}

// 标题-结尾操作按钮[用于ion-note]
.title-end-operate {
  color: #fff;
  padding-right: 16px;
  position: absolute;
  float: right;
  right: 0;
  z-index: 10;
}

// 内容列表-标注标签开始样式
.note-start {
  padding-right: 0;
  margin-right: 10px;
}

// 内容列表-标注标签结束样式
.note-end {
  padding-left: 0;
  margin-left: 5px;
}

// 地图弹窗关闭按钮
.map-close-btn {
  font-size: 22px;
  width: 35px;
  height: 35px;
  background-color: rgba(88, 88, 88, 0.5);
  text-align: center;
  line-height: 40px;
  margin: 16px;
  color: #fff;
  border-radius: 90px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  pointer-events: all;
}

ion-footer {
  ion-toolbar {
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 8px;
    --padding-bottom: 8px;
    --background: #fff;

    &::before {
      display: none;
    }
  }

  .submit-button {
    margin: 0;
    --border-radius: 4px;
  }
}


ion-item-divider {
  color: #000;
}

.sc-ion-textarea-md-h {
  margin-top: 0px !important;
  --padding-top: 0px !important;
}

// 解决 ion-select组件 interface="popover"时 select-option 无法弹出问题
[popover]:not(:popover-open):not(dialog[open]) {
  display: contents;
}