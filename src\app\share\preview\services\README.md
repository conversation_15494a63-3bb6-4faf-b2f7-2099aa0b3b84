# 图片手势服务 (ImageGestureService)

## 概述

`ImageGestureService` 是一个高度优化的图片手势处理服务，专门用于移动端图片预览组件。该服务采用模块化架构，将手势识别、边界计算、性能优化、惯性滑动等功能分离到独立的服务中，提供了卓越的用户体验和代码可维护性。

## 架构设计

该服务采用分层架构，主要包含以下核心模块：

- **ImageGestureService**：主协调器，统一管理所有手势操作
- **GestureManagerService**：手势识别和事件管理
- **BoundaryCalculatorService**：边界计算和约束逻辑
- **PerformanceOptimizerService**：性能优化和缓存管理
- **InertiaScrollHandlerService**：惯性滑动处理
- **PinchGestureHandlerService**：缩放手势专用处理器
- **PanGestureHandlerService**：拖拽手势专用处理器

## 功能特性

- 🔍 **智能缩放**：支持双指捏合缩放，动态边界约束，防止过度缩放
- 🖱️ **流畅拖拽**：支持图片拖拽移动，严格边界控制，确保图片不会完全移出视野
- 🚀 **惯性滑动**：支持拖拽结束后的惯性滑动效果，提供类似原生应用的体验
- 👆 **双击重置**：双击图片快速恢复到初始状态
- 🎯 **精确边界**：基于图片实际尺寸和容器尺寸的精确边界计算
- 🎨 **平滑动画**：支持缩放、拖拽和重置的平滑过渡效果
- ⚡ **性能优化**：智能缓存、节流控制、减少DOM操作频率
- ⚙️ **高度可配置**：支持自定义缩放范围、阻尼系数、惯性参数等

## 使用方法

### 1. 在组件中注入服务

```typescript
import { ImageGestureService } from './services/image-gesture.service';

constructor(
  private imageGestureService: ImageGestureService
) { }
```

### 2. 初始化手势识别

```typescript
ngAfterViewInit(): void {
  if (this.imageElement && this.imageElement.nativeElement) {
    this.imageGestureService.initializeGestures(
      this.imageElement,
      this.containerElement, // 可选，默认使用图片的父元素
      {
        minScale: 0.8,
        maxScale: 5,
        dampingFactor: 0.3,
        resetAnimationDuration: 300,
        minVisibleRatio: 0.25,
        inertiaEnabled: true,
        inertiaFriction: 0.92,
        inertiaMinVelocity: 1.0,
        inertiaMaxDuration: 800
      }
    );
  }
}
```

### 3. 在模板中使用变换样式

```html
<div class="img-container" #containerElement>
  <img
    #imageElement
    [src]="imageSrc"
    [style.transform]="getTransformStyle()"
    class="preview-image"
  />
</div>
```

### 4. 在组件中添加样式获取方法

```typescript
getTransformStyle(): string {
  return this.imageGestureService.getTransformStyle();
}

// 可选：获取当前手势状态
getGestureState() {
  return this.imageGestureService.getTransformState();
}

// 可选：手动重置图片
resetImage(): void {
  this.imageGestureService.resetTransform();
}
```

### 5. 在组件销毁时清理资源

```typescript
ngOnDestroy(): void {
  this.imageGestureService.destroy();
}
```

## 配置选项

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `minScale` | number | 0.8 | 最小缩放比例 |
| `maxScale` | number | 5 | 最大缩放比例 |
| `dampingFactor` | number | 0.3 | 拖拽阻尼系数，值越小移动越精细 |
| `resetAnimationDuration` | number | 300 | 重置动画持续时间（毫秒） |
| `minVisibleRatio` | number | 0.25 | 图片最小可见区域比例（25%） |

### 惯性滑动配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `inertiaEnabled` | boolean | true | 是否启用惯性滑动 |
| `inertiaFriction` | number | 0.92 | 惯性摩擦系数，值越小停止越快 |
| `inertiaMinVelocity` | number | 1.0 | 最小惯性速度阈值 |
| `inertiaMaxDuration` | number | 800 | 最大惯性滑动时间（毫秒） |

## API 方法

### 主要方法

#### `initializeGestures(imageElement, containerElement?, config?)`
初始化手势识别
- **imageElement**: `ElementRef` - 图片元素引用
- **containerElement**: `HTMLElement` - 容器元素（可选）
- **config**: `Partial<GestureConfig>` - 手势配置（可选）

#### `getTransformState(): ImageGestureState`
获取当前变换状态，包含缩放、位移、拖拽状态等信息

#### `getTransformStyle(): string`
获取CSS变换样式字符串，用于应用到图片元素

#### `resetTransform(): void`
重置图片到初始状态，包含平滑动画效果

#### `destroy(): void`
销毁手势识别，清理所有事件监听器和资源

### 状态接口

#### `ImageGestureState`
```typescript
interface ImageGestureState {
  scale: number;              // 当前缩放比例
  lastScale: number;          // 上次缩放比例
  translateX: number;         // X轴位移
  translateY: number;         // Y轴位移
  lastTranslateX: number;     // 上次X轴位移
  lastTranslateY: number;     // 上次Y轴位移
  isDragging: boolean;        // 是否正在拖拽
  velocityX: number;          // X轴速度
  velocityY: number;          // Y轴速度
  isInertiaScrolling: boolean; // 是否正在惯性滑动
}
```

## CSS 样式类

服务会自动为图片元素添加以下CSS类：

- `.dragging`：拖拽时添加，用于禁用过渡效果
- `.pinching`：缩放时添加，用于禁用过渡效果
- `.resetting`：重置时添加，用于应用重置动画

### 推荐样式配置

```scss
.preview-image {
  transition: transform 0.1s ease-out;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;

  &.dragging,
  &.pinching {
    transition: none;
  }

  &.resetting {
    transition: transform 0.3s ease-out;
  }
}

.img-container {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

## 核心特性详解

### 边界控制系统

服务实现了业界领先的边界控制机制：

- **精确边界计算**：基于图片实际尺寸和容器尺寸的实时边界计算
- **最小可见区域保护**：确保图片始终保持至少25%的可见区域
- **智能拖拽限制**：只有当图片缩放大于1时才允许拖拽移动
- **动态边界调整**：缩放过程中实时调整拖拽边界
- **平滑边界回弹**：超出边界时的平滑回弹动画

### 惯性滑动系统

提供类似原生应用的惯性滑动体验：

- **速度追踪**：实时追踪拖拽速度，计算惯性滑动参数
- **摩擦力模拟**：可配置的摩擦系数，模拟真实的物理效果
- **边界约束**：惯性滑动过程中严格遵守边界限制
- **智能停止**：当速度低于阈值或到达边界时自动停止

### 性能优化系统

- **智能缓存**：缓存DOM查询结果，减少重复计算
- **节流控制**：限制手势事件处理频率，提升流畅度
- **内存管理**：及时清理事件监听器和动画帧，防止内存泄漏
- **批量更新**：合并多个状态更新，减少DOM操作次数

## 最佳实践

### 初始化时机
1. 确保在 `ngAfterViewInit` 中初始化手势，此时DOM元素已经可用
2. 如果容器尺寸是动态的，建议在尺寸稳定后再初始化
3. 避免在图片加载完成前初始化，可能导致尺寸计算错误

### 资源管理
1. **必须**在 `ngOnDestroy` 中调用 `destroy()` 方法清理资源
2. 避免重复初始化同一个图片元素
3. 在路由切换或组件销毁前确保清理完成

### 样式配置
1. 容器元素必须设置 `overflow: hidden`
2. 图片元素建议设置 `user-select: none` 防止选择
3. 确保容器有明确的宽高，避免使用百分比高度

### 性能优化建议
1. 避免在手势处理过程中进行复杂的DOM操作
2. 合理配置 `dampingFactor` 和惯性参数
3. 在低端设备上可以禁用惯性滑动以提升性能

### 移动顺滑度优化
如果希望图片放大后移动更顺滑，可以调整以下参数：

1. **提高 `dampingFactor`**：从默认的 `0.3` 调整到 `0.6-0.8`
2. **优化惯性滑动**：
   - `inertiaFriction`: 从 `0.92` 提高到 `0.95`
   - `inertiaMinVelocity`: 从 `1.0` 降低到 `0.5`
   - `inertiaMaxDuration`: 从 `800` 增加到 `1200`

```typescript
// 顺滑度优化配置示例
{
  dampingFactor: 0.6,        // 提高移动响应性
  inertiaFriction: 0.95,     // 更持久的惯性滑动
  inertiaMinVelocity: 0.5,   // 更敏感的惯性触发
  inertiaMaxDuration: 1200   // 更长的惯性滑动时间
}
```

## 依赖要求

- **HammerJS**: 用于手势识别，确保项目中已正确安装和配置
- **Angular**: 支持Angular 12+版本
- **TypeScript**: 支持TypeScript 4.0+版本

## 兼容性

- ✅ iOS Safari 12+
- ✅ Android Chrome 70+
- ✅ 微信内置浏览器
- ✅ 支付宝内置浏览器
- ⚠️ 部分老旧Android设备可能存在性能问题

## 故障排除

### 常见问题

1. **手势不响应**
   - 检查HammerJS是否正确安装
   - 确认容器元素有明确的尺寸
   - 验证初始化时机是否正确

2. **边界计算错误**
   - 确保图片已完全加载
   - 检查容器尺寸是否稳定
   - 验证CSS样式是否影响尺寸计算

3. **性能问题**
   - 降低 `dampingFactor` 值
   - 禁用惯性滑动
   - 检查是否有其他性能瓶颈

## 示例项目

完整的使用示例可以参考 `PreviewComponent` 的实现，该组件展示了所有功能的最佳实践用法。
