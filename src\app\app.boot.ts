import { Injectable } from '@angular/core';
import { AndroidPermissionResponse, AndroidPermissions } from '@ionic-native/android-permissions/ngx';
import { BackgroundGeolocation, BackgroundGeolocationConfig } from '@ionic-native/background-geolocation/ngx';
import { Platform } from '@ionic/angular';
import { Observable, Subject, from, zip } from 'rxjs';
import { loadNetWorkConfig } from './@core/base/environment';
import { RequestResult } from './@core/base/request-result';
import { StorageService } from './@core/providers/storage.service';
import { NetworkService } from './@core/providers/network.service';
import { VersionInfo } from './share/version/class/version-info';
import { VersionCheckService } from './share/version/version-check.service';
import { LocationProviderService } from './share/map-component/service';

@Injectable({
  providedIn: 'root'
})
export class AppBootService {
  // 更新通知
  versionChange$ = new Subject();
  // 启动完成
  appBootStateChange$ = new Subject();
  constructor(
    public backgroundGeolocation: BackgroundGeolocation,
    private androidPermissions: AndroidPermissions,
    public platform: Platform, private storage: StorageService,
    private networkService: NetworkService,
    private versionSer: VersionCheckService,
    private locationProviderService: LocationProviderService
  ) { }
  /**
   * 全局只运行一次
   */
  async boot(): Promise<void> {
    try {
      // 加载网络配置
      loadNetWorkConfig();
      // 初始化平台
      this.initPlatform();
      // 通知启动完成
      this.appBootStateChange$.next(true);
    }
    catch {
      this.appBootStateChange$.error('初始化错误');
    }
  }

  /**
   * 初始化app相关配置
   */
  private initPlatform(): void {
    // 如果是Android平台则执行
    if (this.platform.is('cordova')) {
      this.platform.ready().then(this.onPlatformReady);
    }
  }

  /**
   * 平台加载完成
   */
  private onPlatformReady = async () => {
    // 检查权限
    this.checkAndroidPermission();

    // 检测app版本
    if (this.platform.is('android') && this.networkService.isOnline()) {
      this.checkVersion();
    }
    // 初始化后台定位配置
    await this.initGeolocationConfig();
  }

  /**
   * 初始化后台定位配置
   * 优化点：
   * 1. 避免多余的remove操作，直接set会覆盖旧值，无需先remove
   * 2. 避免多次subscribe嵌套，统一处理异步
   * 3. 增加异常捕获，保证流程健壮
   * 4. 使用用户选择的定位模式，如果没有配置则默认使用混合定位模式
   */
  async initGeolocationConfig(): Promise<void> {
    try {
      const existingConfig = await this.storage.get('GeolocationConfig').toPromise();

      if (!existingConfig) {
        // 如果没有现有配置，创建默认配置（使用混合定位模式）
        console.log('未找到现有定位配置，创建默认配置（混合定位模式）');
        const defaultConfig = this.createGeolocationConfig(3); // 默认使用混合定位
        await this.storage.set('GeolocationConfig', defaultConfig).toPromise();
      } else {
        // 如果有现有配置，检查是否需要更新locationProvider
        const userLocationProvider = this.locationProviderService.getCurrentProvider();

        // 只有当用户选择的定位模式与现有配置不同时才更新
        if (existingConfig.locationProvider !== userLocationProvider) {
          console.log(`更新定位模式: ${existingConfig.locationProvider} -> ${userLocationProvider}`);
          const updatedConfig = { ...existingConfig, locationProvider: userLocationProvider };
          await this.storage.set('GeolocationConfig', updatedConfig).toPromise();
        }
      }
    } catch (error) {
      console.error('初始化定位配置失败', error);

      // 发生错误时，确保至少有一个基本的配置
      try {
        const fallbackConfig = this.createGeolocationConfig(3); // 使用混合定位作为备选
        await this.storage.set('GeolocationConfig', fallbackConfig).toPromise();
        console.log('已创建备选定位配置');
      } catch (fallbackError) {
        console.error('创建备选定位配置也失败了', fallbackError);
      }
    }
  }

  /**
   * 创建定位配置
   */
  private createGeolocationConfig(locationProvider: number): BackgroundGeolocationConfig {
    return {
      desiredAccuracy: 5, // 位置精度（单位：米）
      stationaryRadius: 0, // 静止半径
      distanceFilter: 2, // 移动的最小距离（单位：米），才能触发新的位置事件
      fastestInterval: 200, // 最短时间间隔（单位：毫秒）
      interval: 200, // 位置更新频率（单位：毫秒）
      notificationTitle: '巡检app', // 通知栏标题
      notificationText: '正在定位当中...', // 通知栏文本
      syncThreshold: 100, // 等待时长（单位：毫秒）如果在此时间内无法同步，数据将被缓存并在以后尝试再次同步。
      maxLocations: 50000, // 允许缓存的最大位置数量
      locationProvider, // 使用用户选择的位置数据来源

      // 🔑 关键：锁屏状态下正常工作的核心配置
      stopOnTerminate: false, // ✅ 必须：应用终止时不停止定位服务
      startForeground: true, // ✅ 必须：启用前台服务，确保后台运行
      pauseLocationUpdates: false, // ✅ 必须：应用暂停时不暂停位置更新
      saveBatteryOnBackground: false, // ✅ 推荐：后台不节省电池（确保持续定位）
    };
  }


  /**
   * 权限检测
   */
  checkVersion(): void {
    this.versionSer.CheckVersion()
      .subscribe(
        (ret: RequestResult<VersionInfo>) => this.versionChange$.next(ret),
        error => this.versionChange$.error(error));
  }

  /**
   * 权限检查
   */
  checkAndroidPermission(): void {
    const PERMISSIONS = [
      // 定位相关权限
      this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION,
      this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION,
      this.androidPermissions.PERMISSION.ACCESS_BACKGROUND_LOCATION,

      // 网络相关权限
      this.androidPermissions.PERMISSION.ACCESS_NETWORK_STATE,
      this.androidPermissions.PERMISSION.INTERNET,

      // 后台服务和唤醒锁权限
      this.androidPermissions.PERMISSION.FOREGROUND_SERVICE,
      this.androidPermissions.PERMISSION.WAKE_LOCK,

      // 关键点提醒相关权限
      this.androidPermissions.PERMISSION.VIBRATE,
      this.androidPermissions.PERMISSION.MODIFY_AUDIO_SETTINGS,

      // 其他权限
      this.androidPermissions.PERMISSION.SUBSCRIBED_FEEDS_WRITE,
      this.androidPermissions.PERMISSION.SUBSCRIBED_FEEDS_READ,
      this.androidPermissions.PERMISSION.REQUEST_INSTALL_PACKAGES,
      this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE,
    ];
    const checkPermissions = PERMISSIONS.map(item => from(this.androidPermissions.checkPermission(item)));
    zip(...checkPermissions).subscribe(
      (ret: AndroidPermissionResponse[]) => {
        ret.forEach((value: AndroidPermissionResponse, index: number) => {
          if (!value.hasPermission) {
            this.androidPermissions.requestPermission(PERMISSIONS[index]).then();
          }
        });
      });
  }

  /**
   * 版本变化监听
   */
  onVersionChange(): Observable<any> {
    return this.versionChange$;
  }

  /**
   * 加载完成提醒
   */
  onAppBoot(): Observable<any> {
    return this.appBootStateChange$;
  }
}

