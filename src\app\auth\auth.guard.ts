import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { from, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { UserInfoService } from '../@core/providers/user-Info.service';
import { AuthService } from './auth.service';
import { Network } from '@ionic-native/network/ngx';


@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    public userSer: UserInfoService, public authSer: AuthService,
    private router: Router
  ) { }
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean | UrlTree> {
    return from(this.userSer.init()).pipe(mergeMap(() => this.queryLogin()));
  }

  /**
   * 查询用户登录状态的方法
   * 
   * 此方法用于在用户尝试访问受保护的路由之前，检查用户是否已登录或令牌是否有效
   * 它通过用户服务检查当前登录状态，并根据不同的情况返回Observable，指示用户是否已登录或重定向到登录页面
   * 
   * @returns {Observable<boolean | UrlTree>} 返回一个Observable，如果用户已登录则发出true，否则发出重定向到登录页面的UrlTree
   */
  queryLogin(): Observable<boolean | UrlTree> {
    // 检查用户是否已登录
    if (this.userSer.isLogin()) {
      return of(true);
    } else if (this.userSer.token) {
      // 如果用户存在令牌，则进行令牌有效性验证
      return new Observable(object => {
        this.authSer.tokenValidate().subscribe(result => {
          // 如果令牌验证成功，发出true表示用户已登录
          if (result.code === 0 && result.data === true) {
            this.userSer.updateLoginStatus(true);
            object.next(true);
          } else {
            // 如果令牌验证失败，发出重定向到登录页面的UrlTree
            object.next(this.router.parseUrl('/auth/login'));
          }
        })
      });
    } else {
      // 如果用户未登录且无令牌，发出重定向到登录页面的UrlTree
      return of(this.router.parseUrl('/auth/login'));
    }
  }

}
