import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChangeDetectorRef } from '@angular/core';
import { Platform } from '@ionic/angular';
import { of } from 'rxjs';

import { PipeMenuComponent } from './pipe-menu.component';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { ShareModuleService } from '../../share.service';
import { InputSearchSourceService } from '../../input-search/input-search-source.service';
import { OstTreeListItem } from '../../ost-tree-list/ost-tree-list.service';

describe('PipeMenuComponent', () => {
  let component: PipeMenuComponent;
  let fixture: ComponentFixture<PipeMenuComponent>;
  let mockUserService: jasmine.SpyObj<UserInfoService>;
  let mockShareService: jasmine.SpyObj<ShareModuleService>;
  let mockSearchService: jasmine.SpyObj<InputSearchSourceService>;
  let mockPlatform: jasmine.SpyObj<Platform>;

  const mockPipelineData = [
    {
      pipelineName: '主管线A',
      pipelineCode: 'PIPE001',
      children: [
        {
          pipelineName: '支管线A1',
          pipelineCode: 'PIPE001-1'
        },
        {
          pipelineName: '支管线A2',
          pipelineCode: 'PIPE001-2'
        }
      ]
    },
    {
      pipelineName: '主管线B',
      pipelineCode: 'PIPE002',
      children: [
        {
          pipelineName: '支管线B1',
          pipelineCode: 'PIPE002-1'
        }
      ]
    }
  ];

  beforeEach(async () => {
    const userServiceSpy = jasmine.createSpyObj('UserInfoService', ['isLogin']);
    const shareServiceSpy = jasmine.createSpyObj('ShareModuleService', ['getRequest']);
    const searchServiceSpy = jasmine.createSpyObj('InputSearchSourceService', ['change']);
    const platformSpy = jasmine.createSpyObj('Platform', ['height']);

    await TestBed.configureTestingModule({
      declarations: [PipeMenuComponent],
      providers: [
        { provide: UserInfoService, useValue: userServiceSpy },
        { provide: ShareModuleService, useValue: shareServiceSpy },
        { provide: InputSearchSourceService, useValue: searchServiceSpy },
        { provide: Platform, useValue: platformSpy },
        ChangeDetectorRef
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(PipeMenuComponent);
    component = fixture.componentInstance;
    
    mockUserService = TestBed.inject(UserInfoService) as jasmine.SpyObj<UserInfoService>;
    mockShareService = TestBed.inject(ShareModuleService) as jasmine.SpyObj<ShareModuleService>;
    mockSearchService = TestBed.inject(InputSearchSourceService) as jasmine.SpyObj<InputSearchSourceService>;
    mockPlatform = TestBed.inject(Platform) as jasmine.SpyObj<Platform>;

    // 设置默认返回值
    mockPlatform.height.and.returnValue(800);
    mockShareService.getRequest.and.returnValue(of({ data: mockPipelineData }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize screen height on ngOnInit', () => {
    component.ngOnInit();
    expect(component.screenHeight).toBe(694); // 800 - 56 - 50
  });

  it('should load and transform pipeline data', () => {
    component.interfaceUrl = '/test/api';
    component.loadMenuTree('TEST_DEP');

    expect(mockShareService.getRequest).toHaveBeenCalledWith(jasmine.objectContaining({
      interfaceUrl: '/test/api',
      depCode: 'TEST_DEP'
    }));

    expect(component.items.length).toBe(2);
    expect(component.items[0].title).toBe('主管线A');
    expect(component.items[0].data.pipelineCode).toBe('PIPE001');
    expect(component.items[0].children?.length).toBe(2);
  });

  it('should filter tree data correctly', () => {
    // 设置测试数据
    component.pipeTree = component.transformPipelineData(mockPipelineData);
    
    // 测试搜索功能
    const results = component.filterTree(component.pipeTree, '支管线A');
    
    expect(results.length).toBe(1);
    expect(results[0].title).toBe('主管线A');
    expect(results[0].expanded).toBe(true);
    expect(results[0].children?.length).toBe(2);
  });

  it('should reset search correctly', () => {
    component.pipeTree = component.transformPipelineData(mockPipelineData);
    component.params = '测试搜索';
    component.items = [];

    component.onReset();

    expect(component.params).toBe('');
    expect(component.items).toEqual(component.pipeTree);
  });

  it('should perform search correctly', () => {
    component.pipeTree = component.transformPipelineData(mockPipelineData);
    component.params = '主管线A';

    component.onSearch();

    expect(component.items.length).toBe(1);
    expect(component.items[0].title).toBe('主管线A');
  });

  it('should not search when params is empty', () => {
    component.pipeTree = component.transformPipelineData(mockPipelineData);
    component.items = component.pipeTree;
    component.params = '';

    component.onSearch();

    expect(component.items).toEqual(component.pipeTree);
  });

  it('should handle case-insensitive search', () => {
    component.pipeTree = component.transformPipelineData(mockPipelineData);
    
    const results = component.filterTree(component.pipeTree, '主管线a');
    
    expect(results.length).toBe(1);
    expect(results[0].title).toBe('主管线A');
  });

  it('should find child nodes in search', () => {
    component.pipeTree = component.transformPipelineData(mockPipelineData);
    
    const results = component.filterTree(component.pipeTree, '支管线B1');
    
    expect(results.length).toBe(1);
    expect(results[0].title).toBe('主管线B');
    expect(results[0].expanded).toBe(true);
    expect(results[0].children?.length).toBe(1);
    expect(results[0].children?.[0].title).toBe('支管线B1');
  });
});
