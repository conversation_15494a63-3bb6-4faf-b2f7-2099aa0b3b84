ion-content {
  &.search-visible {
    --height: calc(100vh - 56px - 170px);
  }

  &.search-hidden {
    --height: calc(100vh - 56px);
  }

  .form-search-show {
    height: 170px;
    transform: translate(0, 0);
    transition: all 1s;
    background: #fff;
    border-bottom: 1px solid #eee;
  }

  .form-search-hide {
    transform: translate(0, -101%);
    transition: all 1s;
    background: #fff;
  }

  .content-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    transition: transform 1s;
    transform: translateY(0);
  }

  .content-show {
    transform: translateY(170px);
  }

  .content-hide {
    transform: translate(0, -170px);
    transition: all 1s;
  }

  ion-datetime {
    --margin-inline-start: 0px !important;
    flex: 1; /* 占用剩余所有空间 */
    min-width: 0; /* 防止flex子项溢出 */
    width: 100%;
  }

  .form-button {
    text-align: end;
    margin-right: 16px;
  }
}

// tab 样式
.tabs-container {
  display: flex;
  justify-content: center;
  margin: 6px 0;

  .tabs {
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
    border-radius: 50px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

    .tab-button {
      padding: 10px 20px;
      border: none;
      border-radius: 25px;
      font-size: 16px;
      cursor: pointer;
      background-color: transparent;
      color: #4285F4;
      transition: background-color 0.3s, color 0.3s;
    }

    .tab-button.active {
      background-color: #4285F4;
      color: #ffffff;
    }
  }
}


.site-profile {
  font-family: Arial, sans-serif;

  .task-item {
    margin: 0 6px;
    border-top: 1px solid #f6f6f6;
    border-bottom: 1px solid #f6f6f6;
    margin-bottom: 8px;

    ion-label {
      font-size: 14px;

      .task-p {
        width: 6px;
        height: 20px;
        background-color: var(--ion-color-secondary);
        float: left;
        border-radius: 16px;
        margin: 0 10px 0 0;
      }
    }

    p {
      color: #000;
      font-size: 12px;
      margin: 12px 0 0 0;
    }

    ion-note {
      margin-left: 0;
      padding-left: 0;
    }


  }

  .horizontal-bar-chart {
    border-top: 1px solid #f6f6f6;
    width: 100%;
    padding: 8px;
    background-color: #fff;
  }

  .bar-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .label {
    width: 60px;
    font-size: 14px;
    color: #333;
  }

  .bar {
    width: 70%;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 6px;
  }

  .fill {
    height: 100%;
    background-color: #4285F4;
    border-radius: 10px 0 0 10px;
  }

  .percentage {
    width: 56px;
    text-align: right;
    font-size: 14px;
    color: #333;
  }

  ion-grid {
    border-top: 1px solid #f6f6f6;
    background: #fff;

    .div-box {
      margin: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      // padding: 0;
      .p-img {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        border-radius: 3px;
        height: 28px;
        font-size: 18px;
        text-align: center;
      }

      .name {
        font-size: 12px;
        text-align: center;
        margin-top: 5px;
        color: #000;
      }
    }
  }
}