@import "../../../theme/variables.scss";

.reportBody {
  height: auto;
  width: 100%;
  padding: 1px 0px;
  margin-top: 12px;

  .title {
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    padding: 8px 0px 8px 16px;
    background: var(--ion-color-primary-contrast);

    span {
      color: var(--ion-color-primary);
      font-size: 14px;
      font-weight: 500;
      // height: 18px;
      // line-height: 18px;
      // padding-top: 2px;
    }
  }

  .tabs {
    margin: 0;
    padding: 0;
    border-top: 1px solid #f6f6f6;
    font-size: 12px;
    background: var(--ion-color-primary-contrast);

    .div-box {
      margin: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .p-img {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        border-radius: 3px;
        height: 28px;
        font-size: 18px;
        text-align: center;
      }

      .name {
        font-size: 12px;
        text-align: center;
        margin-top: 5px;
        color: #000;
      }
    }
  }
}