import { Environment } from 'src/app/@core/base/environment';

export const environment: Environment = {
  projectName: '巡检APP',
  production: true,
  openProxy: true,
  version: 'v1.1.2',
  // 加密配置 - 生产环境
  encryption: {
    enabled: true,        // 启用加密
    debugEnabled: false   // 禁用调试日志
  },
  /*********** 内网 145 外网地址 **********/
  api: {
    mapUrl: 'http://**************:8001/geoServerApi',
    ip: '**************',
    port: 8001,
    version: 'v1'
  },

  /*********** 内网 134 外网地址 **********/
  // api: {
  //   ip: '**************',
  //   port: 6001,
  //   version: 'v1'
  // },
};
