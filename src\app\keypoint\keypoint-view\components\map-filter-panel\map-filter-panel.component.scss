// 地图筛选面板样式

.filter-panel {
  display: flex;
  flex-direction: column;
  gap: 6px;
  

}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  
  .filter-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-bottom: 2px;
  }
  
  .filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    
    ion-button {
      --border-radius: 20px;
      --padding-start: 8px;
      --padding-end: 8px;
      --padding-top: 4px;
      --padding-bottom: 4px;
      height: 28px;
      font-size: 13px;
      font-weight: 500;
      
      ion-icon {
        font-size: 14px;
        margin-right: 3px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .filter-panel {
    gap: 8px;
  }
  
  .filter-group {
    gap: 6px;
    
    .filter-label {
      font-size: 13px;
    }
    
    .filter-buttons {
      gap: 4px;
      
      ion-button {
        --padding-start: 8px;
        --padding-end: 8px;
        height: 28px;
        font-size: 12px;
        
        ion-icon {
          font-size: 13px;
          margin-right: 2px;
        }
      }
    }
  }
}

// 按钮状态样式
ion-button {
  &[fill="solid"] {
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &[fill="outline"] {
    --border-width: 1px;
    --border-style: solid;
  }
}

// 特定颜色的按钮样式
ion-button[color="success"] {
  &[fill="solid"] {
    --background: #4CAF50;
    --color: white;
  }
}

ion-button[color="warning"] {
  &[fill="solid"] {
    --background: #FF9800;
    --color: white;
  }
}

ion-button[color="tertiary"] {
  &[fill="solid"] {
    --background: #2196F3;
    --color: white;
  }
}

ion-button[color="danger"] {
  &[fill="solid"] {
    --background: #F44336;
    --color: white;
  }
}