import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { RequestResult } from 'src/app/@core/base/request-result';
import { UserInfoService } from 'src/app/@core/providers/user-Info.service';
import { TaskInfo } from '../class/home';
import { PageEventService } from '../home.event';
import { HomeModuleService } from '../home.service';

@Component({
  selector: 'home-patrol-overview',
  templateUrl: './patrol-overview.component.html',
  styleUrls: ['./patrol-overview.component.scss']
})
export class PatrolOverviewComponent implements OnInit, OnDestroy {
  // 数据模型
  TaskInfo: TaskInfo = new TaskInfo();
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    public userSer: UserInfoService, public netSer: HomeModuleService, public cd: ChangeDetectorRef,
    private dataEvent: PageEventService, public platform: Platform,
  ) { }

  ngOnInit(): void {
    // 页面数据更新
    this.dataEvent.receiveDataForPageType('home')
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: boolean) => {
        if (state) {
          this.loadTask();
        }
      });
    this.loadTask();
  }

  /**
   * 获取巡检任务
   */
  loadTask(): void {
    this.netSer.task()
      .pipe(takeUntil(this.destroy$))
      .subscribe((info: RequestResult<TaskInfo>) => {
        this.TaskInfo.completedNum = info.data.completedNum;
        this.TaskInfo.taskNum = info.data.taskNum;
        this.TaskInfo.unExecutedNum = info.data.unExecutedNum;
        this.cd.markForCheck();
      });
  }

  // 回收
  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

}
