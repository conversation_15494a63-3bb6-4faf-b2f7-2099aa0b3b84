import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ResourceGlobalConfig } from '@ngx-resource/core';
import { Subject } from 'rxjs';
import { DetailsMode } from 'src/app/@core/base/environment';
import { DataSyncManagerService, SyncDataType } from 'src/app/@core/providers/data-sync';
import { ShareMethodService } from 'src/app/@core/providers/share-method.service';
import { ToastService } from 'src/app/@core/providers/toast.service';


/**
 * 未巡检原因上报
 */
@Component({
  selector: 'app-not-inspected',
  templateUrl: './not-inspected.component.html',
  styleUrls: ['./not-inspected.component.scss']
})
export class NotInspectedComponent implements OnInit, OnDestroy {
  // 任务编号
  @Input() taskCode: string;
  // 当前关键点
  @Input() currentKeyPoint: any;
  // 数据模式（新增或查看）
  @Input() modelMode: DetailsMode = DetailsMode.ADD;
  // 是否为详情模式
  @Input() isDetailMode: boolean = false;
  // 提交成功事件
  @Output() submitSuccess = new EventEmitter<{ shouldRefreshKeyPoints: boolean }>();

  reasonText: string = '';
  // 雨天不巡查标识
  isItRaining: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(
    private toastService: ToastService,
    private shareMethod: ShareMethodService,
    private dataSyncManager: DataSyncManagerService
  ) { }

  ngOnInit() {
    // 如果是查看模式且有未巡检原因，回显数据
    if (this.modelMode === DetailsMode.SEE && this.currentKeyPoint?.reason) {
      this.loadExistingData();
    }
  }

  /**
   * 加载已有的未巡检原因数据
   */
  private loadExistingData(): void {
    if (this.currentKeyPoint?.reason) {
      this.reasonText = this.currentKeyPoint.reason;
    }
    // 如果有雨天不巡查标识，也进行回显
    if (this.currentKeyPoint?.isItRaining !== undefined) {
      this.isItRaining = this.currentKeyPoint.isItRaining === '是' || this.currentKeyPoint.isItRaining === true;
    }
  }

  async onSubmit() {
    if (!this.reasonText) {
      this.toastService.presentToast('请填写未巡检原因', 'warning');
      return;
    }

    let params: any;
    let interfaceUrl: string;

    if (this.currentKeyPoint && this.currentKeyPoint.taskPointCode) {
      // 优先按关键点上报未巡检原因
      params = {
        taskPointCode: this.currentKeyPoint.taskPointCode,
        reason: this.reasonText,
        isItRaining: this.isItRaining,
        timestamp: Date.now()
      };
      interfaceUrl = '/work-inspect/api/v2/inspect/app/point/reason';
    } else if (this.taskCode) {
      // 有任务编号但无关键点，按任务上报未巡检原因
      params = {
        taskCode: this.taskCode,
        reason: this.reasonText,
        isItRaining: this.isItRaining,
        timestamp: Date.now()
      };
      interfaceUrl = '/work-inspect/api/v2/inspect/app/task/reason';
    } else {
      // 无法确定上报对象
      this.toastService.presentToast('缺少关键点或任务信息，无法提交', 'danger');
      return;
    }

    // 使用数据同步服务进行缓存和上传
    const result = await this.dataSyncManager.addToCache(
      SyncDataType.NOT_INSPECTED_REASON,
      params,
      `${ResourceGlobalConfig.url}${interfaceUrl}`,
      'PUT'
    );

    if (result.status === 'uploaded' && result.code === 0) {
      this.toastService.presentToast('提交成功！', 'success');
      // 网络通畅且提交成功时，发送刷新关键点数据的信号
      this.submitSuccess.emit({ shouldRefreshKeyPoints: true });
    } else if (result.status === 'cached') {
      this.toastService.presentToast('数据已缓存，网络恢复后自动上传', 'success');
      // 数据缓存时不刷新关键点数据
      this.submitSuccess.emit({ shouldRefreshKeyPoints: false });
    }
  }

  get formattedPoint(): string {
    return this.shareMethod.formatPoint(this.currentKeyPoint?.point);
  }

  /**
   * 是否显示提交按钮
   */
  get showSubmitButton(): boolean {
    return this.modelMode === DetailsMode.ADD;
  }

  /**
   * 是否为查看模式
   */
  get isViewMode(): boolean {
    return this.modelMode === DetailsMode.SEE;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
