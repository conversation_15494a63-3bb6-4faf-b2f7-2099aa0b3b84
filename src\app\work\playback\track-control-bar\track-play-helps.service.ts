import { Injectable } from '@angular/core';
import { Feature } from 'ol';
import { Coordinate } from 'ol/coordinate';
import { getVectorContext } from 'ol/render';
import Geometry from 'ol/geom/Geometry';
import LineString from 'ol/geom/LineString';
import Point from 'ol/geom/Point';
import VectorLayer from 'ol/layer/Vector';
import RenderEvent from 'ol/render/Event';
import VectorSource from 'ol/source/Vector';
import { Fill, Icon, Stroke, Style } from 'ol/style';
import CircleStyle from 'ol/style/Circle';
import { Observable, Subject } from 'rxjs';
import { MapComponent } from 'src/app/share/map-component/map/map.component';

export type PlayState = 'play' | 'pause' | 'stop';
@Injectable({
  providedIn: 'root'
})
export class TrackPlayHelpsService {
  ostMap: MapComponent;
  // 轨迹图层
  trackVectorLayet: VectorLayer<VectorSource<Geometry>> = new VectorLayer({ source: new VectorSource() });
  // 当前移动点样式
  currentPointStyle: Style;
  //  轨迹线要素
  trackLineFeature: Feature<LineString>;
  // 当前运动点要素
  currentPointFeature: Feature<Point>;
  private speed = 50;
  private distance = 0;
  private lastTime = 0;
  // 播放状态可观测对象
  private playState: Subject<PlayState> = new Subject();
  // 播放进度可观测对象
  private playProgress: Subject<number> = new Subject();
  constructor() { }
  /**
   * 初始化
   */
  init(ostMap: MapComponent): void {
    this.ostMap = ostMap;
    this.createCurrentPoint();
    this.createTrackLine();
    this.ostMap.map.addLayer(this.trackVectorLayet);
  }
  /**
   * 创建移动点
   */
  createCurrentPoint(): void {
    this.currentPointStyle = new Style({
      image: new CircleStyle({
        radius: 5,
        fill: new Fill({ color: '#248CFF' }),
        stroke: new Stroke({
          color: 'white',
          width: 2,
        }),
      }),
    });
    this.currentPointFeature = new Feature({
      geometry: new Point([]),
    });
  }

  /**
   * 设置进度
   */
  setProgress(progress: number): void {
    this.distance = progress * 0.01;
  }
  /**
   * 设置播放速度
   */
  setSpeed(speed: number): void {
    this.speed = speed;
  }

  /**
   * 创建管线
   */
  createTrackLine(): void {
    this.trackLineFeature = new Feature({
      geometry: new LineString([]),
    });
  }
  /**
   *  移动轨迹点
   */
  moveFeature = (event: RenderEvent) => {
    const time = event.frameState.time;
    const elapsedTime = time - this.lastTime;
    this.distance = (this.distance + (this.speed * elapsedTime) / 1e6) % 2;
    if (this.distance >= 1) {
      this.trackVectorLayet.un('postrender', this.moveFeature);
      this.distance = 0;
      return;
    }
    this.lastTime = time;
    const currentCoordinate = this.trackLineFeature.getGeometry().getCoordinateAt(this.distance);
    this.currentPointFeature.getGeometry()?.setCoordinates(currentCoordinate || []);
    const vectorContext = getVectorContext(event);
    this.playProgress.next(this.distance > 1 ? 2 - this.distance : this.distance);
    if (this.currentPointStyle) {
      vectorContext.setStyle(this.currentPointStyle);
    }
    if (this.currentPointFeature.getGeometry()) {
      vectorContext.drawGeometry(this.currentPointFeature.getGeometry());
    }
    this.ostMap.map.render();
  }


  /**
   * 创建轨迹
   */
  setTrackByCoordinates(coordinates: Coordinate[]): void {
    this.trackVectorLayet.getSource().clear();
    const track = new LineString(coordinates);
    this.currentPointFeature.getGeometry().setCoordinates(track.getFirstCoordinate());
    this.resetTrack(track);
    if (this.trackLineFeature) {
      this.trackVectorLayet.getSource().addFeature(this.trackLineFeature);
    }
    if (this.currentPointFeature) {
      this.trackVectorLayet.getSource().addFeature(this.currentPointFeature);
    }
  }
  /**
   * 轨迹重置
   */
  private resetTrack(track: LineString): void {
    if (this.trackLineFeature.getGeometry()) {
      this.trackLineFeature.getGeometry().setCoordinates(track.getCoordinates());
      const trackStyle = [
        new Style({
          fill: new Fill({ color: '#0f0' }),
          stroke: new Stroke({
            color: '#0f0',
            width: 3,
          }),
        }),
        new Style({
          geometry: new Point(this.trackLineFeature.getGeometry().getFirstCoordinate()),
          image: new Icon({
            scale: 0.5,
            offset: [0, 0],
            size: [50, 100],
            src: 'assets/map/monitor/map_track_start.png',
          })
        }),
        new Style({
          geometry: new Point(this.trackLineFeature.getGeometry().getLastCoordinate()),
          image: new Icon({
            scale: 0.5,
            offset: [0, 0],
            size: [50, 100],
            src: 'assets/map/monitor/map_track_end.png',
          })
        }),
      ];
      this.trackLineFeature?.setStyle(trackStyle);
      setTimeout(() => {
        this.ostMap.view.fit(this.trackLineFeature.getGeometry().getExtent(), { padding: [50, 50, 50, 50], maxZoom: 18, duration: 500 });
      }, 250);
    }
  }
  /**
   * 播放轨迹
   */
  public play(): void {
    this.lastTime = Date.now();
    this.trackVectorLayet.on('postrender', this.moveFeature);
    if (this.currentPointFeature) {
      this.trackVectorLayet.getSource().removeFeature(this.currentPointFeature);
      this.trackVectorLayet.getSource().addFeature(this.currentPointFeature);
    }
    this.ostMap.map.render();
    this.playState.next('play');
  }

  /**
   * 暂停
   */
  public pause(): void {
    this.trackVectorLayet.un('postrender', this.moveFeature);
    this.playState.next('pause');
  }

  /**
   * 重置
   */
  public stop(): void {
    this.reset();
    this.playState.next('stop');
  }

  public reset(): void {
    this.distance = 0;
    this.trackVectorLayet.un('postrender', this.moveFeature);
    this.trackVectorLayet.getSource().clear();
  }

  /**
   * 播放状态监听
   */
  onPlayState(): Observable<PlayState> {
    return this.playState;
  }

  /**
   * 播放进度监听
   */
  onPlayProgress(): Observable<number> {
    return this.playProgress;
  }
  /**
   * 回收
   */
  public destroy(): void {
    this.ostMap.map.removeLayer(this.trackVectorLayet);
    this.trackVectorLayet.getSource().clear();
    this.trackLineFeature = undefined;
    this.currentPointFeature = undefined;
    this.currentPointStyle = undefined;
    this.ostMap = undefined;
  }
}
