import { DatePipe } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { BackgroundGeolocation, BackgroundGeolocationResponse } from '@ionic-native/background-geolocation/ngx';
import { LoadingController, ModalController } from '@ionic/angular';
import centroid from '@turf/centroid';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PageGridResult } from '../@core/base/request-result';
import { ToastService } from '../@core/providers/toast.service';
import { UserInfoService } from '../@core/providers/user-Info.service';
import { ExecutComponent } from '../execut/execut.component';
import { ExecutService } from '../execut/execut.service';
import { statusColorMap, statusIconMap, Task, TaskParams } from './class/work';
import { TaskDetailComponent } from './detail/detail.component';
import { WorkService } from './work.service';

@Component({
  selector: 'app-work',
  templateUrl: 'work.page.html',
  styleUrls: ['work.page.scss']
})
export class WorkPage implements OnInit, OnDestroy {
  // 检测到存在未上传数据
  onUploadLength = 0;
  // 分页数据
  taskPageResult: PageGridResult<Task[]>;
  // 分页列表数据
  taskGridData: Task[] = [];
  // 搜索条件
  taskParams: TaskParams = new TaskParams();
  // 刷新动画
  loading: any;
  // 下拉刷新事件
  refreshEvent: any;
  // 上拉加载更多事件
  moreEvent: any;
  // 动画类型
  spinnerType = 'bubbles';
  // 加载更多文字提示
  isShowNoMoreStr = '加载更多...';
  // 是否加载中
  isLoading = false;
  // 当前时间
  currentTime = this.dateTransform(new Date(), 'yyyy-MM-dd');
  userId = this.userSer.userId;
  // 用于取消所有订阅的信号
  private destroy$ = new Subject<void>();
  constructor(
    public backgroundGeolocation: BackgroundGeolocation,
    private workService: WorkService, private datePipe: DatePipe,
    private modalCtrl: ModalController, private exeSer: ExecutService,
    private loadingCtrl: LoadingController,
    private userSer: UserInfoService,
    private toastSer: ToastService
  ) { }
  ngOnInit(): void {
    // 检测未上传数据
    this.checkData();
    // 列表加载
    this.loadData(this.taskParams, this.loadSuccess);
  }

  /**
   * trackBy 函数用于优化 ngFor 性能
   */
  trackByTaskCode(index: number, item: Task): string {
    return item.taskCode;
  }

  /**
   * 搜索
   */
  onSearch() {
    this.taskParams.pageNum = 1;
    this.loadData(this.taskParams, this.loadSuccess);
  }

  /**
   * 重置搜索条件
   */
  onReset() {
    this.taskParams = new TaskParams();
    this.loadData(this.taskParams, this.loadSuccess);
  }

  /**
   * 加载数据获取列表数据
   * @param params 请求参数
   * @param callback 成功回调函数
   */
  async loadData(params: any, callback: (arg0: any) => void): Promise<void> {
    this.isLoading = true;
    this.loading = await this.loadingCtrl.create({
      message: '数据加载中...',
    });
    await this.loading.present();

    this.workService.taskGrid(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe(callback, this.loadError);
  }

  /**
   * 数据加载成功处理函数
   * @param request 接口返回的结果
   */
  loadSuccess = (request: PageGridResult<any>) => {
    const { code, data } = request;
    if (code === 0) {
      const pagerData = data?.records || [];
      this.taskPageResult = request;
      this.taskGridData = pagerData.length > 0 ? pagerData : [];
    } else {
      this.taskGridData = [];
      this.taskPageResult = new PageGridResult();
    }

    this.closeRefresh();
  }

  /**
   * 数据加载失败处理函数
   * @param err 错误信息
   */
  loadError = (err: any) => {
    const msg = (err && (err.msg || (err.body && err.body.msg))) || '加载失败';
    this.closeRefresh();
    this.toastSer.presentToast(msg, 'danger');
  }

  /**
   * 关闭加载动画和下拉刷新
   */
  closeRefresh(): void {
    this.isLoading = false;
    this.loading?.dismiss();
    this.refreshEvent?.target.complete();
    // 完成上拉加载事件
    this.moreEvent?.target.complete();
  }


  /**
   * 下拉刷新
   */
  onRefresh(event) {
    this.refreshEvent = event;
    this.onReset();
  }

  /**
   * 上拉加载更多
   */
  loadMoreData(event): void {
    const { pageNum, pageSize, total } = this.taskPageResult.data;

    // 判断是否还有更多数据
    if (total > (pageNum * pageSize)) {
      this.moreEvent = event;
      this.taskParams.pageNum = pageNum + 1;
      this.loadData(this.taskParams, this.moreGridSuccess);
    } else {
      // 没有更多数据时显示提示
      this.spinnerType = null;
      this.isShowNoMoreStr = '到底啦~';
      setTimeout(() => {
        event.target.complete();
      }, 1000);
    }
  }

  /**
   * 加载更多数据成功处理
   * @param result 加载结果
   */
  moreGridSuccess = (result: PageGridResult<any>) => {
    this.taskPageResult = result;
    // 合并新加载的数据到现有列表中
    this.taskGridData = [...this.taskGridData, ...result.data.records];
    this.closeRefresh();
  }

  /**
   * 检测未上传数据
   */
  async checkData(): Promise<void> {
    try {
      this.onUploadLength = (await this.backgroundGeolocation.getValidLocations() as BackgroundGeolocationResponse[]).length;
      if (this.onUploadLength > 0) {
        // 将未发布到服务器的位置进行上传
        this.backgroundGeolocation.forceSync();
      }
    } catch (error) {
      console.error('检测未上传数据失败:', error);
      this.onUploadLength = 0;
    }
  }

  /**
   * 开始任务
   */
  onStartTask(event: any, item: Task): void {
    event.stopPropagation(); // 阻止事件冒泡
    this.onStartTrack(item);
  }

  // 开始执行
  onStartTrack(task: Task) {
    if (task.status === '执行中') {
      this.getTaskInfo(task);
    } else {
      this.openExecute(task);
    }
  }

  /**
   * 继续执行中任务前，先获取最新的任务信息
   * @param task 任务对象
   * @param centerPointCoords (可选) 预先计算好的中心点坐标
   */
  getTaskInfo(task: Task, centerPointCoords?: number[]) {
    // 保存原始 taskCode，避免对象引用问题
    const originalTaskCode = task.taskCode;
    this.exeSer.continueTasck({ taskCode: originalTaskCode }).subscribe((ret) => {
      if (ret.code === 0 && ret.data) {
        // 保留原始的 taskCode，不让 API 返回的数据覆盖
        const { taskCode: apiTaskCode, ...apiData } = ret.data;
        const newTaskInfo = {
          ...task,
          ...apiData,
          taskCode: originalTaskCode // 强制使用原始的 taskCode
        };
        // 调用 openExecute，并将预计算的中心点（如果有）传递下去
        this.openExecute(newTaskInfo, centerPointCoords);
      } else {
        // 如果 API 返回的数据为空，直接使用原始任务信息
        console.warn('getTaskInfo: API 返回数据为空，使用原始任务信息', ret);
        this.openExecute(task, centerPointCoords);
      }
    });
  }

  /**
   * 跳转到任务详情页面
   */
  async onWorkDetail(info: Task): Promise<void> {
    const centerPointCoords = this.calculateCenterPoint(info);

    const modal = await this.modalCtrl.create({
      component: TaskDetailComponent,
      componentProps: {
        centerPoint: centerPointCoords,
        modelInfo: info
      },
      cssClass: 'task-detail',
      swipeToClose: true,
    });
    await modal.present();

    const { data } = await modal.onDidDismiss();

    if (data === 'refresh') {
      this.onReset();
    }

    if (data === 'start_execution') {
      info.status === '执行中' ? this.getTaskInfo(info, centerPointCoords)
        : this.openExecute(info, centerPointCoords); // 如果是新任务，则直接执行
    }
  }

  async openExecute(task: Task, centerPointCoords?: number[]): Promise<void> {
    const finalCenterPointCoords = centerPointCoords || this.calculateCenterPoint(task);

    const modal = await this.modalCtrl.create({
      component: ExecutComponent,
      componentProps: {
        isModal: true, task,
        centerPoint: finalCenterPointCoords,
        executState: task.status === '执行中' ? 'continue' : 'create'
      },
      backdropDismiss: false
    });
    await modal.present();
    modal.onDidDismiss().then(() => {
      this.onReset();
    });
  }
  /**
   * 从任务中解析几何数据并计算中心点
   * @param task 任务对象
   * @returns 中心点坐标数组
   */
  private calculateCenterPoint(task: Task): number[] {
    // 1. 解析geom数据
    let geomData = JSON.parse(task.geom);
    // 2. 清理无效坐标
    geomData = this.cleanGeomCoordinates(geomData);
    // 3. 计算中心点
    const centerPoint = centroid(geomData);
    return centerPoint.geometry.coordinates;
  }

  /**
   * 清理几何数据中的无效坐标 (例如 [0, 0])
   */
  private cleanGeomCoordinates(geomData: any): any {
    if (geomData && geomData.coordinates && Array.isArray(geomData.coordinates)) {
      // 针对 Polygon 类型 [[...]]
      if (Array.isArray(geomData.coordinates[0]) && Array.isArray(geomData.coordinates[0][0])) {
        geomData.coordinates[0] = geomData.coordinates[0].filter((coord: number[]) => {
          return coord.length === 2 && coord[0] !== 0 && coord[1] !== 0;
        });
      }
      // 针对 LineString 或 MultiPoint 类型 [...]
      else if (Array.isArray(geomData.coordinates[0])) {
        geomData.coordinates = geomData.coordinates.filter((coord: number[]) => {
          return coord.length === 2 && coord[0] !== 0 && coord[1] !== 0;
        });
      }
    }
    return geomData;
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }

  getStatusColor(status: string): string {
    return statusColorMap[status] || 'medium';
  }

  getStatusIcon(status: string): string {
    return statusIconMap[status] || 'help-circle';
  }

  /**
   * 时间转换器
   */
  dateTransform(date: any, format: string): any {
    return this.datePipe.transform(date ? new Date(date) : new Date(), format);
  }
}
