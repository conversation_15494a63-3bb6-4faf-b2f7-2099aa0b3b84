import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DetailsMode } from 'src/app/@core/base/environment';

@Injectable()
export class OstFormItemService {
  formGroup: FormGroup;
  ostFormControlNames: string;
  modelMode: DetailsMode;
  required: boolean;
  constructor() { }
}


@Injectable()
export class OstFormService {
  formGroup: FormGroup;
  modelMode: DetailsMode;
  required: boolean;
  constructor() { }
}
