@import "../../../../../../theme/variables.scss";
// xs 超小的最小宽度
$ost-ctr-xs-width: 34px;
$ost-ctr-xs-height: 34px;
// sm 小的
$ost-ctr-sm-width: 38px;
$ost-ctr-sm-height: 38px;
// md 正常的
$ost-ctr-md-width: 42px;
$ost-ctr-md-height: 42px;
// lg 大的
$ost-ctr-lg-width: 46px;
$ost-ctr-lg-height: 46px;
// xl 更大
$ost-ctr-xl-width: 50px;
$ost-ctr-xl-height: 50px;
// xxl 更更大
$ost-ctr-xxl-width: 54px;
$ost-ctr-xxl-height: 54px;
// xxxl 更更更大
$ost-ctr-xxxl-width: 58px;
$ost-ctr-xxxl-height: 58px;

$ost-ctr-size-config: (
  xs: (
    height: $ost-ctr-xs-height,
    min-width: $ost-ctr-xs-width,
  ),
  sm: (
    height: $ost-ctr-sm-height,
    min-width: $ost-ctr-sm-width,
  ),
  md: (
    height: $ost-ctr-md-height,
    min-width: $ost-ctr-md-width,
  ),
  lg: (
    height: $ost-ctr-lg-height,
    min-width: $ost-ctr-lg-width,
  ),
  xl: (
    height: $ost-ctr-xl-height,
    min-width: $ost-ctr-xl-width,
  ),
  xxl: (
    height: $ost-ctr-xxl-height,
    min-width: $ost-ctr-xxl-width,
  ),
  xxxl: (
    height: $ost-ctr-xxxl-height,
    min-width: $ost-ctr-xxxl-width,
  ),
);

.controler-item {
  background-color: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  pointer-events: all;
  @each $type in xxxl, xxl, xl, lg, md, sm, xs {
    &.controler-item-#{$type} {
      @each $key, $value in map-get($ost-ctr-size-config, $type) {
        #{$key}: $value;
      }
    }
  }

  &.active {
    color: #fff;
    background-color: #5260ff;
  }
}
