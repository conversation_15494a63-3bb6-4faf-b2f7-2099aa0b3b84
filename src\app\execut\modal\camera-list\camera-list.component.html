<!-- 摄像头列表页面 -->
<ion-header>
  <ion-toolbar>
    <ion-searchbar 
      placeholder="根据摄像头名称搜索"
      (ionChange)="filterItems($event)"
      debounce="500"
      class="searchbar">
    </ion-searchbar>
    <ion-buttons slot="end">
      <ion-button (click)="close()">
        关闭
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content style="height: calc(60vh - 58px);">
  <ion-grid>
    <ion-row class="ion-text-center header">
      <ion-col>摄像头名称</ion-col>
      <ion-col>站点名称</ion-col>
      <ion-col class="operate-col">操作</ion-col>
    </ion-row>

    <ion-row *ngFor="let camera of allCameras; let i = index" class="ion-text-center row">
      <ion-col>{{ camera.name }}</ion-col>
      <ion-col>{{ camera.websiteName }}</ion-col>
      <ion-col class="operate-col">
        <div class="operate-btn" (click)="viewCameraDetails(camera)">
          <ion-icon name="eye-outline" color="primary"></ion-icon>
        </div>
      </ion-col>
    </ion-row>

  </ion-grid>
</ion-content>