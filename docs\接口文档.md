## 分页查看巡检关键点信息

- **请求地址**: /work-inspect/api/v2/inspect/app/point/msg/grid
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 雨天不巡 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": {
    "total": 424,
    "records": [
      {
        "pointCode": "POINT202507291706020LEGE",
        "bufferRange": 10,
        "pointName": "绿地-拐角",
        "geom": "{\"type\":\"Point\",\"coordinates\":[116.187820089,40.071670653]}",
        "stakeName": "2",
        "depName": "山西蓝焰控股股份有限公司",
        "isItRaining": "是",
        "depCode": "DEP20240416111542HDZUH",
        "stakeCode": "STAKE20241106095446KCN1R",
        "inspectionMethod": "巡查"
      }
    ],
    "pageSize": 10,
    "pageNum": 1
  }
}
```
---

## 查看巡检关键点信息列表(不分页)

- **请求地址**: /work-inspect/api/v2/inspect/app/point/msg/list
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 是否雨天 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": [
    {
      "pointCode": "POINT202507291706020LEGE",
      "bufferRange": 10,
      "pointName": "绿地-拐角",
      "geom": "{\"type\":\"Point\",\"coordinates\":[116.187820089,40.071670653]}",
      "stakeName": "2",
      "depName": "山西蓝焰控股股份有限公司",
      "isItRaining": "是",
      "depCode": "DEP20240416111542HDZUH",
      "stakeCode": "STAKE20241106095446KCN1R",
      "inspectionMethod": "巡查"
    }
  ]
}
```
---

## 查询未确认的关键点列表(不分页)

- **请求地址**: /work-inspect/api/v2/inspect/app/point/unconfirmed/list
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 是否雨天 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": [
    {
      "pointCode": "POINT202507241714091WZ9M",
      "bufferRange": 20,
      "pointName": "长输管线巡视-09",
      "stakeName": "067",
      "geom": "{\"type\":\"Point\",\"coordinates\":[112.58336454,35.61328132]}",
      "depName": "管道部",
      "isItRaining": "是",
      "depCode": "DEP202502061608527QRVN",
      "stakeCode": "STAKE20250519152355U8YD8",
      "inspectionMethod": "巡视"
    }
  ]
}
```
---

## 查询未确认的关键点个数

- **请求地址**: /work-inspect/api/v2/inspect/app/point/countUnconfirmedNo
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 是否雨天 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": "1"
}
```
---

## 关键点审批

- **请求地址**: /work-inspect/api/v2/inspect/app/point/ok
- **请求方法**: POST
- **请求头**: Content-Type: application/json
- **请求参数（JSON格式）**:
```json
{
  "isOk": "yes/no",
  "pointCode": "巡检单编码"
}
```

- **返回数据示例**:
```json
{
  "code": 0,
  "msg": "操作成功"
}
```



## 分页查看巡检关键点信息

- **请求地址**: /work-inspect/api/v2/inspect/app/point/msg/grid
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 是否雨天 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": {
    "total": 424,
    "records": [
      {
        "pointCode": "POINT202507291706020LEGE",
        "bufferRange": 10,
        "pointName": "绿地-拐角",
        "geom": "{\"type\":\"Point\",\"coordinates\":[116.187820089,40.071670653]}",
        "stakeName": "2",
        "depName": "山西蓝焰控股股份有限公司",
        "isItRaining": "是",
        "depCode": "DEP20240416111542HDZUH",
        "stakeCode": "STAKE20241106095446KCN1R",
        "inspectionMethod": "巡查"
      }
    ],
    "pageSize": 10,
    "pageNum": 1
  }
}
```
---

## 查看巡检关键点信息列表(不分页)

- **请求地址**: /work-inspect/api/v2/inspect/app/point/msg/list
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 是否雨天 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": [
    {
      "pointCode": "POINT202507291706020LEGE",
      "bufferRange": 10,
      "pointName": "绿地-拐角",
      "geom": "{\"type\":\"Point\",\"coordinates\":[116.187820089,40.071670653]}",
      "stakeName": "2",
      "depName": "山西蓝焰控股股份有限公司",
      "isItRaining": "是",
      "depCode": "DEP20240416111542HDZUH",
      "stakeCode": "STAKE20241106095446KCN1R",
      "inspectionMethod": "巡查"
    }
  ]
}
```
---

## 查询未确认的关键点列表(不分页)

- **请求地址**: /work-inspect/api/v2/inspect/app/point/unconfirmed/list
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 是否雨天 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": [
    {
      "pointCode": "POINT202507241714091WZ9M",
      "bufferRange": 20,
      "pointName": "长输管线巡视-09",
      "stakeName": "067",
      "geom": "{\"type\":\"Point\",\"coordinates\":[112.58336454,35.61328132]}",
      "depName": "管道部",
      "isItRaining": "是",
      "depCode": "DEP202502061608527QRVN",
      "stakeCode": "STAKE20250519152355U8YD8",
      "inspectionMethod": "巡视"
    }
  ]
}
```
---

## 查询未确认的关键点个数

- **请求地址**: /work-inspect/api/v2/inspect/app/point/countUnconfirmedNo
- **请求方法**: GET
- **请求头**: Content-Type: application/x-www-form-urlencoded
- **请求参数（表单形式）**:
  > 注：以下参数为搜索条件，均为非必填

| 参数名 | 类型 | 描述 | 可选值 |
|--------|------|------|--------|
| depCode | string | 部门编码 | - |
| pointName | string | 巡检点名称 | - |
| inspectionMethod | string | 巡检方式 | 巡视\|巡查 |
| isItRaining | string | 是否雨天 | 是\|否 |

- **返回数据示例**:
```json
{
  "msg": "success",
  "code": 0,
  "data": "1"
}
```
---

## 关键点审批

- **请求地址**: /work-inspect/api/v2/inspect/app/point/ok
- **请求方法**: PUT
- **请求头**: Content-Type: application/json
- **请求参数（JSON格式）**:
```json
{
  "isOk": "yes/no",
  "pointCode": "巡检单编码"
}
```

- **返回数据示例**:
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

## 关键点修改

- **请求地址**: /work-inspect/api/v2/inspect/app/point/msg
- **请求方法**: PUT
- **请求头**: Content-Type: application/json
- **请求参数（JSON格式）**:
```json
{
  "pointGeom": "空间坐标信息",
  "bufferRange": "缓冲范围",
  "pointCode": "关键点编码",
  "pointName": "关键点名称",
  "isItRaining": "雨天不巡",
  "inspectionMethod": "巡检方式(巡视/巡查)"
}
```

- **返回数据示例**:
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

## 关键点删除

- **请求地址**: /work-inspect/api/v2/inspect/app/point/msg
- **请求方法**: DELETE
- **请求头**: Content-Type: application/json
- **请求参数（JSON格式）**:
```json
{
  "pointCode": ["关键点编码数组"]
}
```

- **返回数据示例**:
```json
{
  "code": 0,
  "msg": "操作成功"
}
```