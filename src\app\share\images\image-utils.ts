// 图片处理相关工具函数

/**
 * 拼接base64前缀
 */
export function getBase64WithPrefix(imageData: string): string {
  // 如果已经有前缀则不再拼接
  if (imageData.startsWith('data:image/')) {
    return imageData;
  }
  return 'data:image/jpeg;base64,' + imageData;
}

/**
 * 压缩图片
 * @param base64 base64字符串
 * @returns 压缩后的base64字符串
 */
export function compressImage(base64: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = base64;
    img.onload = () => {
      let width = img.width;
      let height = img.height;
      if (width > 1920 || height > 1080) {
        const ratio = Math.min(1920 / width, 1080 / height);
        width *= ratio;
        height *= ratio;
      }
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);
      const compressedBase64 = canvas.toDataURL('image/jpeg', 0.8);
      resolve(compressedBase64);
    };
    img.onerror = reject;
  });
} 