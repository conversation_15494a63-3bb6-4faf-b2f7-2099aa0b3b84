import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { ShareModule } from "../share/share.module";
import { ControlBarComponent } from "./control-bar/control-bar.component";
import { BasicComponent } from "./modal/evreport/basic/basic.component";
import { EvreportComponent } from "./modal/evreport/evreport.component";
import { LogsComponent } from "./modal/evreport/logs/logs.component";
import { ExecutRoutingModule } from "./execut-routing.module";
import { ExecutComponent } from "./execut.component";
import { RingComponent } from "./ring/ring.component";
import { LogsDetailComponent } from "./modal/evreport/logs/detail/detail.component";
import { CurrentTime } from "../@core/base/environment";
import { EventDetailComponent } from "./modal/evreport/basic/event-detail/event-detail.component";
import { CameraListComponent } from "./modal/camera-list/camera-list.component";
import { ClockInComponent } from "./modal/clock-in/clock-in.component";
import { NotInspectedComponent } from "./modal/not-inspected/not-inspected.component";
import { ClockInModalComponent } from "./modal/clock-in-modal/clock-in-modal.component";


const COMP= [
  ExecutComponent,
  ControlBarComponent,
  RingComponent,
  EvreportComponent,    // 事件上报
  BasicComponent,       // 基础信息
  EventDetailComponent, // 事件详情
  LogsComponent,        // 节点信息
  LogsDetailComponent,  // 节点信息详情
  CameraListComponent,  // 摄像头列表
  ClockInComponent,     // 打卡
  NotInspectedComponent,// 巡检未完成上报
  ClockInModalComponent,// 打卡模态框（tab切换）
]

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    ShareModule,
    ExecutRoutingModule
  ],
  providers: [
    { provide: CurrentTime, useValue: '' }
  ],
  declarations: [...COMP],
  entryComponents: [...COMP],
  exports: [...COMP],
})
export class ExecutModule { }