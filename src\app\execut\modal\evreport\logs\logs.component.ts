import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Alert<PERSON>ontroller, ModalController, ToastController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DetailsMode } from 'src/app/@core/base/environment';
import { LogInfo } from '../../../class/evreport';
import { ExecutService } from '../../../execut.service';
import { LogsDetailComponent } from './detail/detail.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'evreport-logs',
  templateUrl: './logs.component.html',
  styleUrls: ['./logs.component.scss']
})
export class LogsComponent implements OnInit, OnDestroy {
  @Input() isModal = false;
  // 事件信息
  @Input() basicInfo: any;
  @Input() coordinate: any;
  // 数据模式
  @Input() modelMode: DetailsMode;
  DetailsMode: typeof DetailsMode = DetailsMode;
  logList: LogInfo[] = [];
  // 下拉刷新事件
  refreshEvent: any;
  // 用于取消所有订阅的信号
  private destroy$: Subject<void> = new Subject<void>();
  constructor(
    public modalController: ModalController,
    public alertController: AlertController,
    public netSer: ExecutService,
    public toastController: ToastController,
  ) { }

  ngOnInit() {
    this.loadList();
  }

  getFileUrl(fileUrl: string): string {
    return `${environment.production ? 'http' : 'http'}://${environment.api.ip}:${environment.api.port}/file/getV2/${fileUrl}`;
  }

  /**
   * 下拉刷新
   */
  doRefresh(event): void {
    this.refreshEvent = event;
    this.loadList();
  }

  // 关闭刷新
  closeRefresh(): void {
    if (this.refreshEvent) {
      this.refreshEvent.target.complete();
    }
  }

  loadList() {
    const eventCode = this.basicInfo ? this.basicInfo.eventCode : '';
    this.netSer.getLogList({ eventCode })
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        if (res.code === 0) {
          if (!!eventCode) {
            this.logList = res.data;
          }
        }
        this.closeRefresh();
      })
  }

  /**
   * 添加节点信息
   */
  onAddLogs() {
    if (this.basicInfo && !!this.basicInfo.eventCode) {
      this.openDetail(DetailsMode.ADD);
    } else {
      this.onToast('请先填写事件基本信息');
    }
  }

  /**
   * 查看详情
   * @param info 详情信息
   */
  showDetail(info) {
    this.netSer.getLogDetail({ scheduleCode: info.scheduleCode })
      .pipe(takeUntil(this.destroy$))
      .subscribe(res => {
        if (res.code === 0) {
          this.openDetail(DetailsMode.SEE, res.data);
        }
      })
  }

  /**
   * 打开详情
   * @param modelType 模式
   * @param info 详情信息
   */
  async openDetail(modelType: DetailsMode, info: LogInfo = new LogInfo()): Promise<void> {
    if (modelType === DetailsMode.ADD) {
      info.eventCode = this.basicInfo.eventCode;
      info.eventType = this.basicInfo.eventType;
    }
    const modal = await this.modalController.create({
      component: LogsDetailComponent,
      componentProps: {
        modelInfo: info, modelMode: modelType,
      },
      backdropDismiss: false
    });
    await modal.present();
    await modal.onDidDismiss().then((state) => {
      // 保存后刷新列表
      if (state.data) {
        this.loadList()
      }
    });
  }

  async onDelete(items: LogInfo): Promise<void> {
    const alert = await this.alertController.create({
      cssClass: 'my-custom-class',
      header: '警告!',
      message: '你确定删除此条数据吗？',
      buttons: [
        {
          text: '取消',
          role: 'cancel',
          cssClass: 'secondary',
        }, {
          text: '确定',
          cssClass: 'Okay',
          handler: () => {
            const id: any | string[] = items.scheduleCode;
            const datas = id instanceof Array ? id : [id];
            this.netSer.deleteLog(datas)
              .pipe(takeUntil(this.destroy$))
              .subscribe(this.onDeleteSuccess, this.onDeleteErroe);
          }
        }
      ]
    });
    await alert.present();
  }

  /**
   * 删除成功
   */
  onDeleteSuccess = () => {
    this.loadList();
    this.onToast('数据删除成功！', 'success');
  }

  /**
   * 删除成功
   */
  onDeleteErroe = (err) => {
    this.onToast(err.body.message);
  }

  /**
   * 消息提醒
   */
  async onToast(mgs = '出现错误请重试！', color = 'danger'): Promise<void> {
    const toast = await this.toastController.create({
      message: mgs,
      duration: 2000,
      color
    });
    toast.present();
  }

  ngOnDestroy(): void {
    this.destroy$.next(); // 发送完成信号
    this.destroy$.complete(); // 标记Subject完成
  }
}
