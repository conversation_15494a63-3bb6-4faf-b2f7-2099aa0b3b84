import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { IControlerItemSize } from './controler-item/controler-item.component';

@Component({
  selector: 'ost-controlers-group',
  templateUrl: './controler-group.component.html',
  styleUrls: ['./controler-group.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MapControlersGroupComponent {
  // 布局
  @Input() layout: 'horizontal' | 'vertical' = 'vertical';
  // 大小
  @Input() size: IControlerItemSize = 'sm';
  constructor() { }

}
