import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { map } from 'rxjs/operators';
import { RequestResult } from 'src/app/@core/base/request-result';
import { OstTreeListComponent } from '../../ost-tree-list/ost-tree-list.component';
import { OstTreeListItem } from '../../ost-tree-list/ost-tree-list.service';
import { ShareModuleService } from '../../share.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'ost-tree-menu',
  templateUrl: './tree-menu.component.html',
  styleUrls: ['./tree-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TreeMenuComponent implements OnInit, OnDestroy {
  @ViewChild('menu', { static: false }) menu: OstTreeListComponent;
  @Input() interfaceUrl: string;
  @Input() labelName: string;

  @Output() toggleSubMenu = new EventEmitter<any>();
  @Output() itemClick = new EventEmitter<any>();


  items: OstTreeListItem[] = [];

  private destroy$ = new Subject<void>();
  constructor(
    public cd: ChangeDetectorRef,
    public netSer: ShareModuleService,
  ) { }

  ngOnInit(): void {
    this.loadMenuTree();
  }


  /**
   * 设置选中的菜单项
   */
  setSelectItemById(id: string): void {
    // 根据id查找对应的菜单项
    const selectItem = this.items.find(itme => (itme.data.pipelineCode === id));
    if (selectItem) {
      selectItem.expanded = true;
      this.menu.setSelectItem(selectItem);
      this.cd.markForCheck();
    }
  }

  /**
   * 设置选中的菜单项
   */
  setSelectItem(item: OstTreeListItem): void {
    this.menu.setSelectItem(item);
  }

  /**
   * 切换子菜单
   */
  onToggleSubMenu(item: OstTreeListItem): void {
    this.toggleSubMenu.emit(item);
  }

  /**
   * 点击菜单项
   */
  onItemClick(item: OstTreeListItem): void {
    this.itemClick.emit(item);
  }

  /**
   * 获取数据
   */
  loadMenuTree(): void {
    this.netSer.getRequest({ interfaceUrl: this.interfaceUrl })
      .pipe(map((data: RequestResult<any[]>) => this.transformation(data)))
      .subscribe(res => {
        this.items = res;
        this.cd.markForCheck();
      }, error => {
        this.cd.markForCheck();
      });
  }

  /**
   * 数据转换
   */
  transformation(res: any): OstTreeListItem[] {
    const data = res.data || res;
    return this.transformPipelineData(data);
  }

  /**
   * 将管道数据转换为树形结构
   */
  private transformPipelineData(data: any[]): OstTreeListItem[] {
    if (!data || !data.length) {
      return [];
    }

    const convertToTreeItem = (item: any): OstTreeListItem => {
      const treeItem: OstTreeListItem = {
        title: item[this.labelName],
        data: item
      };

      if (item.children && item.children.length) {
        treeItem.children = item.children.map(child => convertToTreeItem(child));
      }

      return treeItem;
    };

    return data.map(item => convertToTreeItem(item));
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
