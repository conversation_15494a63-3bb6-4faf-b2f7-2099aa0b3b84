import { PagingModule } from 'src/app/@core/base/request-result';

/**
 * 任务
 */
export class Task {
    createTime: string;
    taskCode: string = '';
    // 任务名称
    taskName: string;
    startTime: string;
    endTime: string;
    // 完成率
    completionRate: string;
    planCode: string;
    planName: string;
    // 部门编码
    depCode: string;
    depName: string;
    // 巡检状态  未执行、执行中、已完成、超时未完成
    status: string;
    // 范围坐标信息
    geom: any;
    groupCode: string;
    // 巡检方式
    inspectionMethod: string;
    // 是否雨天
    isItRaining: string;
}
/**
 * 任务-查询条件
 */
export class TaskParams extends PagingModule<Task> {
    taskName = '';
}

/**
 * 任务状态颜色映射
 */
export const statusColorMap: { [key: string]: string } = {
    '未执行': 'warning',
    '执行中': 'primary',
    '执行完成': 'success',
    '超时未完成': 'danger'
};

/**
 * 任务状态图标映射
 */
export const statusIconMap: { [key: string]: string } = {
    '未执行': 'time',
    '执行中': 'pie-chart',
    '执行完成': 'checkmark-circle',
    '超时未完成': 'alert-circle-outline'
};