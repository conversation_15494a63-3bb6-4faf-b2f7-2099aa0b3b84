<!-- 工作 -->
<ion-header [translucent]="true" class="work-header">
  <div color="primary" class="search-bar">
    <div class="search-bar-parent">
      <ion-icon class="login-form-input-icon" name="search-outline"></ion-icon>
      <ion-input clearInput placeholder="请输入关键字" [(ngModel)]="taskParams.taskName">
      </ion-input>
    </div>
    <ion-note slot="end" class="title-end-operate" style="padding-right: 60px;" (click)="onReset()">
      重置
    </ion-note>
    <ion-note slot="end" class="title-end-operate" style="padding-right:10px;" (click)="onSearch()">
      搜索
    </ion-note>
  </div>
</ion-header>

<ion-content>
  <!-- 下拉刷新 -->
  <ion-refresher slot="fixed" (ionRefresh)="onRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-grid *ngIf="taskGridData.length > 0 || isLoading; else noData">
    <ng-container *ngFor="let item of taskGridData; let i= index; trackBy: trackByTaskCode">
      <ion-row class="split-line">
        <!-- 标题和状态区域容器 -->
        <div class="title-status-container">
          <!-- 标题 -->
          <div class="title-wrapper">
            <span class="title">
              <p class="task-p"></p>
              {{item.taskName}}
            </span>
          </div>
          <!-- 状态 -->
          <div *ngIf="item.status" class="status-section" [ngStyle]="{'width': item.status === '超时未完成' || item.status === '执行完成' ? '114px' : '88px'}">
            <ion-chip [color]="getStatusColor(item.status)" class="status-chip">
              <ion-icon [name]="getStatusIcon(item.status)"></ion-icon>
              <ion-label>{{item.status}}</ion-label>
            </ion-chip>
          </div>
        </div>
        <!-- 内容 -->
        <ion-col [size]="12" class="content"  (click)="onWorkDetail(item)">
          <span>计划名称&nbsp;:&nbsp;{{item.planName}}</span>
          <span>巡检方式&nbsp;:&nbsp;{{item.inspectionMethod}}</span>
          <span>巡检完成率&nbsp;:&nbsp;{{item.completionRate}}</span>
          <span>任务开始时间&nbsp;:&nbsp;{{item.startTime}}</span>
          <span>任务结束时间&nbsp;:&nbsp;{{item.endTime}}</span>
          <div class="task-start">
            <!-- 任务执行按钮 -->
            <ng-container *ngIf="['执行中','未执行'].includes(item.status) && item.completionRate!=='100'">
              <ion-button size="small" color="primary"
              (click)="onStartTask($event, item)"
              >
                {{item.status === '未执行' ? '开始任务' : '继续任务'}}
              </ion-button>
            </ng-container>
          </div>
        </ion-col>
      </ion-row>
    </ng-container>
  </ion-grid>

  <!-- 上拉加载更多 -->
  <ion-infinite-scroll threshold="40px" class="infinite-scroll" 
    *ngIf="taskGridData.length > 0;"
    (ionInfinite)="loadMoreData($event)">
    <ion-infinite-scroll-content
      style="margin-top: 16px;"
      [loadingSpinner]="spinnerType" 
      [loadingText]="isShowNoMoreStr">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll>

</ion-content>

<!-- 暂无数据 -->
<ng-template #noData>
  <div class="no-data">
    <img src="/assets/menu/box2.png" style="padding-top: 50px;" />
    <span class="no-data-span">暂无数据</span>
  </div>
</ng-template>