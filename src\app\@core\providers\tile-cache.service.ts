import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { flatMap } from 'rxjs/operators';
import { StorageService } from './storage.service';

@Injectable({
    providedIn: 'root'
})
export class TileCacheService {

    constructor(private http: HttpClient, private storageService: StorageService) { }

    downloadTile(url: string, key: string): Observable<any> {
        return this.http.get(url, { responseType: 'blob' }).pipe(
            flatMap(blob => this.storageService.set(key, blob))
        );
    }

    getTile(key: string): Observable<Blob> {
        return this.storageService.get(key);
    }
}
