@import "../../../theme/variables.scss";

::ng-deep .ion-page {
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: inline-block;
  position: absolute;
  flex-direction: column;
  justify-content: space-between;
  contain: layout size style;
  overflow: hidden;
  z-index: 0;
}

.login-bg {
  height: 100vh;
  overflow: hidden;
  margin: 0px;
  padding: 0px;
  background-image: url("../../..//assets/login/bigBg.jpg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.bgCircle2 {
  width: 55%;
  position: fixed;
  top: 7%;
  left: 23%;
  animation: just-rotate 36s linear infinite;
}

.bgCircle3 {
  width: 50%;
  position: fixed;
  top: 8%;
  left: 27%;
  animation: back-rotate 36s linear infinite;
}

.bgCircle4 {
  width: 60%;
  position: fixed;
  top: 5.3%;
  left: 20.2%;
  animation: just-rotate 36s linear infinite;
}

@keyframes just-rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes back-rotate {
  from {
    transform: rotate(360deg);
  }

  to {
    transform: rotate(0deg);
  }
}


.login-form {
  .login-form-subtitle {
    color: #ffffff;
    font-size: 16px;
    margin: 120px 0 20px 0;
  }

  .login-form-subtitle-small {
    color: #ffffff;
    font-size: 16px;
  }

  top: 0px;
  height: 100%;
  width: 100%;
  position:fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .login-form-input {
    color: black;
    width: 80%;
    height: 40px;
    margin: 10px;
    padding: 0 15px;
    font-size: 16px;
    // background-color: #e6e6e6;
    background-color: #ffffff;
    border-radius: 3px;
    border: 0px solid #35bef2;
    filter: Alpha(opacity=80);
    opacity: 0.8;

    ::-webkit-input-placeholder {
      color: #ffffff;
      // color: #666666;
    }

    display: flex;
    align-items: center;

    .login-form-input-icon {
      padding-right: 8px;
      text-align: center;
      height: 50%;
      border-right: 1px solid #666666;
    }

    ion-input {
      padding-left: 6px;
    }

    .error {
      font-size: 12px;
      color: #C7000B;
    }
  }

  .login-form-foot {
    padding-top: 60px;
    width: 80%;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .register {
      padding-top: 20px;
    }
  }

  .login-form-foot-small {
    width: 80%;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .register {
      padding-top: 20px;
    }
  }

  .login-form-assist {
    width: 80%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
    font-size: 14px;
    font-weight: normal;
  }
}