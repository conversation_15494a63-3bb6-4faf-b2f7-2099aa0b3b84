import { AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';

@Component({
  selector: 'ost-ring',
  templateUrl: './ring.component.html',
  styleUrls: ['./ring.component.scss']
})
export class RingComponent implements OnInit, AfterViewInit, OnChanges {
  @ViewChild('canvasDom', { static: false }) canvasDom: ElementRef<HTMLCanvasElement>;
  @Input() width = 130;
  @Input() height = 130;
  @Input() rate = 0;
  lineWidth = 10;
  radius = 60;
  context: CanvasRenderingContext2D;

  constructor() { }

  ngOnInit(): void {

  }

  ngAfterViewInit(): void {
    this.drawArc();
  }

  drawArc(): void {
    // 设置宽高
    this.context = this.canvasDom.nativeElement.getContext('2d');
    this.canvasDom.nativeElement.width = this.width;
    this.canvasDom.nativeElement.height = this.height;
    // 外层圆环
    this.context.beginPath();
    this.context.strokeStyle = '#ebebeb';  // 颜色
    this.context.arc(this.width / 2, this.height / 2, this.radius, (Math.PI / 180) * 270, (Math.PI / 180) * - 90, false);
    this.context.lineWidth = this.lineWidth;
    this.context.closePath();
    this.context.stroke();

  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.context) {
      if (this.rate === 0) {
        this.context.clearRect(0, 0, this.width, this.height);
        this.drawArc();
      }
      // 内层圆环
      this.context.beginPath();
      // 设置渐变色
      const gradient = this.context.createLinearGradient(0, 20, 0, 120);
      gradient.addColorStop(0, '#ffaa03');
      gradient.addColorStop(1.0, '#ff4514');
      this.context.strokeStyle = gradient;
      this.context.lineCap = 'round';  // 使其形状为圆弧
      this.context.arc(this.width / 2, this.height / 2, this.radius, (Math.PI / 180) * 270, (Math.PI / 180) * (270 + (this.rate / 100 * 360)), false);
      this.context.lineWidth = this.lineWidth; // 圆环的宽度
      this.context.stroke();
      this.context.closePath();
    }
  }


}
